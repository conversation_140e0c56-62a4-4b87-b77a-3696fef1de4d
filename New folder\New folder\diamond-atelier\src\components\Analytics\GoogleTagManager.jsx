"use client";
import { useEffect, useState } from 'react';

const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID || 'GTM-XXXXXXX';

const GoogleTagManager = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    if (typeof window !== 'undefined' && GTM_ID) {
      // Initialize Google Tag Manager
      (function(w,d,s,l,i){
        w[l]=w[l]||[];
        w[l].push({'gtm.start': new Date().getTime(), event:'gtm.js'});
        var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
        j.async=true;
        j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
        f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer',GTM_ID);
    }
  }, []);

  // Only render on client side to avoid hydration mismatch
  if (!isClient || !GTM_ID) {
    return null;
  }

  return (
    <div style={{ display: 'none' }}>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
        height="0"
        width="0"
        style={{ display: 'none', visibility: 'hidden' }}
      />
    </div>
  );
};

// GTM Event Tracking Functions
export const gtmTrack = {
  // Page view tracking
  pageView: (pageName, pageCategory) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'page_view',
        page_name: pageName,
        page_category: pageCategory,
        page_location: window.location.href,
        page_title: document.title
      });
    }
  },

  // Diamond specific events
  diamondView: (diamondData) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'diamond_view',
        diamond_id: diamondData.id,
        diamond_shape: diamondData.shape,
        diamond_carat: diamondData.carat,
        diamond_color: diamondData.color,
        diamond_clarity: diamondData.clarity,
        diamond_cut: diamondData.cut,
        diamond_price: diamondData.price
      });
    }
  },

  // Contact form events
  contactForm: (formData) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'contact_form_submit',
        form_type: formData.type,
        form_location: formData.location,
        user_email: formData.email,
        user_phone: formData.phone
      });
    }
  },

  // Search events
  search: (searchData) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'search',
        search_term: searchData.term,
        search_category: searchData.category,
        search_results: searchData.results
      });
    }
  },

  // Education events
  educationInteraction: (section, action) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'education_interaction',
        education_section: section,
        interaction_type: action,
        page_location: window.location.href
      });
    }
  },

  // Download events
  download: (fileName, fileType) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'file_download',
        file_name: fileName,
        file_type: fileType,
        download_location: window.location.href
      });
    }
  },

  // External link clicks
  externalLink: (url, linkText) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'external_link_click',
        link_url: url,
        link_text: linkText,
        source_page: window.location.href
      });
    }
  },

  // Video interactions
  videoInteraction: (videoTitle, action, progress) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'video_interaction',
        video_title: videoTitle,
        video_action: action,
        video_progress: progress,
        page_location: window.location.href
      });
    }
  }
};

export default GoogleTagManager;
