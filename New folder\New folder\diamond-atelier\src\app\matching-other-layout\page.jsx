import React from "react";
import ClientCalibratedLayoutsPage from "./ClientCalibratedLayoutsPage";

export const metadata = {
  title: "Calibrated Layouts - Diamond Atelier | Precision Diamond Matching",
  description: "Explore Diamond Atelier's calibrated layouts for precision diamond matching. Professional diamond layout services for jewelry manufacturers and designers.",
  keywords: [
    "calibrated layouts",
    "diamond matching",
    "precision layouts",
    "diamond arrangement",
    "jewelry design",
    "diamond atelier layouts",
    "professional diamond services"
  ],
  openGraph: {
    title: "Calibrated Layouts - Diamond Atelier | Precision Diamond Matching",
    description: "Explore Diamond Atelier's calibrated layouts for precision diamond matching. Professional diamond layout services for jewelry manufacturers and designers.",
    images: [
      {
        url: '/images/calibrated-layouts-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Calibrated Layouts - Diamond Atelier',
      },
    ],
  },
};

// Server-side data preparation
async function getCalibratedLayoutsData() {
  const diamondShapes = [
    {
      id: 1,
      shape: "Emerald",
      variants: [
        { size: "4X2 mm", pointer: "12 pointer", image: "/images/education/2x4Emerald.png" },
        { size: "4X3 mm", pointer: "20 pointer", image: "/images/education/4 x 3 - Emerald.png" },
        { size: "5X3 mm", pointer: "30 pointer", image: "/images/education/5 x 3 - Emerald.png" }
      ]
    },
    {
      id: 2,
      shape: "Oval",
      variants: [
        { size: "4X3 mm", pointer: "20 pointer", image: "/images/education/4 x 3 - Oval.png" },
        { size: "5X3 mm", pointer: "15 pointer", image: "/images/education/5 x 3 Oval.png" }
      ]
    },
    {
      id: 3,
      shape: "Marquise",
      variants: [
        { size: "4X2 mm", pointer: "10 Cent", image: "/images/education/4 x 2 - Marquise.png" },
        { size: "5X2.50 mm", pointer: "14 Cent", image: "/images/education/5 x 2-50 - Marquise.png" },
        { size: "6X3 mm", pointer: "25 Cent", image: "/images/education/6 x 3 Marquise.png" }
      ]
    },
    {
      id: 4,
      shape: "Pear",
      variants: [
        { size: "3X2 mm", pointer: "13 Cent", image: "/images/education/3 x 2 Pear.png" },
        { size: "4X2.50 mm", pointer: "18 Cent", image: "/images/education/4 x 2-50 - Pear.png" },
        { size: "5X3 mm", pointer: "25 Cent", image: "/images/education/5 x 3 - Pear.png" },
        { size: "6X4 mm", pointer: "50 Cent", image: "/images/education/6 x 4 - Pear.png" }
      ]
    }
  ];

  const heroImage = "/images/education/ml2.jpg";

  return {
    diamondShapes,

  };
}

// Server-side component
export default async function Page() {
  // Get data on server
  const { diamondShapes } = await getCalibratedLayoutsData();

  return (
    <ClientCalibratedLayoutsPage 
      diamondShapes={diamondShapes}
  
    />
  );
}
