"use client";
import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

/**
 * OptimizedImage Component
 * 
 * A wrapper around Next.js Image that provides:
 * - Lazy loading with intersection observer
 * - WebP/AVIF format detection and fallback
 * - Loading states and error handling
 * - Responsive image sizing
 * - Performance optimizations for static export
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  style,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority); // Load immediately if priority
  const imgRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before entering viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);



  const handleLoad = (e) => {
    console.log('Image loaded successfully:', src);
    setIsLoaded(true);
    onLoad?.(e);
  };

  const handleError = (e) => {
    console.error('Image failed to load:', src, e);
    setHasError(true);
    onError?.(e);
  };

  // Placeholder while loading
  const LoadingPlaceholder = () => (
    <div 
      className={`bg-gray-800 animate-pulse flex items-center justify-center ${className}`}
      style={{ width, height, ...style }}
    >
      <div className="text-gray-400 text-sm">Loading...</div>
    </div>
  );

  // Error fallback
  const ErrorFallback = () => (
    <div 
      className={`bg-gray-900 flex items-center justify-center ${className}`}
      style={{ width, height, ...style }}
    >
      <div className="text-gray-500 text-sm">Image failed to load</div>
    </div>
  );

  if (hasError) {
    return <ErrorFallback />;
  }

  return (
    <div ref={imgRef} className={`relative ${className}`}>
      {!isInView && !priority ? (
        <LoadingPlaceholder />
      ) : (
        <>
          {/* Show loading placeholder until image loads */}
          {!isLoaded && <LoadingPlaceholder />}
          
          {/* Actual image */}
          <Image
            src={src}
            alt={alt}
            {...(fill ? { fill: true } : { width, height })}
            sizes={sizes}
            quality={quality}
            priority={priority}
            placeholder={blurDataURL ? placeholder : 'empty'}
            blurDataURL={blurDataURL}
            style={{
              ...style,
              opacity: isLoaded ? 1 : 0,
              transition: 'opacity 0.3s ease-in-out'
            }}
            className={className}
            onLoad={handleLoad}
            onError={handleError}
            {...props}
          />
        </>
      )}
    </div>
  );
};

export default OptimizedImage;

// Utility function to generate blur data URL for placeholder
export const generateBlurDataURL = () => {
  // Simple base64 encoded 1x1 gray pixel - works without canvas
  return 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';
};

// Hook for responsive image sizes
export const useResponsiveImageSizes = () => {
  return "(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw";
};
