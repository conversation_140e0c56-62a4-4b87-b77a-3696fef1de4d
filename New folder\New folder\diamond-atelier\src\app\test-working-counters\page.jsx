"use client";
import React from 'react';
import WorkingCounter from '@/components/WorkingCounter';

export default function TestWorkingCounters() {
  return (
    <div style={{ 
      minHeight: '200vh', 
      backgroundColor: '#000', 
      color: '#fff', 
      padding: '50px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ fontSize: '2rem', marginBottom: '50px', textAlign: 'center' }}>
        Working Counter Test Page
      </h1>
      
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: '150px',
        alignItems: 'center',
        marginTop: '100vh' // Force scroll to test intersection
      }}>
        
        <div style={{ textAlign: 'center', padding: '50px', border: '2px solid #333' }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '20px' }}>Hero Counter Test</h2>
          <div style={{ fontSize: '4rem', fontWeight: 'bold', color: '#fff' }}>
            <WorkingCounter 
              targetNumber={10000} 
              suffix="+ CERTIFIED STONES" 
              duration={2000}
            />
          </div>
        </div>

        <div style={{ textAlign: 'center', padding: '50px', border: '2px solid #333' }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '20px' }}>Shapes Counter Test</h2>
          <div style={{ fontSize: '3rem', fontWeight: 'bold', color: '#fff' }}>
            <WorkingCounter
              targetNumber={100}
              suffix="+ SHAPES"
              duration={1200}
            />
          </div>
        </div>

        <div style={{ textAlign: 'center', padding: '50px', border: '2px solid #333' }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '20px' }}>Colors Counter Test</h2>
          <div style={{ fontSize: '3rem', fontWeight: 'bold', color: '#fff' }}>
            <WorkingCounter
              targetNumber={30}
              suffix="+ COLORS"
              duration={800}
            />
          </div>
        </div>

        <div style={{ textAlign: 'center', padding: '50px', border: '2px solid #333' }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '20px' }}>Certified Counter Test</h2>
          <div style={{ fontSize: '3rem', fontWeight: 'bold', color: '#fff' }}>
            <WorkingCounter
              targetNumber={10000}
              suffix="+ CERTIFIED"
              duration={1800}
            />
          </div>
        </div>

        <div style={{ 
          marginTop: '100px', 
          padding: '30px', 
          border: '1px solid #333',
          borderRadius: '10px',
          textAlign: 'center',
          backgroundColor: '#111'
        }}>
          <h3 style={{ color: '#fff', marginBottom: '20px' }}>Testing Instructions:</h3>
          <div style={{ color: '#ccc', lineHeight: '1.6' }}>
            <p>✅ Scroll down to see each counter animate</p>
            <p>✅ Each counter should count from 0 to target</p>
            <p>✅ Animation should be smooth</p>
            <p>✅ Check browser console for any errors</p>
            <p>✅ Test on different screen sizes</p>
          </div>
        </div>

      </div>
    </div>
  );
}
