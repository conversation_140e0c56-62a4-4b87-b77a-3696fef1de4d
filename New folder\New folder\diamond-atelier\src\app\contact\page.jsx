import React from "react";
import background from "../../../public/images/banner/contact.png";
import {countryCodes} from "@/utils/countrycode";
import ClientContactPage from "./ClientContactPage";

export const metadata = {
  title: "Contact Diamond Atelier - Get in Touch with Diamond Experts",
  description: "Contact Diamond Atelier for premium lab-grown diamonds. Get expert consultation, quotes, and personalized service. Reach out to India's leading diamond manufacturer.",
  keywords: [
    "contact diamond atelier",
    "diamond consultation",
    "diamond quotes",
    "lab grown diamond inquiry",
    "diamond manufacturer contact",
    "diamond expert consultation"
  ],
  openGraph: {
    title: "Contact Diamond Atelier - Get in Touch with Diamond Experts",
    description: "Contact Diamond Atelier for premium lab-grown diamonds. Get expert consultation, quotes, and personalized service.",
    images: [
      {
        url: '/images/contact-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Contact Diamond Atelier',
      },
    ],
  },
};

// Server-side data preparation
async function getContactPageData() {
  return {
    background,
    countryCodes
  };
}

// Server-side component
export default async function Page() {
  // Get data on server
  const { background, countryCodes } = await getContactPageData();

  return (
    <ClientContactPage 
      background={background}
      countryCodes={countryCodes}
    />
  );
}
