
// "use client";
// import React, { useState } from "react";
// import { ChevronDown } from "lucide-react";
// import rough from "../../../public/images/about/rough.png";
// import seed from "../../../public/images/about/seed.png";
// import Image from "next/image";
// import diamond from "../../../public/images/education/Bitmap.png";
// import price from "../../../public/images/about/Difference - Lab Grown-Price-.png";
// import tree from "../../../public/images/about/Difference - Lab Grown-Sustainability-.png";
// import value from "../../../public/images/about/Difference - Lab Grown-Value-.png";
// import future from "../../../public/images/about/FutureIcon.png";

// const DifferenceBetweenDiamonds = () => {
//   const [activeCard, setActiveCard] = useState(null);

//   const handleCardClick = (cardId) => {
//     setActiveCard(activeCard === cardId ? null : cardId);
//   };

//   const advantages = [
//     {
//       id: 1,
//       icon: <Image src={price} alt="Price Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
//       title: "Exceptional Value",
//       subtitle: "90% Less Cost",
//       description: "Lab diamonds deliver the same brilliance at a fraction of the price",
//       details: {
//         type: "comparison",
//         content: (
//           <div className="grid grid-cols-2 gap-6 text-center text-sm md:text-base xl:text-lg">
//             <div className="space-y-2">
//               <Image src={diamond} alt="Natural Diamond" className="mx-auto w-20 md:w-24 xl:w-28" />
//               <p className="font-semibold">Natural Diamond</p>
//               <p className="text-lg md:text-xl xl:text-2xl font-bold text-red-400">$10,000</p>
//             </div>
//             <div className="space-y-2">
//               <Image src={diamond} alt="Lab Diamond" className="mx-auto w-20 md:w-24 xl:w-28" />
//               <p className="font-semibold">Lab Diamond....</p>
//               <p className="text-lg md:text-xl xl:text-2xl font-bold text-emerald-400">$1,000</p>
//             </div>
//           </div>
//         ),
//       },
//     },
//     {
//       id: 2,
//       icon: <Image src={tree} alt="Value Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
//       title: "Certified Quality",
//       subtitle: "Industry Standard",
//       description: "Recognized and certified by leading gemological institutes worldwide",
//       details: {
//         type: "text",
//         content:
//           "Lab diamonds maintain identical chemical, physical, and optical properties to mined diamonds, ensuring lasting value and beauty.",
//       },
//     },
//     {
//       id: 3,
//       icon: <Image src={value} alt="Sustainability Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
//       title: "Zero Environmental Impact",
//       subtitle: "Sustainable Choice",
//       description: "Clean creation process with no mining, no waste, no environmental damage",
//       details: {
//         type: "text",
//         content:
//           "Traditional mining disturbs 100 sq ft of earth and creates 6,000 lbs of waste per carat. Lab diamonds eliminate this entirely.",
//       },
//     },
//     {
//       id: 4,
//       icon: <Image src={future} alt="Future Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
//       title: "Future Forward",
//       subtitle: "Growing Market",
//       description: "Leading the diamond industry evolution with 22% projected growth",
//       details: {
//         type: "text",
//         content:
//           "The lab diamond market is forecasted to grow 22% annually from 2021-2026, representing the future of the diamond industry.",
//       },
//     },
//   ];

//   const comparisonTables = [
//     {
//       title: "Industrial Advantages",
//       headers: ["Feature", "Earth Mined", "Lab Grown"],
//       rows: [
//         ["Origin Guaranteed", "❌", "✅"],
//         ["Security of Supply", "❌", "✅"],
//         ["Future Growth Security", "❌", "✅"],
//         ["Employment Security", "❌", "✅"],
//       ],
//     },
//     {
//       title: "Technical Properties",
//       headers: ["Property", "Earth Mined", "Lab Grown", "Cubic Zirconia", "Moissanite"],
//       rows: [
//         ["Composition", "C", "C", "ZrO₂", "SiC"],
//         ["Crystal Structure", "Cubic", "Cubic", "Cubic", "Hexagonal"],
//         ["Refractive Index", "2.42", "2.42", "2.2", "2.7"],
//         ["Hardness (Mohs)", "10", "10", "8.25", "9.25"],
//         ["Thermal Conductivity", "Excellent", "Excellent", "Poor", "High"],
//         ["Purity Level", "Only 2% Type IIa", "100% Type IIa", "N/A", "N/A"],
//         ["Measured In", "Carats", "Carats", "Carats", "Carats"],
//         ["Blue Fluorescence", "Strong to None", "None", "None", "None"],
//         ["Phosphorescence", "No", "Some", "NA", "NA"],
//         ["Color", "Even", "Even", "NA", "NA"],
//       ],
//     },
//   ];

//   return (
//     <div className="min-h-screen bg-black text-white">

//       <section className="relative py-20 md:py-28 xl:py-36 px-4 sm:px-8 md:px-16 xl:px-24 2xl:px-48 text-center">
//         <div className="relative z-10 max-w-4xl md:max-w-5xl xl:max-w-6xl mx-auto mt-10">
//           <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl 3xl:text-[10rem] 4k:text-[12rem] font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400 bg-clip-text text-transparent mb-6 leading-tight">
//             Lab vs Natural Diamonds
//           </h1>
//           <p className="text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl 3xl:text-5xl 4k:text-6xl text-gray-300 mb-12 leading-relaxed max-w-3xl lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl mx-auto">
//             Discover the difference between lab-grown and earth-mined diamonds
//           </p>

//           <div className="flex flex-col md:flex-row items-center justify-center gap-12 md:gap-20 mt-12">

//             <div className="group cursor-pointer">
//               <div className="relative">
//                 <div className="absolute -inset-2 bg-gradient-to-r from-amber-400 to-orange-600 rounded-full opacity-20 group-hover:opacity-40 blur-md"></div>
//                 <Image
//                   src={rough}
//                   alt="diamonds"
//                   width={200}
//                   height={200}
//                   className="w-24 sm:w-28 md:w-32 lg:w-36 xl:w-44 2xl:w-56 3xl:w-64 4k:w-80 h-24 sm:h-28 md:h-32 lg:h-36 xl:h-44 2xl:h-56 3xl:h-64 4k:h-80 bg-gradient-to-br from-amber-400 to-white rounded-full shadow-2xl group-hover:scale-110 transition-transform duration-300 object-cover"
//                 />
//               </div>
//               <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl 3xl:text-5xl 4k:text-6xl font-semibold mt-4 text-amber-400">
//                 Earth Mined
//               </h3>
//               <p className="text-xs sm:text-sm md:text-base xl:text-lg text-gray-400">Rough Stone</p>
//             </div>

       
//             <div className="hidden md:block">
//               <div className="text-5xl sm:text-6xl md:text-7xl xl:text-8xl 2xl:text-9xl font-thin text-gray-600">
//                 VS
//               </div>
//             </div>

//             <div className="group cursor-pointer">
//               <div className="relative">
//                 <Image
//                   src={seed}
//                   alt="Diamond Seed"
//                   width={200}
//                   height={200}
//                   className="w-24 sm:w-28 md:w-32 xl:w-40 2xl:w-48 h-24 sm:h-28 md:h-32 xl:h-40 2xl:h-48 object-contain bg-white rounded-full p-4 shadow-2xl group-hover:scale-110 transition-transform duration-300"
//                 />
//                 <div className="absolute -inset-2 bg-gradient-to-r from-emerald-400 to-blue-600 rounded-full opacity-20 group-hover:opacity-40 blur-md"></div>
//               </div>
//               <h3 className="text-base sm:text-lg md:text-xl xl:text-2xl font-semibold mt-4 text-emerald-400">
//                 Lab Grown
//               </h3>
//               <p className="text-xs sm:text-sm md:text-base xl:text-lg text-gray-400">Diamond Seed</p>
//             </div>
//           </div>
//         </div>
//       </section>
//       <section className="py-12 md:py-16 xl:py-20 px-4 sm:px-8 md:px-16 xl:px-24 2xl:px-48 bg-black">
//         <div className="max-w-4xl md:max-w-5xl xl:max-w-6xl mx-auto text-center">
//           <p className="text-base sm:text-lg md:text-xl xl:text-2xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
//             Lab-grown and mined diamonds are{" "}
//             <span className="text-emerald-400 font-semibold">chemically identical</span> and{" "}
//             <span className="text-blue-400 font-semibold">visually indistinguishable</span>, but lab-grown diamonds
//             have a vastly smaller environmental and social footprint.
//           </p>
//         </div>
//       </section>


//       <section className="py-16 md:py-20 xl:py-28 px-4 sm:px-8 md:px-12 xl:px-16 2xl:px-24 bg-black">
//         <div className="max-w-full mx-auto grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 items-start" style={{ minHeight: '650px' }}>
//           {advantages.map(({ id, icon, title, subtitle, description, details }) => {
//             const isActive = activeCard === id;

//             if (isActive) {
     
//               return (
//                 <div
//                   key={`card-${id}`}
//                   className="group bg-gray-900 rounded-3xl p-10 md:p-12 flex flex-col items-center text-center shadow-lg cursor-pointer relative transition-all duration-300 ring-2 ring-emerald-400 h-[630px] w-full"
//                   onClick={() => handleCardClick(id)}
//                 >
//                   <div className="mb-6">{icon}</div>
//                   <h3 className="text-lg md:text-xl font-bold mb-1">{title}</h3>
//                   <p className="text-sm md:text-base font-semibold text-emerald-400 mb-3">{subtitle}</p>
//                   <p className="text-xs md:text-sm text-gray-400 mb-6">{description}</p>

//                   <button
//                     aria-label="Collapse details"
//                     className="absolute bottom-4 right-4 text-gray-400 hover:text-emerald-400 transition-transform duration-300 pointer-events-none"
//                   >
//                     <ChevronDown
//                       size={24}
//                       className="transform rotate-180 transition-transform duration-300"
//                     />
//                   </button>

//                   <div
//                     className="mt-4 w-full border-t border-gray-700 pt-4 text-left text-sm md:text-base animate-fadeIn"
//                     onClick={(e) => {
//                       e.preventDefault();
//                       e.stopPropagation();
//                     }}
//                   >
//                     {details.type === "comparison" ? (
//                       details.content
//                     ) : (
//                       <p>{details.content}</p>
//                     )}
//                   </div>
//                 </div>
//               );
//             }
//             return (
//               <div
//                 key={`card-${id}`}
//                 className="group bg-gray-900 rounded-3xl p-8 md:p-10 flex flex-col items-center text-center shadow-lg cursor-pointer relative h-[450px] w-full"
//                 onClick={() => handleCardClick(id)}
//               >
//                 <div className="mb-6">{icon}</div>
//                 <h3 className="text-lg md:text-xl font-bold mb-1">{title}</h3>
//                 <p className="text-sm md:text-base font-semibold text-emerald-400 mb-3">{subtitle}</p>
//                 <p className="text-xs md:text-sm text-gray-400 mb-6">{description}</p>

//                 <button
//                   aria-label="Expand details"
//                   className="absolute bottom-4 right-4 text-gray-400 hover:text-emerald-400 pointer-events-none"
//                 >
//                   <ChevronDown size={24} />
//                 </button>
//               </div>
//             );
//           })}
//         </div>
//       </section>

//       {/* Tables Section */}
//       <section className="py-16 md:py-20 xl:py-28 px-4 sm:px-8 md:px-16 xl:px-24 2xl:px-48 bg-black">
//         <div className="max-w-7xl mx-auto space-y-16">
//           {comparisonTables.map(({ title, headers, rows }, index) => (
//             <div key={index}>
//               <h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-emerald-400">
//                 {title}
//               </h2>
//               <div className="overflow-x-auto">
//                 <table className="min-w-full border-collapse border border-gray-700 text-sm md:text-base">
//                   <thead className="bg-gray-900">
//                     <tr>
//                       {headers.map((header, idx) => (
//                         <th
//                           key={idx}
//                           className="border border-gray-700 px-4 py-3 text-left whitespace-nowrap"
//                         >
//                           {header}
//                         </th>
//                       ))}
//                     </tr>
//                   </thead>
//                   <tbody>
//                     {rows.map((row, rIndex) => (
//                       <tr
//                         key={rIndex}
//                         className={rIndex % 2 === 0 ? "bg-gray-800" : "bg-gray-900"}
//                       >
//                         {row.map((cell, cIndex) => (
//                           <td
//                             key={cIndex}
//                             className="border border-gray-700 px-4 py-3 whitespace-nowrap"
//                           >
//                             {cell}
//                           </td>
//                         ))}
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>
//             </div>
//           ))}
//         </div>
//       </section>
//     </div>
//   );
// };

// export default DifferenceBetweenDiamonds;




"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import rough from "../../../public/images/about/rough.png";
import seed from "../../../public/images/about/seed.png";
import Image from "next/image";
import diamond from "../../../public/images/education/Bitmap.png";
import price from "../../../public/images/about/Difference - Lab Grown-Price-.png";
import tree from "../../../public/images/about/Difference - Lab Grown-Sustainability-.png";
import value from "../../../public/images/about/Difference - Lab Grown-Value-.png";
import future from "../../../public/images/about/FutureIcon.png";

const DifferenceBetweenDiamonds = () => {
  const [activeCard, setActiveCard] = useState(null);

  const handleCardClick = (cardId) => {
    setActiveCard(activeCard === cardId ? null : cardId);
  };

  // const advantages = [
  //   {
  //     id: 1,
  //     icon: <Image src={price} alt="Investment Value" className="w-20 md:w-24 xl:w-28" />,
  //     title: "Superior Investment",
  //     subtitle: "Intelligent Value",
  //     description: "Maximize your investment with identical quality at exceptional pricing for New York's discerning buyers",
  //     details: {
  //       type: "comparison",
  //       content: (
  //         <div className="grid grid-cols-2 gap-8 text-center">
  //           <div className="space-y-4 p-6 bg-slate-800/30 rounded-xl">
  //             <Image src={diamond} alt="Natural Diamond" className="mx-auto w-16 md:w-20" />
  //             <p className="font-light text-slate-300">Natural Diamond</p>
  //             <p className="text-xl md:text-2xl font-light text-amber-400">$15,000</p>
  //             <p className="text-sm text-slate-400">1 Carat, VS1, G Color</p>
  //           </div>
  //           <div className="space-y-4 p-6 bg-emerald-900/20 rounded-xl border border-emerald-400/30">
  //             <Image src={diamond} alt="Lab Diamond" className="mx-auto w-16 md:w-20" />
  //             <p className="font-light text-slate-300">Lab Grown Diamond</p>
  //             <p className="text-xl md:text-2xl font-light text-emerald-400">$3,000</p>
  //             <p className="text-sm text-slate-400">1 Carat, VS1, G Color</p>
  //           </div>
  //         </div>
  //       ),
  //     },
  //   },
  //   {
  //     id: 2,
  //     icon: <Image src={tree} alt="Ethical Sourcing" className="w-20 md:w-24 xl:w-28" />,
  //     title: "Ethical Excellence",
  //     subtitle: "Responsible Luxury",
  //     description: "Certified conflict-free diamonds that align with New York's values of social responsibility and ethical luxury",
  //     details: {
  //       type: "text",
  //       content:
  //         "Lab-grown diamonds offer complete transparency in origin, ensuring your investment supports ethical practices while maintaining the highest standards of luxury that New York clientele expects.",
  //     },
  //   },
  //   {
  //     id: 3,
  //     icon: <Image src={value} alt="Sustainability Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
  //     title: "Zero Environmental Impact",
  //     subtitle: "Sustainable Choice",
  //     description: "Clean creation process with no mining, no waste, no environmental damage",
  //     details: {
  //       type: "text",
  //       content:
  //         "Traditional mining disturbs 100 sq ft of earth and creates 6,000 lbs of waste per carat. Lab diamonds eliminate this entirely.",
  //     },
  //   },
  //   {
  //     id: 4,
  //     icon: <Image src={future} alt="Future Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
  //     title: "Future Forward",
  //     subtitle: "Growing Market",
  //     description: "Leading the diamond industry evolution with 22% projected growth",
  //     details: {
  //       type: "text",
  //       content:
  //         "The lab diamond market is forecasted to grow 22% annually from 2021-2026, representing the future of the diamond industry.",
  //     },
  //   },
  // ];
  const advantages = [
    {
      id: 1,
      icon: <Image src={price} alt="Price Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
      title: "Exceptional Value",
      subtitle: "90% Less Cost",
      description: "Lab diamonds deliver the same brilliance at a fraction of the price",
      details: {
        type: "comparison",
        content: (
          <div className="grid grid-cols-2 gap-3 text-center text-sm">
            <div className="space-y-2 p-3 bg-slate-800/30 rounded-lg overflow-hidden">
              <Image src={diamond} alt="Natural Diamond" className="mx-auto w-10 md:w-12" />
              <p className="font-light text-slate-300 text-xs truncate">Natural Diamond</p>
              <p className="text-sm md:text-base font-light text-amber-400 truncate">$10,000</p>
            </div>
            <div className="space-y-2 p-3 bg-emerald-900/20 rounded-lg border border-emerald-400/30 overflow-hidden">
              <Image src={diamond} alt="Lab Diamond" className="mx-auto w-10 md:w-12" />
              <p className="font-light text-slate-300 text-xs truncate">Lab Diamond</p>
              <p className="text-sm md:text-base font-light text-emerald-400 truncate">$1,000</p>
            </div>
          </div>
        ),
      },
    },
    {
      id: 2,
      icon: <Image src={tree} alt="Value Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
      title: "Certified Quality",
      subtitle: "Industry Standard",
      description: "Recognized and certified by leading gemological institutes worldwide",
      details: {
        type: "text",
        content:
          "Lab diamonds maintain identical chemical, physical, and optical properties to mined diamonds, ensuring lasting value and beauty.",
      },
    },
    {
      id: 3,
      icon: <Image src={value} alt="Sustainability Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
      title: "Zero Environmental Impact",
      subtitle: "Sustainable Choice",
      description: "Clean creation process with no mining, no waste, no environmental damage",
      details: {
        type: "text",
        content:
          "Traditional mining 100 sq ft of earth and creates 6,000 lbs of waste per carat. Lab diamonds eliminate this entirely.",
      },
    },
    {
      id: 4,
      icon: <Image src={future} alt="Future Icon" className="w-24 md:w-28 xl:w-32 2xl:w-36" />,
      title: "Future Forward",
      subtitle: "Growing Market",
      description: "Leading the diamond industry evolution with 22% projected growth",
      details: {
        type: "text",
        content:
          "The lab diamond market is forecasted to grow 22% annually from 2021-2026, representing the future of the diamond industry.",
      },
    },
  ];
  const comparisonTables = [
    {
      title: "Industrial Advantages",
      headers: ["Feature", "Earth Mined", "Lab Grown"],
      rows: [
        ["Origin Guaranteed", "❌", "✅"],
        ["Security of Supply", "❌", "✅"],
        ["Future Growth Security", "❌", "✅"],
        ["Employment Security", "❌", "✅"],
      ],
    },
    {
      title: "Technical Properties",
      headers: ["Property", "Earth Mined", "Lab Grown", "Cubic Zirconia", "Moissanite"],
      rows: [
        ["Composition", "C", "C", "ZrO₂", "SiC"],
        ["Crystal Structure", "Cubic", "Cubic", "Cubic", "Hexagonal"],
        ["Refractive Index", "2.42", "2.42", "2.2", "2.7"],
        ["Hardness (Mohs)", "10", "10", "8.25", "9.25"],
        ["Thermal Conductivity", "Excellent", "Excellent", "Poor", "High"],
        ["Purity Level", "Only 2% Type IIa", "100% Type IIa", "N/A", "N/A"],
        ["Measured In", "Carats", "Carats", "Carats", "Carats"],
        ["Blue Fluorescence", "Strong to None", "None", "None", "None"],
        ["Phosphorescence", "No", "Some", "NA", "NA"],
        ["Color", "Even", "Even", "NA", "NA"],
      ],
    },
  ];

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-black"></div>

        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-light text-white mb-8 tracking-[0.2em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              LAB-GROWN VS NATURAL DIAMONDS
            </h1>
            <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-lg md:text-xl text-white/90 mb-6 font-light max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Discover the difference between lab-grown and earth-mined diamonds
            </p>
          </motion.div>

          {/* Diamond Comparison Cards */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto mb-16"
          >
            {/* Earth Mined Diamond */}
            <div className="group bg-black/20 backdrop-blur-sm rounded-lg p-8 text-center border border-white/20 hover:border-white/40 transition-all duration-500">
              <div className="relative mb-6">
                <div className="w-24 h-24 md:w-32 md:h-32 mx-auto relative">
                  <div className="absolute inset-0 bg-white/10 rounded-full blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <Image
                    src={rough}
                    alt="Earth Mined Diamond"
                    width={160}
                    height={160}
                    className="w-full h-full object-cover rounded-full relative z-10 group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
              <h3 className="text-xl md:text-2xl font-light text-white mb-2 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                Earth Mined
              </h3>
              <p className="text-white/80 font-light mb-4 text-sm tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>Traditional Natural Origin</p>
              <div className="space-y-2 text-xs md:text-sm text-white/70">
                <div className="flex justify-between">
                  <span style={{ fontFamily: 'Times New Roman, serif' }}>Formation Time:</span>
                  <span className="text-white" style={{ fontFamily: 'Times New Roman, serif' }}>Billions of Years</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ fontFamily: 'Times New Roman, serif' }}>Environmental Impact:</span>
                  <span className="text-white" style={{ fontFamily: 'Times New Roman, serif' }}>Significant</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ fontFamily: 'Times New Roman, serif' }}>Price Premium:</span>
                  <span className="text-white" style={{ fontFamily: 'Times New Roman, serif' }}>Higher Cost</span>
                </div>
              </div>
            </div>

            {/* Lab Grown Diamond */}
            <div className="group bg-black/20 backdrop-blur-sm rounded-lg p-8 text-center border border-white/20 hover:border-white/40 transition-all duration-500">
              <div className="relative mb-6">
                <div className="w-24 h-24 md:w-32 md:h-32 mx-auto relative">
                  <div className="absolute inset-0 bg-white/10 rounded-full blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <Image
                    src={seed}
                    alt="Lab Grown Diamond"
                    width={160}
                    height={160}
                    className="w-full h-full object-contain bg-white/10 rounded-full p-4 relative z-10 group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
              <h3 className="text-xl md:text-2xl font-light text-white mb-2 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                Lab Grown
              </h3>
              <p className="text-white/80 font-light mb-4 text-sm tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>Advanced Scientific Creation</p>
              <div className="space-y-2 text-xs md:text-sm text-white/70">
                <div className="flex justify-between">
                  <span style={{ fontFamily: 'Times New Roman, serif' }}>Formation Time:</span>
                  <span className="text-white" style={{ fontFamily: 'Times New Roman, serif' }}>Weeks to Months</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ fontFamily: 'Times New Roman, serif' }}>Environmental Impact:</span>
                  <span className="text-white" style={{ fontFamily: 'Times New Roman, serif' }}>Minimal</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ fontFamily: 'Times New Roman, serif' }}>Price Advantage:</span>
                  <span className="text-white" style={{ fontFamily: 'Times New Roman, serif' }}>Exceptional Value</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Professional Insight Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-8"
          >
            <h2 className="text-3xl md:text-4xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              THE SOPHISTICATED CHOICE
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <div className="space-y-6">
              <p className="text-lg md:text-xl text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Lab-grown and mined diamonds are
                <span className="text-white font-medium"> chemically identical</span>,
                <span className="text-white font-medium"> optically indistinguishable</span>, and
                <span className="text-white font-medium"> physically equivalent</span>.
              </p>

              <p className="text-base text-white/80 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                The discerning choice for sophisticated clientele who value
                <span className="text-white font-medium"> exceptional quality</span>,
                <span className="text-white font-medium"> ethical sourcing</span>, and
                <span className="text-white font-medium"> intelligent investment</span>.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Advantages Cards */}
      {/* <section className="py-20 md:py-24 bg-gradient-to-b from-black to-slate-900">
        <div className="container mx-auto px-6 sm:px-8 lg:px-12 max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-light text-emerald-400 tracking-wider mb-4">
              WHY NEW YORK CHOOSES LAB-GROWN
            </h2>
            <p className="text-lg md:text-xl text-slate-300 font-light max-w-3xl mx-auto">
              The intelligent choice for sophisticated buyers who demand excellence without compromise
            </p>
            <div className="w-32 h-0.5 bg-gradient-to-r from-emerald-400 to-amber-400 mx-auto mt-6"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 items-stretch auto-rows-fr">
          {advantages.map(({ id, icon, title, subtitle, description, details }) => {
            const isActive = activeCard === id;

            if (isActive) {

              return (
                <div
                  key={`card-${id}`}
                  className="group bg-gradient-to-br from-slate-800/60 to-slate-900/90 backdrop-blur-sm rounded-2xl p-6 md:p-8 flex flex-col items-center text-center shadow-2xl cursor-pointer relative transition-all duration-500 border-2 border-emerald-400/50 hover:border-emerald-400 min-h-[350px] h-auto w-full overflow-hidden"
                  onClick={() => handleCardClick(id)}
                >
                  <div className="mb-6 transform group-hover:scale-110 transition-transform duration-300">{icon}</div>
                  <h3 className="text-lg md:text-xl font-light text-white mb-2 tracking-wide truncate">{title}</h3>
                  <p className="text-xs md:text-sm font-medium text-emerald-400 mb-3 tracking-wider truncate">{subtitle}</p>
                  <p className="text-sm md:text-base text-slate-300 mb-6 leading-relaxed font-light flex-grow break-words hyphens-auto">{description}</p>

                  <button
                    aria-label="Collapse details"
                    className="absolute bottom-4 right-4 text-gray-400 hover:text-emerald-400 transition-transform duration-300 pointer-events-none"
                  >
                    <ChevronDown
                      size={24}
                      className="transform rotate-180 transition-transform duration-300"
                    />
                  </button>

                  <div
                    className="mt-4 w-full border-t border-gray-700 pt-4 text-left text-xs md:text-sm animate-fadeIn overflow-hidden"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    {details.type === "comparison" ? (
                      details.content
                    ) : (
                      <p className="text-justify leading-relaxed break-words hyphens-auto">{details.content}</p>
                    )}
                  </div>
                </div>
              );
            }

       
            return (
              <div
                key={`card-${id}`}
                className="group bg-gradient-to-br from-slate-800/40 to-slate-900/70 backdrop-blur-sm rounded-2xl p-4 md:p-6 flex flex-col items-center text-center shadow-xl cursor-pointer relative min-h-[250px] h-auto w-full border border-slate-700/30 hover:border-emerald-400/30 transition-all duration-500 hover:shadow-emerald-400/10 hover:shadow-2xl overflow-hidden"
                onClick={() => handleCardClick(id)}
              >
                <div className="mb-6 transform group-hover:scale-105 transition-transform duration-300">{icon}</div>
                <h3 className="text-base md:text-lg font-light text-white mb-2 tracking-wide truncate">{title}</h3>
                <p className="text-xs md:text-sm font-medium text-emerald-400 mb-3 tracking-wider truncate">{subtitle}</p>
                <p className="text-xs md:text-sm text-slate-400 mb-auto leading-relaxed font-light flex-grow break-words hyphens-auto">{description}</p>

                <button
                  aria-label="Expand details"
                  className="absolute bottom-4 right-4 text-slate-500 hover:text-emerald-400 transition-colors duration-300 pointer-events-none"
                >
                  <ChevronDown size={20} />
                </button>

      
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-emerald-400/30 to-transparent group-hover:via-emerald-400/60 transition-all duration-500"></div>
              </div>
            );
          })}
          </div>
        </div>
      </section> */}

          <section className="py-8 md:py-10 xl:py-12 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 items-start" style={{ minHeight: '550px' }}>
          {advantages.map(({ id, icon, title, subtitle, description, details }) => {
            const isActive = activeCard === id;

            if (isActive) {
              // Active card - expanded version
              return (
                <div
                  key={`card-${id}`}
                  className="group bg-gray-900 rounded-3xl p-10 md:p-12 flex flex-col items-center text-center shadow-lg cursor-pointer relative transition-all duration-300 ring-2 ring-emerald-400 h-[550px] w-full"
                  onClick={() => handleCardClick(id)}
                >
                  <div className="mb-6">{icon}</div>
                  <h3 className="text-lg md:text-xl font-bold mb-1">{title}</h3>
                  <p className="text-sm md:text-base font-semibold text-emerald-400 mb-3">{subtitle}</p>
                  <p className="text-xs md:text-sm text-gray-400 mb-6">{description}</p>

                  <button
                    aria-label="Collapse details"
                    className="absolute bottom-4 right-4 text-gray-400 hover:text-emerald-400 transition-transform duration-300 pointer-events-none"
                  >
                    <ChevronDown
                      size={24}
                      className="transform rotate-180 transition-transform duration-300"
                    />
                  </button>

                  <div
                    className="mt-4 w-full border-t border-gray-700 pt-4 text-left text-sm md:text-base animate-fadeIn"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    {details.type === "comparison" ? (
                      details.content
                    ) : (
                      <p>{details.content}</p>
                    )}
                  </div>
                </div>
              );
            }

            // Inactive card - original size, no changes
            return (
              <div
                key={`card-${id}`}
                className="group bg-gray-900 rounded-3xl p-8 md:p-10 flex flex-col items-center text-center shadow-lg cursor-pointer relative h-[400px] w-full"
                onClick={() => handleCardClick(id)}
              >
                <div className="mb-6">{icon}</div>
                <h3 className="text-lg md:text-xl font-bold mb-1">{title}</h3>
                <p className="text-sm md:text-base font-semibold text-emerald-400 mb-3">{subtitle}</p>
                <p className="text-xs md:text-sm text-gray-400 mb-6">{description}</p>

                <button
                  aria-label="Expand details"
                  className="absolute bottom-4 right-4 text-gray-400 hover:text-emerald-400 pointer-events-none"
                >
                  <ChevronDown size={24} />
                </button>
              </div>
            );
          })}
        </div>
      </section>

      {/* Tables Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto space-y-16">
          {comparisonTables.map(({ title, headers, rows }, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-light text-white text-center mb-8 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                {title}
              </h2>
              <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-12"></div>
              <div className="overflow-x-auto">
                <table className="min-w-full border-collapse border border-white/20 text-sm md:text-base bg-black/20 backdrop-blur-sm rounded-lg overflow-hidden">
                  <thead className="bg-black/40">
                    <tr>
                      {headers.map((header, idx) => (
                        <th
                          key={idx}
                          className="border border-white/20 px-4 py-3 text-left whitespace-nowrap text-white font-light tracking-wide uppercase"
                          style={{ fontFamily: 'Times New Roman, serif' }}
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {rows.map((row, rIndex) => (
                      <tr
                        key={rIndex}
                        className={rIndex % 2 === 0 ? "bg-black/20" : "bg-black/30"}
                      >
                        {row.map((cell, cIndex) => (
                          <td
                            key={cIndex}
                            className="border border-white/20 px-4 py-3 whitespace-nowrap text-white/90 font-light"
                            style={{ fontFamily: 'Times New Roman, serif' }}
                          >
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </motion.div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default DifferenceBetweenDiamonds;
