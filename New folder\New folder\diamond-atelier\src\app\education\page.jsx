import React from "react";
import ClientEducationLandingPage from "./ClientEducationLandingPage";

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;

export const metadata = {
  title: "Diamond Education - Learn About Lab-Grown Diamonds | Diamond Atelier",
  description: "Comprehensive diamond education covering lab-grown vs natural diamonds, 4Cs, anatomy, methods, and more. Expert knowledge from Diamond Atelier.",
  keywords: [
    "diamond education",
    "lab grown diamonds",
    "diamond 4Cs",
    "diamond anatomy",
    "diamond grading",
    "diamond knowledge",
    "CVD diamonds",
    "HPHT diamonds",
    "diamond certification"
  ],
  openGraph: {
    title: "Diamond Education - Learn About Lab-Grown Diamonds | Diamond Atelier",
    description: "Comprehensive diamond education covering lab-grown vs natural diamonds, 4Cs, anatomy, methods, and more.",
    images: [
      {
        url: '/images/education-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Diamond Education - Diamond Atelier',
      },
    ],
  },
};

// Server-side data preparation
async function getEducationLandingData() {
  const educationSections = [
    {
      id: "difference",
      title: "Lab-Grown vs Natural Diamonds",
      subtitle: "Understanding the Key Differences",
      description: "Discover the similarities and differences between lab-grown and natural diamonds, including formation, properties, and value.",
      image: "/images/education/difference-hero.jpg",
      link: "/education/difference",
      features: ["Formation Process", "Physical Properties", "Price Comparison", "Environmental Impact"]
    },
    {
      id: "methods",
      title: "Diamond Creation Methods",
      subtitle: "CVD & HPHT Technologies",
      description: "Learn about the advanced technologies used to create lab-grown diamonds, including CVD and HPHT methods.",
      image: "/images/education/methods-hero.jpg",
      link: "/education/methods",
      features: ["CVD Process", "HPHT Technology", "Quality Control", "Innovation"]
    },
    {
      id: "whyLab",
      title: "Why Lab-Grown Diamonds",
      subtitle: "The Smart Choice for Modern Consumers",
      description: "Explore the benefits of choosing lab-grown diamonds for ethical, environmental, and economic reasons.",
      image: "/images/education/why-lab-hero.jpg",
      link: "/education/whyLab",
      features: ["Ethical Sourcing", "Environmental Benefits", "Cost Effectiveness", "Quality Assurance"]
    },
    {
      id: "the4cs",
      title: "The 4Cs of Diamonds",
      subtitle: "Cut, Color, Clarity, Carat",
      description: "Master the fundamental grading criteria that determine a diamond's quality and value.",
      image: "/images/education/4cs-hero.jpg",
      link: "/education/the4cs",
      features: ["Cut Quality", "Color Grading", "Clarity Assessment", "Carat Weight"]
    },
    {
      id: "anatomy",
      title: "Diamond Anatomy",
      subtitle: "Understanding Diamond Structure",
      description: "Explore the detailed anatomy of a diamond and how each part affects its brilliance and beauty.",
      image: "/images/education/anatomy-hero.jpg",
      link: "/education/anatomy",
      features: ["Crown & Pavilion", "Table & Culet", "Girdle Thickness", "Facet Arrangement"]
    },
    {
      id: "myth",
      title: "Myths & Facts",
      subtitle: "Debunking Diamond Misconceptions",
      description: "Separate fact from fiction with our comprehensive guide to common diamond myths and realities.",
      image: "/images/education/myths-hero.jpg",
      link: "/education/myth",
      features: ["Common Myths", "Scientific Facts", "Expert Insights", "Truth Revealed"]
    },
    {
      id: "evolution",
      title: "Diamond Industry Evolution",
      subtitle: "Past, Present & Future",
      description: "Trace the evolution of the diamond industry and the revolutionary impact of lab-grown diamonds.",
      image: "/images/education/evolution-hero.jpg",
      link: "/education/evolution",
      features: ["Historical Timeline", "Technology Advances", "Market Trends", "Future Outlook"]
    },

    //important please dont delete this
    //   id: "labgrown",
    //   title: "Complete Lab-Grown Guide",
    //   subtitle: "Everything You Need to Know",
    //   description: "Comprehensive guide covering all aspects of lab-grown diamonds in one complete resource.",
    //   image: "/images/education/labgrown-hero.jpg",
    //   link: "/education/labgrown",
    //   features: ["Complete Overview", "Interactive Learning", "Expert Knowledge", "Detailed Analysis"]
    // }
  ];

  const stats = {
    totalTopics: educationSections.length,
    expertYears: "15+",
    studentsEducated: "5000+",
    certifications: "IGI & GIA Certified"
  };

  return {
    educationSections,
    stats
  };
}

// Server-side component
export default async function Page() {
  const { educationSections, stats } = await getEducationLandingData();

  return (
    <ClientEducationLandingPage
      educationSections={educationSections}
      stats={stats}
    />
  );
}
