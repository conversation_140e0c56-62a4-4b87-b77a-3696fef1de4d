"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/page",{

/***/ "(app-pages-browser)/./src/components/common/SimpleHome.jsx":
/*!**********************************************!*\
  !*** ./src/components/common/SimpleHome.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AnimatedHeading */ \"(app-pages-browser)/./src/components/common/AnimatedHeading.jsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sections = [\n    // {\n    //   id: \"hero\",\n    //   title: \"Exquisite Lab Grown Diamonds\",\n    //   subtitle: \"Every Diamond Tells a Story\",\n    //   description:\n    //     \"Ethically sourced, certified diamonds with unparalleled quality and brilliance.\",\n    //   image: \"/image/tache_diamond_rough_cut.png\",\n    //   cta: \"Discover Our Collection\",\n    // },\n    {\n        id: \"shapes\",\n        title: \"100+ shapes\",\n        subtitle: \"Diamonds over 100+ shapes\",\n        description: \"Our artisan workshop offers 100+ diamond shapes, crafted with elegance and precision. We also create custom shapes tailored to your vision within 30 days.\",\n        image: \"/image/diamond-cuts.jpg\",\n        // video: \"/images/about/Shapes video.mp4\",\n        cta: \"Discover Shapes\"\n    },\n    {\n        id: \"matching-layout\",\n        title: \"Matching layout\",\n        subtitle: \"Calibrated consistency from 10–99 cents\",\n        description: \"Our comprehensive collection of calibrated matching layouts ranges from 10 to 99 cents, with a 0.10 MM tolerance. Whether you seek uniformity in shapes, colors, clarities, dimensions, or cent values, trust us to deliver precisely what you need.\",\n        image: \"/image/shaping_phase_tache-768x768.png\",\n        video: \"/images/about/Matching Layout video.mp4\",\n        cta: \"View Our Process\"\n    },\n    {\n        id: \"colors\",\n        title: \"30+ colors\",\n        subtitle: \"Nature's Spectrum, Perfected Diamonds colors over 30+ colors\",\n        description: \"We specialize in unique colored diamonds across 10+ fancy shades, with customized color delivery guaranteed within 20 days.\",\n        image: \"/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png\",\n        video: null,\n        cta: \"Explore Colors\"\n    },\n    {\n        id: \"star-melee\",\n        title: \"Star Melee\",\n        subtitle: \"Small-sized lab-grown diamonds\",\n        description: \"We have the finest small-sized lab-grown diamonds: -2 (0.006 to 0.008 carats), Star ranging from (0.009 to 0.021 carats), and Melee (0.021 to 0.074 carats). Perfect for intricate jewelry designs.\",\n        image: \"/images/about/star-melee.png\",\n        video: null,\n        cta: \"View Star Melee\"\n    }\n];\nfunction SimpleHome() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHome.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SimpleHome.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"full-section relative w-full h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png\",\n                        alt: \"Diamond Atelier Homepage Banner\",\n                        fill: true,\n                        className: \"absolute inset-0 w-full h-full object-cover\",\n                        priority: true,\n                        quality: 90\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full opacity-60\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight,\n                                    scale: 0\n                                },\n                                animate: {\n                                    y: [\n                                        null,\n                                        -100\n                                    ],\n                                    opacity: [\n                                        0.6,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 3,\n                                    ease: \"easeOut\"\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col items-center justify-center z-20 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat\",\n                                            style: {\n                                                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',\n                                                letterSpacing: \"0.1em\"\n                                            },\n                                            children: \"DIAMONDS THAT DESERVE YOU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 0.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center max-w-4xl mx-auto mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.3em] leading-relaxed font-montserrat\",\n                                        children: [\n                                            \" \",\n                                            \"10,000+ Certified Stones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg lg:text-xl text-black/70 font-light tracking-wide leading-relaxed font-montserrat\",\n                                        children: \"Where precision meets perfection in every facet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 1,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/shapes\",\n                                        className: \"group relative px-8 py-3 bg-Black border border-white/30 text-Black hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"EXPlORE SHAPES\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-Black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"https://inventory.diamondatelier.in/\",\n                                        className: \"group relative px-8 py-3 bg-black/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: \"VIEW INVENTORY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            delay: 1.5\n                        },\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                        onClick: ()=>setCurrentIndex(1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs tracking-widest mb-2 font-light\",\n                                    children: \"SCROLL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 bg-gradient-to-br from-gray-50 to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center justify-between gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-black\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                children: sections[0].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: sections[0].title,\n                                            level: \"h1\",\n                                            className: \"text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat\",\n                                            showUnderline: false,\n                                            animationDelay: 0.2,\n                                            triggerOnMount: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat\",\n                                            children: sections[0].description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/shapes\",\n                                            className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                            children: sections[0].cta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-64 mb-6 overflow-hidden rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: sections[0].image,\n                                                        alt: sections[0].title,\n                                                        fill: true,\n                                                        className: \"object-cover\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl text-black font-light mb-4 font-montserrat\",\n                                                            children: \"Premium Diamond Cuts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 font-light font-montserrat\",\n                                                            children: \"Masterfully crafted for maximum brilliance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                        children: sections[0].subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    text: sections[0].title,\n                                    level: \"h1\",\n                                    className: \"text-4xl sm:text-5xl mb-8 font-light leading-tight font-montserrat\",\n                                    showUnderline: false,\n                                    animationDelay: 0.2,\n                                    triggerOnMount: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-xl p-6 max-w-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 mb-4 overflow-hidden rounded-xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: sections[0].image,\n                                                    alt: sections[0].title,\n                                                    fill: true,\n                                                    className: \"object-cover\",\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-light mb-2 font-montserrat\",\n                                                children: \"Premium Diamond Cuts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm font-light font-montserrat\",\n                                                children: \"Masterfully crafted for maximum brilliance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                    children: sections[0].description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/shapes\",\n                                    className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                    children: sections[0].cta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            sections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 \".concat(index % 2 === 0 ? 'bg-white' : 'bg-gray-50'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:grid lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-black \".concat(index % 2 === 0 ? 'order-2' : 'order-1'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                    children: section.subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl xl:text-5xl mb-6 font-light leading-tight font-montserrat\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 font-montserrat\",\n                                                children: section.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                        children: section.cta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                        children: \"Learn More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(index % 2 === 0 ? 'order-1' : 'order-2', \" flex justify-center\"),\n                                        children: [\n                                            section.id === 'star-melee' ? /* Star Melee Card Box */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full max-w-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64 mb-6 overflow-hidden rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: section.image,\n                                                                alt: section.title,\n                                                                fill: true,\n                                                                className: \"object-contain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl text-black font-light mb-4 font-montserrat\",\n                                                                    children: \"Premium Star Melee\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 font-light font-montserrat\",\n                                                                    children: \"Perfect for intricate jewelry designs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this) : section.id === 'colors' ? /* 30+ Colors Card Box */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full max-w-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64 mb-6 overflow-hidden rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: section.image,\n                                                                alt: section.title,\n                                                                fill: true,\n                                                                className: \"object-contain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl text-black font-light mb-4 font-montserrat\",\n                                                                    children: \"Premium Fancy Colors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 font-light font-montserrat\",\n                                                                    children: \"30+ stunning diamond colors available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this) : /* Regular Video/Image Container */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative max-w-md w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden rounded-2xl shadow-2xl mx-auto \".concat(section.id === 'shapes' ? 'h-72 w-80' : 'h-96 w-full max-w-lg'),\n                                                    children: [\n                                                        section.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            autoPlay: true,\n                                                            loop: true,\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            className: \"w-full h-full object-cover\",\n                                                            ref: (video)=>{\n                                                                if (video) {\n                                                                    video.playbackRate = 2.5;\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                    src: section.video,\n                                                                    type: \"video/mp4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: section.image,\n                                                                    alt: section.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this),\n                                            section.id !== 'star-melee' && /* <div className=\"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-xl p-4 max-w-xs\">\n                    <h4 className=\"text-base font-medium mb-2 font-montserrat\">\n                      {section.id === 'shapes' && '100+ Diamond Shapes'}\n                      {section.id === 'matching-layout' && 'Calibrated Precision'}\n                      {section.id === 'colors' && '30+ Fancy Colors'}\n                    </h4>\n                    <p className=\"text-xs text-gray-600 font-light font-montserrat\">\n                      {section.id === 'shapes' && 'Custom shapes in 30 days'}\n                      {section.id === 'matching-layout' && '0.10 MM tolerance guarantee'}\n                      {section.id === 'colors' && 'Custom colors in 20 days'}\n                    </p>\n                  </div> */ null\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden text-center text-black\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                            children: section.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl mb-6 font-light leading-tight font-montserrat\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: section.id === 'star-melee' ? /* Star Melee Mobile Card */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl shadow-xl p-6 max-w-sm mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 mb-4 overflow-hidden rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: section.image,\n                                                        alt: section.title,\n                                                        fill: true,\n                                                        className: \"object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl text-black font-light mb-2 font-montserrat\",\n                                                            children: \"Premium Star Melee\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm font-light font-montserrat\",\n                                                            children: \"Perfect for intricate jewelry designs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, this) : section.id === 'colors' ? /* 30+ Colors Mobile Card */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl shadow-xl p-6 max-w-sm mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 mb-4 overflow-hidden rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: section.image,\n                                                        alt: section.title,\n                                                        fill: true,\n                                                        className: \"object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl text-black font-light mb-2 font-montserrat\",\n                                                            children: \"Premium Fancy Colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm font-light font-montserrat\",\n                                                            children: \"30+ stunning diamond colors available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this) : /* Regular Mobile Video/Image */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden rounded-2xl shadow-xl mx-auto \".concat(section.id === 'shapes' ? 'h-48 w-64' : 'h-64 max-w-sm w-full'),\n                                            children: [\n                                                section.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                    autoPlay: true,\n                                                    loop: true,\n                                                    muted: true,\n                                                    playsInline: true,\n                                                    className: \"w-full h-full object-cover\",\n                                                    ref: (video)=>{\n                                                        if (video) {\n                                                            video.playbackRate = 4.0;\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                            src: section.video,\n                                                            type: \"video/mp4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: section.image,\n                                                    alt: section.title,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-4 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                children: section.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                children: \"Learn More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, section.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative w-full h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"url('/image/Expert.jpg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/40 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 533,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-light text-black mb-6 drop-shadow-lg font-montserrat\",\n                                        children: \"Expert Eyes on Every Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-black mb-8 leading-relaxed drop-shadow-md font-light font-montserrat\",\n                                        children: \"Our in-house gemologists personally inspect and verify every lab-grown diamond. You'll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 536,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHome, \"2oCXE2WiAW05zG4oFnHELxlAGiA=\");\n_c = SimpleHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/SimpleHome.jsx\n"));

/***/ })

});