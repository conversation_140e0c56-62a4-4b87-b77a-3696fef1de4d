"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { ChevronRight, BookOpen, Users, Award, Clock } from "lucide-react";
import LoadingScreen from "../../utils/LoadingScreen";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } }
};

const staggerContainer = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.1 } }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.6, ease: "easeOut" } }
};

export default function ClientEducationLandingPage({ educationSections, stats }) {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 800);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <LoadingScreen />;

  return (
    <div className="bg-black text-white min-h-screen" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section */}
      <section className="relative pt-20 sm:pt-24 md:pt-32 pb-12 sm:pb-16 md:pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
        </div>
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <h1 className="text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light text-white mb-6 sm:mb-8 tracking-[0.1em] sm:tracking-[0.2em] uppercase leading-tight" style={{ fontFamily: 'Times New Roman, serif' }}>
            
              DIAMOND EDUCATION
            </h1>
            <div className="h-px w-24 sm:w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6 sm:mb-8"></div>
            <p className="text-base sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6 font-light max-w-4xl mx-auto tracking-wide px-2" style={{ fontFamily: 'Times New Roman, serif' }}>
              When You Can Buy The Diamond, Take The Knowledge About Them
            </p>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-white/85 max-w-5xl mx-auto leading-relaxed mb-8 sm:mb-12 md:mb-16 tracking-wide font-light px-2" style={{ fontFamily: 'Times New Roman, serif', lineHeight: '1.6' }}>
              Make informed decisions with confidence. Before investing in your perfect diamond,
              empower yourself with expert knowledge. From understanding the 4Cs to mastering
              lab-grown technology, our comprehensive education ensures you choose wisely and buy smartly.
            </p>
          </motion.div>


        </div>
      </section>

      {/* Education Sections Grid */}
      <section className="relative py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
     
        <div className="relative max-w-7xl mx-auto z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-light text-white mb-4 sm:mb-6 tracking-[0.1em] sm:tracking-[0.15em] uppercase leading-tight" style={{ fontFamily: 'Times New Roman, serif' }}>
              Smart Buying Starts With Smart Learning
            </h2>
            <div className="h-px w-16 sm:w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6 sm:mb-8"></div>
            <p className="text-sm sm:text-base text-white/80 max-w-4xl mx-auto tracking-wide px-2" style={{ fontFamily: 'Times New Roman, serif' }}>
              Every expert buyer starts here. Choose your learning path and become a confident diamond purchaser.
              Knowledge is your best investment before making the purchase.
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8"
          >
            {educationSections.map((section, index) => (
              <motion.div
                key={section.id}
                variants={fadeInUp}
                className="group relative bg-black/20 backdrop-blur-sm rounded-lg overflow-hidden border border-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105"
              >
                <Link href={section.link} className="block">
                  <div className="p-4 sm:p-6 md:p-8">
                    <div className="flex items-center justify-between mb-4 sm:mb-6">
                      <h3 className="text-base sm:text-lg md:text-xl font-light text-white group-hover:text-white transition-colors duration-500 tracking-wide uppercase leading-tight" style={{ fontFamily: 'Times New Roman, serif' }}>
                        {section.title}
                      </h3>
                      <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-white/60 group-hover:text-white group-hover:translate-x-1 transition-all duration-500 flex-shrink-0" />
                    </div>

                    <p className="text-white/70 text-xs sm:text-sm mb-3 sm:mb-4 font-light tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                      {section.subtitle}
                    </p>

                    <p className="text-white/80 text-xs sm:text-sm leading-relaxed mb-4 sm:mb-6 tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                      {section.description}
                    </p>

                    <div className="space-y-2 sm:space-y-3">
                      <p className="text-xs text-white/60 font-light mb-2 sm:mb-3 uppercase tracking-[0.1em]" style={{ fontFamily: 'Times New Roman, serif' }}>KEY TOPICS:</p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 sm:gap-2">
                        {section.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center text-xs text-white/70 tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                            <div className="w-1 h-1 bg-white/40 rounded-full mr-2 flex-shrink-0"></div>
                            <span className="leading-tight">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-black/20 backdrop-blur-sm rounded-lg p-6 sm:p-8 md:p-12 border border-white/20"
          >
            <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-light text-white mb-4 sm:mb-6 tracking-[0.1em] uppercase leading-tight" style={{ fontFamily: 'Times New Roman, serif' }}>
              Ready to Buy Smart? Learn First.
            </h3>
            <p className="text-white/80 mb-6 sm:mb-8 md:mb-10 max-w-3xl mx-auto tracking-wide text-sm sm:text-base px-2" style={{ fontFamily: 'Times New Roman, serif' }}>
              Don't just buy a diamond - invest in the right diamond. Start your education journey today
              and make every purchase decision with expert-level confidence and knowledge.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center">
              <Link href="/education/labgrown">
                <button className="bg-white text-black px-6 sm:px-8 md:px-10 py-3 sm:py-4 rounded-lg font-light hover:bg-white/90 transition-all duration-300 tracking-wide uppercase text-sm sm:text-base w-full sm:w-auto" style={{ fontFamily: 'Times New Roman, serif' }}>
                  Start Learning
                </button>
              </Link>
              <Link href="/contact">
                <button className="border-2 border-white/40 text-white px-6 sm:px-8 md:px-10 py-3 sm:py-4 rounded-lg font-light hover:bg-white hover:text-black transition-all duration-300 tracking-wide uppercase text-sm sm:text-base w-full sm:w-auto" style={{ fontFamily: 'Times New Roman, serif' }}>
                  Ask Expert
                </button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
