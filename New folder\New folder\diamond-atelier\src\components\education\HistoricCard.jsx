// import { useState } from 'react';

// const HistoricCard = ({ event, isVisible, delay }) => {
//     const [imageLoaded, setImageLoaded] = useState(false);
  
//     const cardStyle = {
//       transitionDelay: `${delay}s`,
//       opacity: isVisible ? 1 : 0,
//       transform: isVisible ? "translateY(0)" : "translateY(20px)",
//       backgroundColor: "#fff",
//       borderRadius: "8px",
//       boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
//       overflow: "hidden",
//       cursor: "pointer",
//       marginBottom: "16px",
//       display: "flex",
//       flexDirection: "column",
//     };
  
//     const imageContainerStyle = {
//       width: "100%",
//       height: "180px", 
//       overflow: "hidden",
//       flexShrink: 0,
//     };
  
//     const imageStyle = {
//       width: "100%",
//       height: "100%",
//       objectFit: "cover",
//       transition: "opacity 0.3s ease",
//       opacity: imageLoaded ? 1 : 0,
//     };
  
//     return (
//       <div className="event-card" style={cardStyle}>
//         <div className="event-image-container" style={imageContainerStyle}>
//           <img
//             src={event.imageUrl}
//             alt={event.title}
//             style={imageStyle}
//             onLoad={() => setImageLoaded(true)}
//           />
//           <div
//             className={`event-date ${event.highlighted ? "highlighted" : ""}`}
//             style={{
//               position: "absolute",
//               top: "8px",
//               left: "8px",
//               backgroundColor: event.highlighted ? "red" : "rgba(0,0,0,0.6)",
//               color: "#fff",
//               padding: "14px 8px",
//               borderRadius: "4px",
//               fontSize: "12px",
//               fontWeight: "bold",
//             }}
//           >
//             {event.date}
//           </div>
//         </div>
//         <div
//           className="event-content"
//           style={{
//             padding: "12px 16px",
//             flexGrow: 1,
//             display: "flex",
//             flexDirection: "column",
//             justifyContent: "center",
//           }}
//         >
//           <p
//             className="event-description"
//             style={{
//               color: event.highlighted ? "red" : "black",
//               margin: 0,
//               fontSize: "14px",
//               lineHeight: "1.4",
//             }}
//           >
//             {event.description}
//           </p>
//         </div>
//       </div>
//     );
//   };

// export default HistoricCard;


import { useState } from 'react';

const HistoricCard = ({ event, isVisible, delay }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
  
    const cardStyle = {
      transitionDelay: `${delay}s`,
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? "translateY(0)" : "translateY(20px)",
      backgroundColor: "#fff",
      borderRadius: "8px",
      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      overflow: "hidden",
      cursor: "pointer",
      marginBottom: "16px",
      display: "flex",
      flexDirection: "column",
    };
  
    const imageContainerStyle = {
      width: "100%",
      height: "180px", 
      overflow: "hidden",
      flexShrink: 0,
    };
  
    const imageStyle = {
      width: "100%",
      height: "100%",
      objectFit: "cover",
      transition: "opacity 0.3s ease",
      opacity: imageLoaded ? 1 : 0,
    };
  
    return (
      <div className="event-card" style={cardStyle}>
        <div className="event-image-container" style={imageContainerStyle}>
          <img
            src={event.imageUrl}
            alt={event.title}
            style={imageStyle}
            onLoad={() => setImageLoaded(true)}
          />
          <div
            className={`event-date ${event.highlighted ? "highlighted" : ""}`}
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              backgroundColor: event.highlighted ? "red" : "rgba(0,0,0,0.6)",
              color: "#fff",
              padding: "14px 8px",
              borderRadius: "4px",
              fontSize: "12px",
              fontWeight: "bold",
            }}
          >
            {event.date}
          </div>
        </div>
        <div
          className="event-content"
          style={{
            padding: "12px 16px",
            flexGrow: 1,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
          }}
        >
          <p
            className="event-description"
            style={{
              color: event.highlighted ? "red" : "black",
              margin: 0,
              fontSize: "14px",
              lineHeight: "1.4",
            }}
          >
            {event.description}
          </p>
        </div>
      </div>
    );
  };

export default HistoricCard;
