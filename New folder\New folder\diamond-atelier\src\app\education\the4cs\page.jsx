import React from "react";
import The4Cs from "../../../components/education/4Cs";

export const metadata = {
  title: "The 4Cs of Diamonds - Cut, Color, Clarity, Carat | Diamond Education",
  description: "Master the 4Cs of diamond grading: Cut, Color, Clarity, and Carat. Complete guide to understanding diamond quality and making informed purchases.",
  keywords: [
    "4Cs of diamonds",
    "diamond cut",
    "diamond color",
    "diamond clarity",
    "diamond carat",
    "diamond grading",
    "diamond education",
    "diamond quality guide"
  ],
  openGraph: {
    title: "The 4Cs of Diamonds - Complete Diamond Quality Guide",
    description: "Master the 4Cs of diamond grading: Cut, Color, Clarity, and Carat. Complete guide to understanding diamond quality.",
    images: [
      {
        url: '/images/4cs-og.jpg',
        width: 1200,
        height: 630,
        alt: 'The 4Cs of Diamonds - Quality Guide',
      },
    ],
  },
};

// Server-side component
export default function Page() {
  return <The4Cs />;
}