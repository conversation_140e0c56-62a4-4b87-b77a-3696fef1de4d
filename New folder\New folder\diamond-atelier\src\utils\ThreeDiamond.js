"use client";

import React, { useRef, useEffect } from "react";
import * as THREE from "three";
import { OctahedronGeometry } from "three";

const ThreeDiamond = ({ mousePosition }) => {
  const threeRef = useRef(null);
  const animationRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const isVisibleRef = useRef(true);

  useEffect(() => {
    const currentRef = threeRef.current;
    if (!currentRef) return;

    // Clear previous canvas if any
    currentRef.innerHTML = "";

    const size = 400;

    const scene = new THREE.Scene();
    sceneRef.current = scene;

    const camera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);
    camera.position.z = 7;

    const renderer = new THREE.WebGLRenderer({
      alpha: true,
      antialias: true,
      powerPreference: "high-performance" // Optimize for performance
    });
    renderer.setSize(size, size);
    renderer.setClearColor(0x000000, 0);
    rendererRef.current = renderer;
    currentRef.appendChild(renderer.domElement);

    const geometry = new OctahedronGeometry(1.5, 0);

    const material = new THREE.MeshPhysicalMaterial({
      color: 0xffffff,
      metalness: 0,
      roughness: 0,
      transmission: 1,
      thickness: 1.5,
      ior: 2.4,
      clearcoat: 1,
      clearcoatRoughness: 0,
      reflectivity: 1,
      side: THREE.DoubleSide,
    });

    const diamond = new THREE.Mesh(geometry, material);
    scene.add(diamond);

    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    scene.add(ambientLight);

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight1.position.set(5, 10, 7);
    scene.add(directionalLight1);

    const directionalLight2 = new THREE.DirectionalLight(0xaaaaaa, 0.5);
    directionalLight2.position.set(-5, -3, -3);
    scene.add(directionalLight2);

    const pointLight = new THREE.PointLight(0xffffff, 1, 20);
    pointLight.position.set(0, 2, 3);
    scene.add(pointLight);

    const animate = () => {
      if (!isVisibleRef.current) return; // Pause when not visible

      animationRef.current = requestAnimationFrame(animate);
      diamond.rotation.x += 0.005 + (mousePosition.y || 0) * 0.02;
      diamond.rotation.y += 0.01 + (mousePosition.x || 0) * 0.02;
      renderer.render(scene, camera);
    };
    animate();

    // Intersection Observer to pause animation when not visible
    const observer = new IntersectionObserver(
      ([entry]) => {
        isVisibleRef.current = entry.isIntersecting;
        if (entry.isIntersecting && !animationRef.current) {
          animate(); // Resume animation
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(currentRef);

    return () => {
      // Proper cleanup
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }

      observer.disconnect();

      // Dispose of Three.js resources
      geometry.dispose();
      material.dispose();
      renderer.dispose();

      // Clear scene
      while(scene.children.length > 0) {
        scene.remove(scene.children[0]);
      }

      if (currentRef) {
        currentRef.innerHTML = "";
      }
    };
  }, [mousePosition]);

  return <div ref={threeRef} className="w-96 h-96 mx-auto" />;
};

export default ThreeDiamond;
