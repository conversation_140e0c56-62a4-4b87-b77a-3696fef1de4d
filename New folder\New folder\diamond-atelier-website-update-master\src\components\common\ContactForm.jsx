"use client";

import React, { useState, useEffect } from 'react';

function ContactForm() {
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [message, setMessage] = useState('');
  const [mounted, setMounted] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log({ name, phone, message });
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="md:max-w-xs xl:max-w-md w-full mx-auto">
      <div className={`bg-[#d9d9d9] rounded-2xl shadow-xl xl:p-6 md:p-4 p-6 ${mounted ? 'fade-left' : ''}`} data-aos={mounted ? "fade-left" : undefined}>
        <form className="xl:space-y-4 space-y-1" onSubmit={handleSubmit}>
          <div>
            <label htmlFor='name' className='text-[#111c4f] block mb-1 xl:text-base md:text-sm text-xs font-medium'>Full Name:</label>
            <input
              type="text"
              id="name"
              value={name}
              placeholder="Enter your Full Name"
              className="bg-white rounded-full w-full xl:py-2 py-1 px-4 font-medium shadow focus:outline-none"
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div>
            <label htmlFor='phone' className='text-[#111c4f] block mb-1 xl:text-base md:text-sm text-xs font-medium'>Phone Number:</label>
            <input
              type='tel'
              id="phone"
              value={phone}
              placeholder='****** 456 7890'
              onChange={(e) => setPhone(e.target.value)}
              className="bg-white rounded-full w-full xl:py-2 py-1 px-4 font-medium shadow focus:outline-none"
            />
          </div>
          <div>
            <label htmlFor='message' className='text-[#111c4f] block mb-1 xl:text-base md:text-sm text-xs font-medium'>Message:</label>
            <textarea
              id="message"
              rows={4}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Write your message..."
              className="bg-white w-full px-4 xl:py-2 py-0 rounded-md font-medium shadow focus:outline-none"
            />
          </div>
          <div className="text-center">
            <button type="submit" className="bg-white text-[#111c4f] rounded-full px-8 xl:py-2 py-1 text-base font-bold hover:bg-gray-400 hover:text-white transition">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ContactForm;
