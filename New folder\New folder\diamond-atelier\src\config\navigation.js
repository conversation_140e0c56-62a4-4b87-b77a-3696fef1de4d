/**
 * Navigation Configuration
 * 
 * Centralized configuration for all navigation routes used in the header.
 * This makes it easier to maintain and update navigation items.
 */

export const navigationRoutes = [
  { 
    id: 1, 
    label: "INVENTORY", 
    path: "https://inventory.diamondatelier.in/", 
    external: true,
    description: "Browse our diamond inventory"
  },
  { 
    id: 2, 
    label: "ABOUT", 
    path: "/about", 
    external: false,
    description: "Learn about Diamond Atelier"
  },
  { 
    id: 3, 
    label: "SHAPES", 
    path: "/shapes", 
    external: false,
    description: "Explore diamond shapes"
  },
  { 
    id: 4, 
    label: "EDUCATION", 
    path: "/education/labgrown", 
    external: false,
    description: "Diamond education and information"
  },
  { 
    id: 5, 
    label: "CONTACT US", 
    path: "/contact", 
    external: false,
    description: "Get in touch with us"
  },
  { 
    id: 6, 
    label: "CAREERS", 
    path: "/careers", 
    external: false,
    description: "Join our team"
  },
];

/**
 * Utility function to get route by path
 * @param {string} path - The route path to search for
 * @returns {object|null} - The route object or null if not found
 */
export const getRouteByPath = (path) => {
  return navigationRoutes.find(route => route.path === path) || null;
};

/**
 * Utility function to check if a route is external
 * @param {string} path - The route path to check
 * @returns {boolean} - True if the route is external
 */
export const isExternalRoute = (path) => {
  const route = getRouteByPath(path);
  return route ? route.external : false;
};
