"use client";
import React, { useState, useEffect, useRef, useCallback } from 'react';

const RefreshCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef(null);
  const animationRef = useRef(null);

  // Optimized animation with RAF
  const startAnimation = useCallback(() => {
    if (hasAnimated) return;

    setHasAnimated(true);

    // Session storage for one-time animation
    const sessionKey = `counter_${targetNumber}_${suffix.replace(/[^a-zA-Z0-9]/g, '_')}`;
    const pageLoadKey = 'page_load_time';

    const now = Date.now();
    const lastLoad = sessionStorage.getItem(pageLoadKey);

    // Check if fresh page load (>2 seconds gap)
    const isNewPage = !lastLoad || (now - parseInt(lastLoad)) > 2000;

    if (isNewPage) {
      sessionStorage.clear();
      sessionStorage.setItem(pageLoadKey, now.toString());
    }

    // Check if already animated
    if (sessionStorage.getItem(sessionKey) && !isNewPage) {
      setCount(targetNumber);
      return;
    }

    // Mark as animated
    sessionStorage.setItem(sessionKey, 'done');

    // Optimized RAF animation
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Smooth easing
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentCount = Math.floor(easeOut * targetNumber);

      setCount(currentCount);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        setCount(targetNumber);
      }
    };

    animationRef.current = requestAnimationFrame(animate);
  }, [targetNumber, suffix, duration, hasAnimated]);

  useEffect(() => {
    // Quick session check first
    const sessionKey = `counter_${targetNumber}_${suffix.replace(/[^a-zA-Z0-9]/g, '_')}`;
    const pageLoadKey = 'page_load_time';

    const now = Date.now();
    const lastLoad = sessionStorage.getItem(pageLoadKey);
    const isNewPage = !lastLoad || (now - parseInt(lastLoad)) > 2000;

    if (sessionStorage.getItem(sessionKey) && !isNewPage) {
      setCount(targetNumber);
      setHasAnimated(true);
      return;
    }

    // Optimized intersection observer
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          startAnimation();
          observer.disconnect(); // Disconnect after first trigger
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px' // Start animation earlier
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [startAnimation, targetNumber, suffix]);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit'
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

export default RefreshCounter;
