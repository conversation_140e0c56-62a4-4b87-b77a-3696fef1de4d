@import "tailwindcss";
/* External font imports removed for better performance - using Next.js font optimization */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ========== Global Styles ========== */

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

/* Header scroll performance optimization */
header {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  contain: layout style paint;
}

/* Smooth scroll transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* GPU acceleration for smooth animations */
.sticky {
  transform: translateZ(0);
  will-change: transform;
}

.perspective-1000 {
  perspective: 1000px;
}

.backface-hidden {
  backface-visibility: hidden;
}


body {
  padding-top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

body::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

/* ========== Animation Utilities ========== */
@layer utilities {
  .animate-slide-down {
    animation: slideDown 0.3s ease-out forwards;
  }

  @keyframes slideDown {
    0% {
      opacity: 0;
      transform: translateY(-10%);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}

/* ========== Font Utilities ========== */
.font-ebgaramond {
  font-family: "EB Garamond", serif;
}

.font-migra {
  font-family: "Migra", serif;
}

.font-oldstandardtt {
  font-family: "Old Standard TT", serif;
}

.font-opensans {
  font-family: "Open Sans", sans-serif;
}

.font-montserrat {
  font-family: "Montserrat", sans-serif;
}

.font-arimo {
  font-family: "Arimo", sans-serif;
}

/* Modern Production Fonts */
.font-poppins {
  font-family: var(--font-poppins), sans-serif;
}

.font-playfair {
  font-family: var(--font-playfair), serif;
}

.font-dm-sans {
  font-family: var(--font-dm-sans), sans-serif;
}

.font-space-grotesk {
  font-family: var(--font-space-grotesk), sans-serif;
}

.font-inter {
  font-family: var(--font-inter), sans-serif;
}

/* Typography Enhancements */
.text-balance {
  text-wrap: balance;
}

.font-display {
  font-display: swap;
}

/* Professional Text Styles */
.text-heading {
  font-family: var(--font-space-grotesk), sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.text-subheading {
  font-family: var(--font-dm-sans), sans-serif;
  font-weight: 600;
  letter-spacing: -0.01em;
  line-height: 1.3;
}

.text-body {
  font-family: var(--font-inter), sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

.text-display {
  font-family: var(--font-playfair), serif;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

/* Custom Local Font */
@font-face {
  font-family: "Montserrat Classic";
  src: url("/fonts/MontserratClassic.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

/* ========== Theme Colors ========== */
:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* ========== Scrollbar Hiding Utility ========== */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

/* ========== Dynamic Header Spacing System ========== */
:root {
  --dynamic-header-height: 110px; /* Fallback value, will be updated by JS */
  --education-nav-height: 60px;
  --education-nav-top: 100px; /* Will be updated by JS */
}

/* Dynamic header spacing - automatically adjusts to actual header height */
.below-header {
  margin-top: var(--dynamic-header-height, 110px);
  transition: margin-top 0.3s ease; /* Smooth transition when header changes */
}

/* For pages with additional navigation (like education) */
.below-header-with-nav {
  margin-top: calc(var(--dynamic-header-height, 110px) + var(--education-nav-height, 60px));
  transition: margin-top 0.3s ease;
}

/* Alternative utility classes for different spacing needs */
.header-safe-area {
  padding-top: var(--dynamic-header-height, 110px);
}

.header-offset {
  top: var(--dynamic-header-height, 110px);
}

/* Fallback for browsers that don't support CSS custom properties */
@supports not (margin-top: var(--dynamic-header-height)) {
  .below-header {
    margin-top: 110px; /* Mobile fallback */
  }

  @media (min-width: 768px) {
    .below-header {
      margin-top: 150px; /* Tablet fallback */
    }
  }

  @media (min-width: 1024px) {
    .below-header {
      margin-top: 170px; /* Laptop fallback */
    }
  }

  @media (min-width: 1536px) {
    .below-header {
      margin-top: 220px; /* Desktop fallback */
    }
  }
}



/* ========== Footer Styling Fix ========== */
/* Ensure footer background is always pure black, even on deployment */
.footer-dark-bg {
  background-color: #000000 !important; /* Pure black */
  background: #000000 !important; /* No gradient, just solid black */
  color: #ffffff !important;
  min-height: 400px;
}

.footer-dark-bg * {
  color: inherit;
}

/* Additional Mac-specific fixes */
@supports (-webkit-appearance: none) {
  .footer-dark-bg {
    -webkit-background-clip: padding-box !important;
    background-clip: padding-box !important;
  }
}

/* Force pure black background on all browsers */
footer[class*="footer-dark-bg"] {
  background-color: #000000 !important;
  background-image: none !important; /* Remove any gradient */
}

/* ========== Performance Optimizations ========== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.reduce-paint {
  contain: layout style paint;
}

.optimize-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========== Additional Spacing Utilities ========== */
/* For sections that need extra top padding */
.section-padding-top {
  padding-top: 2rem; /* 32px base */
}

@media (min-width: 768px) {
  .section-padding-top {
    padding-top: 3rem; /* 48px tablets */
  }
}

@media (min-width: 1024px) {
  .section-padding-top {
    padding-top: 4rem; /* 64px laptops */
  }
}

@media (min-width: 1536px) {
  .section-padding-top {
    padding-top: 5rem; /* 80px large screens */
  }
}

/* For components that need to avoid header overlap */
.avoid-header-overlap {
  position: relative;
  z-index: 10;
}

/* Safe area for fixed positioned elements */
.safe-area-top {
  top: var(--safe-area-top, 110px);
}

:root {
  --safe-area-top: 110px; /* Mobile safe area */
}

@media (min-width: 768px) {
  :root {
    --safe-area-top: 150px; /* Tablet safe area */
  }
}

@media (min-width: 1024px) {
  :root {
    --safe-area-top: 170px; /* Laptop safe area - FIXED FOR YOUR LAPTOP */
  }
}

@media (min-width: 1536px) {
  :root {
    --safe-area-top: 220px; /* Large desktop safe area */
  }
}
