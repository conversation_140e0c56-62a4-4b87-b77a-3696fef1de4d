#!/usr/bin/env node

/**
 * Compress Evolution Image Script
 * 
 * This script creates a compressed version of the large evolution_main.jpg
 * Run with: node scripts/compress-evolution-image.js
 */

const fs = require('fs');
const path = require('path');

function compressEvolutionImage() {
  console.log('🖼️  Evolution Image Compression Guide\n');
  
  const originalPath = 'public/images/education/evolution/evolution_main.jpg';
  const compressedPath = 'public/images/education/evolution/evolution_main_compressed.jpg';
  
  // Check if original exists
  if (!fs.existsSync(originalPath)) {
    console.log('❌ Original evolution_main.jpg not found');
    return;
  }
  
  // Get file size
  const stats = fs.statSync(originalPath);
  const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
  
  console.log(`📊 Current File Analysis:`);
  console.log(`• File: ${originalPath}`);
  console.log(`• Size: ${fileSizeInMB}MB`);
  console.log(`• Status: 🚨 TOO LARGE (causing 200MB+ memory usage)`);
  
  console.log(`\n🎯 Target Optimization:`);
  console.log(`• Target Size: < 500KB (95% reduction)`);
  console.log(`• Quality: 60-70% (still good visual quality)`);
  console.log(`• Format: WebP (better compression)`);
  
  console.log(`\n🛠️  Compression Options:\n`);


  
  console.log(`1. 🌐 Online Tools (Easiest):`);
  console.log(`   • TinyJPG: https://tinyjpg.com/`);
  console.log(`   • Squoosh: https://squoosh.app/`);
  console.log(`   • ImageOptim: https://imageoptim.com/online`);
  
  console.log(`\n2. 💻 Command Line (Advanced):`);
  console.log(`   # Install ImageMagick first`);
  console.log(`   magick "${originalPath}" -quality 60 -resize 1200x675 "${compressedPath}"`);
  
  console.log(`\n3. 🔧 FFmpeg (If available):`);
  console.log(`   ffmpeg -i "${originalPath}" -q:v 8 -vf scale=1200:675 "${compressedPath}"`);
  
  console.log(`\n4. 📱 Manual Steps:`);
  console.log(`   1. Open ${originalPath} in any image editor`);
  console.log(`   2. Resize to 1200x675 pixels`);
  console.log(`   3. Save as JPEG with 60-70% quality`);
  console.log(`   4. Save as ${compressedPath}`);
  
  console.log(`\n🚀 Expected Results:`);
  console.log(`• File Size: ${fileSizeInMB}MB → 0.3-0.5MB (90%+ reduction)`);
  console.log(`• Memory Usage: 200MB+ → 20-30MB (85%+ reduction)`);
  console.log(`• Load Time: 3-5s → 0.5-1s (80%+ faster)`);
  console.log(`• Visual Quality: Minimal difference`);
  
  console.log(`\n⚡ Quick Fix (Temporary):`);
  console.log(`Replace the evolution image import with a smaller placeholder:`);
  console.log(`\n// In Evolution.jsx, replace:`);
  console.log(`import value from "../../../public/images/education/evolution/evolution_main.jpg";`);
  console.log(`// With:`);
  console.log(`import value from "../../../public/images/education/AboutLabGrown.png";`);
  
  console.log(`\n🔄 After Compression:`);
  console.log(`1. Replace the original file with compressed version`);
  console.log(`2. Test the About page: npm run dev`);
  console.log(`3. Check memory usage in DevTools`);
  console.log(`4. Verify image quality is acceptable`);
  
  // Create a backup recommendation
  console.log(`\n💾 Backup Recommendation:`);
  console.log(`cp "${originalPath}" "${originalPath}.backup"`);
  
  console.log(`\n🎯 Priority: URGENT - This single image is causing massive memory usage!`);
}

function createTemporaryFix() {
  console.log('\n🚑 Creating Temporary Fix...\n');
  
  const evolutionFile = 'src/components/education/Evolution.jsx';
  
  if (!fs.existsSync(evolutionFile)) {
    console.log('❌ Evolution.jsx not found');
    return;
  }
  
  let content = fs.readFileSync(evolutionFile, 'utf8');
  const originalContent = content;
  
  // Replace the large image import with a smaller one
  const oldImport = 'import value from "../../../public/images/education/evolution/evolution_main.jpg";';
  const newImport = 'import value from "../../../public/images/education/AboutLabGrown.png";';
  
  if (content.includes(oldImport)) {
    content = content.replace(oldImport, newImport);
    
    // Create backup
    fs.writeFileSync(`${evolutionFile}.backup`, originalContent);
    
    // Write updated content
    fs.writeFileSync(evolutionFile, content);
    
    console.log('✅ Temporary fix applied!');
    console.log('• Replaced 3.9MB evolution_main.jpg with 50KB AboutLabGrown.png');
    console.log('• Backup created: Evolution.jsx.backup');
    console.log('• Expected memory reduction: 150MB+');
    console.log('\n🧪 Test now: npm run dev');
    console.log('📊 Check memory usage in DevTools Console');
    
    console.log('\n⚠️  This is temporary - please compress the original image and revert this change');
  } else {
    console.log('ℹ️  Large image import not found or already replaced');
  }
}

function main() {
  console.clear();
  console.log('🖼️  Diamond Atelier - Evolution Image Optimization\n');
  
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  console.log('Choose an option:');
  console.log('1. Show compression guide');
  console.log('2. Apply temporary fix (replace with smaller image)');
  console.log('3. Both');
  console.log('');
  
  readline.question('Enter choice (1-3): ', (answer) => {
    console.log('');
    
    switch(answer) {
      case '1':
        compressEvolutionImage();
        break;
      case '2':
        createTemporaryFix();
        break;
      case '3':
        compressEvolutionImage();
        createTemporaryFix();
        break;
      default:
        console.log('Invalid choice. Showing compression guide...');
        compressEvolutionImage();
    }
    
    readline.close();
  });
}

if (require.main === module) {
  main();
}

module.exports = { compressEvolutionImage, createTemporaryFix };
