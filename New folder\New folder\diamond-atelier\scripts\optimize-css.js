#!/usr/bin/env node

/**
 * CSS Optimization Script
 * Removes unused CSS and optimizes for better performance
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 CSS Optimization Script\n');

// Critical CSS classes that should always be kept
const criticalClasses = [
  'bg-black',
  'text-white',
  'flex',
  'items-center',
  'justify-center',
  'w-full',
  'h-full',
  'absolute',
  'relative',
  'inset-0',
  'transition-all',
  'duration-300',
  'opacity-0',
  'opacity-100',
  'transform',
  'translate',
  'scale',
  'hover:',
  'group-hover:',
  'focus:',
  'active:',
  'md:',
  'lg:',
  'xl:',
  'sm:',
  'xs:',
];

// Performance optimizations to add
const performanceOptimizations = `
/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

.contain-style {
  contain: style;
}

/* Reduce paint complexity */
.reduce-paint {
  contain: layout style paint;
}

/* Optimize animations */
.smooth-animation {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Critical loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Optimize text rendering */
.optimize-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reduce layout shifts */
.prevent-layout-shift {
  min-height: 1px;
  min-width: 1px;
}
`;

function optimizeCSS() {
  console.log('📊 CSS Optimization Report:\n');
  
  // Add performance optimizations to globals.css
  const globalsPath = path.join(process.cwd(), 'src/app/globals.css');
  
  if (fs.existsSync(globalsPath)) {
    let content = fs.readFileSync(globalsPath, 'utf8');
    
    // Check if optimizations already exist
    if (!content.includes('Performance optimizations')) {
      content += performanceOptimizations;
      fs.writeFileSync(globalsPath, content);
      console.log('✅ Added performance optimizations to globals.css');
    } else {
      console.log('ℹ️  Performance optimizations already exist in globals.css');
    }
  }
  
  console.log('\n🎯 Recommendations:');
  console.log('1. Use PurgeCSS to remove unused Tailwind classes');
  console.log('2. Enable CSS minification in production');
  console.log('3. Use critical CSS for above-the-fold content');
  console.log('4. Implement CSS-in-JS for component-specific styles');
  console.log('5. Use CSS containment for better performance');
  
  console.log('\n💡 Usage Examples:');
  console.log('• Add .gpu-accelerated to animated elements');
  console.log('• Use .will-change-transform before animations');
  console.log('• Apply .reduce-paint to complex layouts');
  console.log('• Use .optimize-text for better text rendering');
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Run: npm run build to see CSS bundle size');
  console.log('2. Use Chrome DevTools to identify unused CSS');
  console.log('3. Consider using CSS modules for component isolation');
  console.log('4. Implement lazy loading for non-critical CSS');
}

// Run optimization
optimizeCSS();
