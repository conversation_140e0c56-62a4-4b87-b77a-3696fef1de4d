"use client";

import React, { useEffect, useState } from 'react';
import { motion } from "framer-motion";

const fadeRight = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const childVariant = {
  hidden: { opacity: 0, x: -50 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.6 },
  }
};

function AnimatedGrid({ children, className = "" }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch
  if (!mounted) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      variants={fadeRight}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: false, amount: 0.2 }}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          variants={childVariant}
          key={index}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

export default AnimatedGrid;
