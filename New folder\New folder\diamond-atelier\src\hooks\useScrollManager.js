"use client";
import { useState, useEffect, useRef, useCallback } from "react";

/**
 * Custom hook for managing scroll-based header state
 * 
 * This hook handles:
 * - Scroll position tracking with throttling for performance
 * - Logo visibility states based on scroll position and pathname
 * - Smooth transitions between different logo states
 * 
 * @param {string} pathname - Current page pathname
 * @returns {object} - Scroll state object with isScrolled, showBigLogo, showMobileLogo
 */
export const useScrollManager = (pathname) => {
  const [scrollState, setScrollState] = useState({
    isScrolled: false,
    showBigLogo: true,
    showMobileLogo: false,
  });
  
  const lastScrollY = useRef(0);
  const ticking = useRef(false);

  const updateScrollState = useCallback(() => {
    const currentScrollY = window.scrollY;
    const scrollThreshold = 50;
    
    const newState = {
      isScrolled: currentScrollY > scrollThreshold,
      showBigLogo: false,
      showMobileLogo: false,
    };

    if (pathname === "/") {
      // Homepage logic: show big logo only at top
      if (currentScrollY <= scrollThreshold) {
        newState.showBigLogo = true;
      }
    } else {
      // Other pages: show mobile logo when scrolled
      newState.showMobileLogo = currentScrollY > scrollThreshold;
    }

    setScrollState(newState);
    lastScrollY.current = currentScrollY;
    ticking.current = false;
  }, [pathname]);

  const handleScroll = useCallback(() => {
    if (!ticking.current) {
      requestAnimationFrame(updateScrollState);
      ticking.current = true;
    }
  }, [updateScrollState]);

  useEffect(() => {
    // Initialize state based on pathname
    if (pathname === "/") {
      setScrollState(prev => ({ ...prev, showBigLogo: true }));
      window.scrollTo(0, 0);
    }
  }, [pathname]);

  useEffect(() => {
    // Add scroll listener with passive option for better performance
    window.addEventListener("scroll", handleScroll, { passive: true });
    
    // Cleanup
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  return scrollState;
};
