"use client";
import { useEffect } from 'react';

/**
 * Performance Monitor Component
 * 
 * Monitors page performance and logs metrics to console
 * Only runs in development mode
 */
const PerformanceMonitor = ({ pageName = 'Unknown Page' }) => {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          console.log(`📊 ${pageName} Performance:`, {
            'DOM Content Loaded': `${entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart}ms`,
            'Load Complete': `${entry.loadEventEnd - entry.loadEventStart}ms`,
            'First Paint': `${entry.responseEnd - entry.requestStart}ms`,
            'Total Load Time': `${entry.loadEventEnd - entry.navigationStart}ms`
          });
        }
        
        if (entry.entryType === 'largest-contentful-paint') {
          console.log(`🎯 ${pageName} LCP:`, `${entry.startTime}ms`);
        }
        
        if (entry.entryType === 'first-input') {
          console.log(`⚡ ${pageName} FID:`, `${entry.processingStart - entry.startTime}ms`);
        }
        
        if (entry.entryType === 'layout-shift') {
          if (!entry.hadRecentInput) {
            console.log(`📐 ${pageName} CLS:`, entry.value);
          }
        }
      });
    });

    // Observe different performance metrics
    try {
      observer.observe({ entryTypes: ['navigation', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
    } catch (e) {
      // Fallback for browsers that don't support all entry types
      observer.observe({ entryTypes: ['navigation'] });
    }

    // Monitor scroll performance
    let scrollCount = 0;
    let scrollStart = 0;
    
    const scrollHandler = () => {
      if (scrollCount === 0) {
        scrollStart = performance.now();
      }
      scrollCount++;
      
      // Log scroll performance every 100 scroll events
      if (scrollCount % 100 === 0) {
        const scrollTime = performance.now() - scrollStart;
        console.log(`🖱️ ${pageName} Scroll Performance:`, {
          'Events': scrollCount,
          'Time': `${scrollTime}ms`,
          'Events/sec': Math.round(scrollCount / (scrollTime / 1000))
        });
      }
    };

    window.addEventListener('scroll', scrollHandler, { passive: true });

    // Monitor memory usage (if available)
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = performance.memory;
        console.log(`💾 ${pageName} Memory:`, {
          'Used': `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
          'Total': `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
          'Limit': `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`
        });
      }
    };

    // Check memory every 10 seconds
    const memoryInterval = setInterval(checkMemory, 10000);
    checkMemory(); // Initial check

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', scrollHandler);
      clearInterval(memoryInterval);
    };
  }, [pageName]);

  // This component doesn't render anything
  return null;
};

export default PerformanceMonitor;
