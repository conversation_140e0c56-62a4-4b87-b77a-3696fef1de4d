"use client";
import React, { useState, useEffect, useRef } from 'react';

const OneTimeOnlyCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    // Create unique key for this counter
    const counterKey = `onetime_counter_${targetNumber}_${suffix.replace(/[^a-zA-Z0-9]/g, '_')}`;
    
    // Check if page was refreshed using performance API
    const isPageRefresh = performance.getEntriesByType('navigation')[0]?.type === 'reload' ||
                         performance.navigation?.type === 1;
    
    // If page was refreshed, clear all counter data
    if (isPageRefresh) {
      // Clear all counter-related session storage
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('onetime_counter_')) {
          sessionStorage.removeItem(key);
        }
      });
    }
    
    // Check if this counter has already animated
    if (sessionStorage.getItem(counterKey)) {
      // Already animated - show final number immediately
      setCount(targetNumber);
      setHasAnimated(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setHasAnimated(true);
          
          // Mark this counter as animated
          sessionStorage.setItem(counterKey, 'animated');
          
          // Start counting animation
          const steps = 60;
          const stepValue = targetNumber / steps;
          const stepDelay = duration / steps;
          
          let currentStep = 0;
          
          const timer = setInterval(() => {
            currentStep++;
            const newCount = Math.floor(currentStep * stepValue);
            
            if (currentStep >= steps) {
              setCount(targetNumber);
              clearInterval(timer);
            } else {
              setCount(newCount);
            }
          }, stepDelay);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [targetNumber, duration, suffix, hasAnimated]);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit'
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

export default OneTimeOnlyCounter;
