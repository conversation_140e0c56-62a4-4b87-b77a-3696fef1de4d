"use client";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { FaGem, FaRulerCombined, FaChartLine, FaCube } from "react-icons/fa";
import Image from "next/image";
import LoadingScreen from "../../utils/LoadingScreen";

export default function ClientCalibratedLayoutsPage({ diamondShapes}) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1300);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <LoadingScreen />;

  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: "easeOut" } }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const shapeIcons = {
    "Emerald": FaGem,
    "Oval": FaCube,
    "Marquise": FaRulerCombined,
    "Pear": FaChartLine
  };

  return (
    <div className="min-h-screen bg-black overflow-x-hidden">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Video or Image */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/70 to-black"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.h1
              variants={fadeInUp}
              className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light text-white mb-8 tracking-[0.2em] uppercase"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              MATCHING LAYOUTS
            </motion.h1>

            <motion.div
              variants={fadeInUp}
              className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"
            ></motion.div>

            <motion.p
              variants={fadeInUp}
              className="text-base md:text-lg text-white/90 font-light max-w-4xl mx-auto tracking-wide leading-relaxed"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              Calibrated consistency from 10–99 cents. Our comprehensive collection of calibrated matching layouts ranges from 10 to 99 cents, with a 0.10 MM tolerance. Whether you seek uniformity in shapes, colors, clarities, dimensions, or cent values, trust us to deliver precisely what you need.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Diamond Shapes Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              DIAMOND SHAPE COLLECTIONS
            </motion.h2>
            <motion.div
              variants={fadeInUp}
              className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"
            ></motion.div>
            <motion.p
              variants={fadeInUp}
              className="text-sm text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              Precisely calibrated diamond layouts available in multiple shapes and sizes for your jewelry designs.
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="space-y-16"
          >
            {diamondShapes.map((shapeData, index) => {
              return (
                <motion.div
                  key={shapeData.id}
                  variants={fadeInUp}
                  className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20"
                >
                  <div className="text-center mb-12">
                    <h3 className="text-xl lg:text-2xl xl:text-3xl font-light text-white mb-4 tracking-[0.2em] uppercase"
                        style={{ fontFamily: 'Times New Roman, serif' }}>
                      {shapeData.shape}
                    </h3>
                    <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"></div>
                  </div>

                  <div className="space-y-8">
                    {shapeData.variants.map((variant, variantIndex) => (
                      <motion.div
                        key={variantIndex}
                        whileHover={{ scale: 1.01, y: -2 }}
                        className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-white/30 transition-all duration-500 group"
                      >
                        <div className="flex flex-col lg:flex-row items-center gap-6">
                          {/* Text Section */}
                          <div className="lg:w-1/4 text-center lg:text-left flex-shrink-0">
                            <h4 className="text-xl lg:text-2xl font-light text-white mb-2 tracking-wide uppercase"
                                style={{ fontFamily: 'Times New Roman, serif' }}>
                              {variant.size}
                            </h4>
                            <div className="h-px w-12 bg-gradient-to-r from-transparent via-white to-transparent mx-auto lg:mx-0 mb-2"></div>
                            <p className="text-base text-white/80 font-light tracking-wide"
                               style={{ fontFamily: 'Times New Roman, serif' }}>
                              {variant.pointer}
                            </p>
                          </div>

                          {/* Diamond Image Section - Full Width */}
                          <div className="lg:w-3/4 w-full">
                            <div className="relative h-32 lg:h-40 rounded-lg overflow-hidden bg-black/20 flex items-center justify-center group-hover:bg-black/30 transition-all duration-500">
                              <Image
                                src={variant.image}
                                alt={`${shapeData.shape} diamond - ${variant.size}`}
                                width={800}
                                height={300}
                                className="object-contain filter brightness-110 group-hover:brightness-125 transition-all duration-500 w-full h-full"
                                unoptimized={true}
                                priority={true}
                              />
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              WHY CHOOSE OUR CALIBRATED LAYOUTS?
            </motion.h2>
            <motion.div
              variants={fadeInUp}
              className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"
            ></motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              { title: "Precision Matching", description: "Exact size calibration for perfect jewelry fits" },
              { title: "Quality Assurance", description: "Each diamond meets our strict quality standards" },
              { title: "Multiple Sizes", description: "Wide range of sizes from 10 cent to 99 cent" },
              { title: "Professional Service", description: "Expert consultation and technical support" }
            ].map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500 text-center"
              >
                <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase"
                    style={{ fontFamily: 'Times New Roman, serif' }}>
                  {feature.title}
                </h3>
                <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-4"></div>
                <p className="text-xs text-white/80 font-light leading-relaxed tracking-wide"
                   style={{ fontFamily: 'Times New Roman, serif' }}>
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="bg-black/20 backdrop-blur-sm rounded-lg p-12 border border-white/20"
          >
            <h2 className="text-xl lg:text-2xl xl:text-3xl font-light text-white mb-6 tracking-[0.2em] uppercase"
                style={{ fontFamily: 'Times New Roman, serif' }}>
              NEED CUSTOM CALIBRATED LAYOUTS?
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-sm text-white/90 font-light leading-relaxed max-w-3xl mx-auto tracking-wide mb-8"
               style={{ fontFamily: 'Times New Roman, serif' }}>
              Contact our experts to discuss your specific requirements and get perfectly matched diamond layouts for your jewelry designs.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push('/contact')}
              className="group relative px-6 py-3 bg-white border border-gray-300 text-black font-light uppercase tracking-[0.15em] text-sm transition-all duration-300 hover:bg-gray-100 hover:border-gray-400 shadow-sm hover:shadow-md overflow-hidden cursor-pointer"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              <span className="relative z-10">CONTACT US TODAY</span>
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
