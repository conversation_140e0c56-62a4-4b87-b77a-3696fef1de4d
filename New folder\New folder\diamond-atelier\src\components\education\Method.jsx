

// "use client";
// import Image from "next/image";
// import React, { useState } from "react";
// import { motion } from "framer-motion";
// import cvd from "../../../public/images/about/cvd.jpeg";
// import hpht from "../../../public/images/about/hpht.jpeg";

// const fadeInUp = {
//   hidden: { opacity: 0, y: 30 },
//   visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
// };

// const listItemVariant = {
//   hidden: { opacity: 0, x: -20 },
//   visible: (i) => ({
//     opacity: 1,
//     x: 0,
//     transition: { delay: i * 0.15, duration: 0.5 },
//   }),
// };

// const hphtSteps = [
//   "Diamond seed is placed in a specifically designed press.",
//   "The growth chamber is heated to 1300-1600 °C with pressures above 870,000 pounds per square inch.",
//   "The molten metal dissolves the high purity carbon source.",
//   "Carbon atoms precipitate on a small diamond seed crystal, and a synthetic diamond begins to grow.",
//   "The lab-grown crystal is then cut and polished by a diamond cutter.",
// ];

// const cvdSteps = [
//   "Diamond seed crystals are placed in a diamond growth chamber filled with carbon-containing gas.",
//   "The chamber is heated to about 900-1200°C.",
//   "A microwave beam causes carbon to precipitate out of a plasma cloud and deposit onto a seed crystal.",
//   "Diamonds are removed every few days to polish off non-diamond carbon.",
//   "The synthetic diamonds are then cut and polished into final products.",
// ];

// const methodInfo = {
//   HPHT: { title: "HPHT", img: hpht, steps: hphtSteps },
//   CVD: { title: "CVD", img: cvd, steps: cvdSteps },
// };

// const Method = () => {
//   const [activeMethod, setActiveMethod] = useState("HPHT");

//   const { title, img, steps } = methodInfo[activeMethod];

//   return (
//     <div className="bg-black text-white min-h-screen flex flex-col items-center">
//       {/* Heading & Tabs */}
//       <section className="pt-32 pb-12 px-6 max-w-6xl mx-auto text-center">
//         <motion.h1
//           initial={{ opacity: 0, y: -30 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.8 }}
//           viewport={{ once: true, amount: 0.2 }}
//           className="uppercase font-semibold font-montserrat text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl 3xl:text-8xl 4k:text-9xl tracking-widest drop-shadow-lg"
//         >
//           Methods of Growing Lab Grown Diamonds
//         </motion.h1>

//         <div className="flex justify-center gap-4 sm:gap-6 mt-6 flex-wrap">
//           {Object.keys(methodInfo).map((method) => (
//             <motion.button
//               key={method}
//               onClick={() => setActiveMethod(method)}
//               whileHover={{ scale: 1.1 }}
//               className={`px-6 py-2 sm:px-10 sm:py-3 lg:px-12 lg:py-4 xl:px-16 xl:py-6 2xl:px-20 2xl:py-8 rounded-full text-sm sm:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl 3xl:text-4xl font-semibold focus:outline-none transition-all duration-300
//                 ${
//                   activeMethod === method
//                     ? "bg-gradient-to-r from-indigo-500 via-purple-600 to-pink-500 shadow-lg shadow-pink-500/40"
//                     : "bg-gray-700 hover:bg-gray-600"
//                 }`}
//             >
//               {method}
//             </motion.button>
//           ))}
//         </div>
//       </section>


//       <section className="bg-gradient-to-br from-gray-50 to-gray-200 text-black p-4 sm:p-8 max-w-xl mx-auto rounded-xl shadow-xl mt-5 mb-12 w-full">
//         <motion.div
//           initial="hidden"
//           whileInView="visible"
//           viewport={{ once: true }}
//           variants={fadeInUp}
//           className="flex flex-col md:flex-row justify-center items-start gap-4 md:gap-8"
//         >
//           <div className="relative pt-10 bg-white rounded-2xl shadow-xl flex-1 flex flex-col items-center hover:shadow-2xl hover:scale-[1.02] transition duration-500">
         
//             <motion.div
//               className="absolute -top-16 sm:-top-20 left-1/2 transform -translate-x-1/2 w-32 h-32 sm:w-44 sm:h-44 rounded-full border-8 border-white overflow-hidden shadow-2xl"
//               animate={{
//                 scale: 1.05,
//                 boxShadow: "0 8px 30px rgba(131, 58, 180, 0.6)",
//               }}
//               transition={{ duration: 0.4 }}
//             >
//               <Image
//                 src={img}
//                 alt={`${title} method`}
//                 className="object-cover w-full h-full"
//                 priority
//               />
//             </motion.div>

//             {/* Title */}
//             <h4 className="text-gray-800 font-extrabold text-xl sm:text-xl md:text-xl uppercase mt-16 mb-8 font-montserrat tracking-wide text-center px-4">
//               {title}
//             </h4>

//             {/* Steps List */}
//             <ul className="list-disc pl-8 sm:pl-14 pr-6 sm:pr-8 mb-3 text-gray-700 font-montserrat text-base sm:text-lg space-y-2 max-w-lg">
//               {steps.map((step, i) => (
//                 <motion.li
//                   key={i}
//                   custom={i}
//                   variants={listItemVariant}
//                   initial="hidden"
//                   whileInView="visible"
//                   viewport={{ once: true }}
//                   whileHover={{ scale: 1.03, color: "#7C3AED" }}
//                   transition={{ type: "spring", stiffness: 300 }}
//                 >
//                   {step}
//                 </motion.li>
//               ))}
//             </ul>
//           </div>
//         </motion.div>
//       </section>
//     </div>
//   );
// };

// export default Method;




"use client";
import Image from "next/image";
import React, { useState } from "react";
import { motion } from "framer-motion";
import OptimizedImage from "@/components/common/OptimizedImage";
import cvd from "../../../public/images/about/cvd.jpeg";
import hpht from "../../../public/images/about/hpht.jpeg";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

const listItemVariant = {
  hidden: { opacity: 0, x: -20 },
  visible: (i) => ({
    opacity: 1,
    x: 0,
    transition: { delay: i * 0.15, duration: 0.5 },
  }),
};

const hphtSteps = [
  "Diamond seed is placed in a specifically designed press.",
  "The growth chamber is heated to 1300-1600 °C with pressures above 870,000 pounds per square inch.",
  "The molten metal dissolves the high purity carbon source.",
  "Carbon atoms precipitate on a small diamond seed crystal, and a synthetic diamond begins to grow.",
  "The lab-grown crystal is then cut and polished by a diamond cutter.",
];

const cvdSteps = [
  "Diamond seed crystals are placed in a diamond growth chamber filled with carbon-containing gas.",
  "The chamber is heated to about 900-1200°C.",
  "A microwave beam causes carbon to precipitate out of a plasma cloud and deposit onto a seed crystal.",
  "Diamonds are removed every few days to polish off non-diamond carbon.",
  "The synthetic diamonds are then cut and polished into final products.",
];

const methodInfo = {
  HPHT: { title: "HPHT", img: hpht, steps: hphtSteps },
  CVD: { title: "CVD", img: cvd, steps: cvdSteps },
};

const Method = () => {
  const [activeMethod, setActiveMethod] = useState("HPHT");

  const { title, img, steps } = methodInfo[activeMethod];

  return (
    <div className="bg-black text-white min-h-screen" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Heading & Tabs */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-black"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-light text-white mb-8 tracking-[0.2em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              METHODS OF GROWING LAB GROWN DIAMONDS
            </h1>
            <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-lg md:text-xl text-white/90 mb-6 font-light max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Discover the advanced technologies used to create lab-grown diamonds
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="flex justify-center gap-4 mt-12 flex-wrap"
          >
            {Object.keys(methodInfo).map((method) => (
              <motion.button
                key={method}
                onClick={() => setActiveMethod(method)}
                whileHover={{ scale: 1.05 }}
                className={`px-8 py-4 rounded-lg text-base font-light tracking-wide focus:outline-none transition-all duration-500 border uppercase ${
                  activeMethod === method
                    ? "bg-white text-black border-white"
                    : "bg-black/20 backdrop-blur-sm text-white border-white/20 hover:border-white/40 hover:bg-white/10"
                }`}
                style={{ fontFamily: 'Times New Roman, serif' }}
              >
                {method}
              </motion.button>
            ))}
          </motion.div>
        </div>
      </section>


      {/* Method Content Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-black/20 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-white/20"
          >
            <div className="flex flex-col lg:flex-row items-center gap-12">
              {/* Image Section */}
              <div className="flex-1 flex justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="relative w-64 h-64 md:w-80 md:h-80 rounded-lg overflow-hidden border border-white/20"
                >
                  <OptimizedImage
                    src={img}
                    alt={`${title} method`}
                    width={320}
                    height={320}
                    className="object-cover w-full h-full"
                    priority={false}
                  />
                </motion.div>
              </div>

              {/* Content Section */}
              <div className="flex-1">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="text-3xl md:text-4xl font-light text-white mb-6 tracking-[0.15em] uppercase text-center lg:text-left"
                  style={{ fontFamily: 'Times New Roman, serif' }}
                >
                  {title} METHOD
                </motion.h2>

                <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto lg:mx-0 mb-8"></div>

                {/* Steps List */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-xl font-light text-white mb-6 tracking-wide uppercase text-center lg:text-left" style={{ fontFamily: 'Times New Roman, serif' }}>
                    Process Steps
                  </h3>
                  <ul className="space-y-4">
                    {steps.map((step, i) => (
                      <motion.li
                        key={i}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: i * 0.1 }}
                        viewport={{ once: true }}
                        className="flex items-start space-x-4"
                      >
                        <span className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center text-white text-sm font-light" style={{ fontFamily: 'Times New Roman, serif' }}>
                          {i + 1}
                        </span>
                        <p className="text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                          {step}
                        </p>
                      </motion.li>
                    ))}
                  </ul>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Method;
