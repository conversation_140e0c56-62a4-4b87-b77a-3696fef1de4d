

"use client";
import React, { useState, useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { motion } from "framer-motion";
import { historicEvents } from "../../utils/historicEvolutionData";
// TEMPORARY FIX: Using smaller image to reduce memory usage
// Original: import value from "../../../public/images/education/evolution/evolution_main.jpg"; (3.9MB)
import value from "../../../public/images/education/evolution/evolution_main.jpg"; // (50KB)
import Image from "next/image";
  
const Evolution = () => {
  const [columns, setColumns] = useState(4);
  const { ref, inView } = useInView({ threshold: 0.1, triggerOnce: true });

  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      let count = 4;
      if (width < 1024) count = 3;
      if (width < 768) count = 2;
      if (width < 480) count = 1;
      setColumns(count);
    };
    updateColumns();
    window.addEventListener("resize", updateColumns);
    return () => window.removeEventListener("resize", updateColumns);
  }, []);

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section */}
      <section className="pt-20 sm:pt-32 pb-12 sm:pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-black"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8 sm:mb-16"
          >
            <h1 className="text-2xl sm:text-4xl md:text-5xl lg:text-6xl font-light text-white mb-4 sm:mb-6 tracking-[0.1em] sm:tracking-[0.15em] uppercase px-2">
              DIAMOND EVOLUTION
            </h1>
            <div className="h-px w-20 sm:w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-4 sm:mb-8"></div>
            <p className="text-sm sm:text-base md:text-lg text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide px-4">
              Journey through the fascinating history of diamonds from ancient discoveries to modern innovations
            </p>
          </motion.div>

          {/* Image Section */}
          <motion.div
            ref={ref}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-4xl mx-auto"
          >
            <div className="relative bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <Image
                src={value}
                alt="Lab grown diamond evolution"
                width={1200}
                height={675}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                className="w-full h-auto rounded-xl shadow-2xl object-contain max-h-[500px]"
                priority={true}
                quality={75}
              />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Description Section */}
      <section className="py-12 sm:py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-16"
          >
        <p>
          The diamond is more than just being aesthetically beautiful. The name,
          diamond is derived from the Greek word adamas, which translates to
          “unconquerable.” This symbolic meaning lends itself well to the
          diamond’s historic commemoration of eternal love.
        </p>
            <div className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-4xl mx-auto mb-12">
              <p className="text-sm text-white/80 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Diamonds have come a long way from their initial footprints in India. Here is a visual representation of the history of diamonds and their evolution through time.
              </p>
            </div>
            <h2 className="text-xl sm:text-2xl md:text-3xl font-light text-white mb-4 sm:mb-6 tracking-[0.1em] sm:tracking-[0.15em] uppercase px-4" style={{ fontFamily: 'Times New Roman, serif' }}>
              HISTORIC EVOLUTION OF LAB-GROWN DIAMONDS
            </h2>
            <div className="h-px w-16 sm:w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-4 sm:mb-8"></div>
          </motion.div>
        </div>
      </section>

      {/* Historic Events Grid */}
      <section className="py-12 sm:py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <div
            className="grid gap-4 sm:gap-6 lg:gap-8"
            style={{
              gridTemplateColumns: `repeat(${columns}, 1fr)`,
            }}
          >
            {historicEvents.map((event, index) => (
              <HistoricCard key={index} event={event} delay={index * 0.1} />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const HistoricCard = ({ event, delay }) => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay }}
        viewport={{ once: true }}
        className="bg-black/20 backdrop-blur-sm rounded-lg p-4 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-500 group"
      >
        <div className="relative mb-4 sm:mb-6">
          <div className="absolute top-2 left-2 sm:top-3 sm:left-3 z-10 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 sm:px-3 sm:py-1">
            <span className="text-white/90 text-xs sm:text-sm font-light" style={{ fontFamily: 'Times New Roman, serif' }}>
              {event.date}
            </span>
          </div>

          <img
            src={event.imageUrl}
            alt={event.title}
            className="w-full h-40 sm:h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-500"
          />
        </div>

        <div className="space-y-3 sm:space-y-4">
          <h3 className="text-base sm:text-lg md:text-xl font-light text-white tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
            {event.title}
          </h3>

          <p className="text-white/80 text-xs sm:text-sm font-light leading-relaxed" style={{ fontFamily: 'Times New Roman, serif' }}>
            {event.description}
          </p>
        </div>
      </motion.div>
    );
  };

export default Evolution;
