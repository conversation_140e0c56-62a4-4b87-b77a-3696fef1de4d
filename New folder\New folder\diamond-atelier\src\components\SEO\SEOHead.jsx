"use client";
import Head from 'next/head';

const SEOHead = ({ 
  title, 
  description, 
  keywords = [], 
  ogImage = '/images/og-default.jpg',
  canonicalUrl,
  structuredData,
  noIndex = false 
}) => {
  const defaultTitle = "Diamond Atelier - Premium Lab Grown Diamonds";
  const defaultDescription = "Discover premium lab-grown diamonds at Diamond Atelier. Certified, ethical, and exceptional quality diamonds.";
  
  const seoTitle = title ? `${title} | Diamond Atelier` : defaultTitle;
  const seoDescription = description || defaultDescription;
  const keywordString = keywords.length > 0 ? keywords.join(', ') : 'lab grown diamonds, synthetic diamonds, ethical diamonds';

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{seoTitle}</title>
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content={keywordString} />
      <meta name="author" content="Diamond Atelier" />
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? "noindex,nofollow" : "index,follow"} />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="Diamond Atelier" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={seoTitle} />
      <meta name="twitter:description" content={seoDescription} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
      
      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    </Head>
  );
};

export default SEOHead;
