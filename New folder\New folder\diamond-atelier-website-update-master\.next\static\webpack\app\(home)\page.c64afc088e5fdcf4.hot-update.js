"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/page",{

/***/ "(app-pages-browser)/./src/components/common/SimpleHome.jsx":
/*!**********************************************!*\
  !*** ./src/components/common/SimpleHome.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AnimatedHeading */ \"(app-pages-browser)/./src/components/common/AnimatedHeading.jsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sections = [\n    // {\n    //   id: \"hero\",\n    //   title: \"Exquisite Lab Grown Diamonds\",\n    //   subtitle: \"Every Diamond Tells a Story\",\n    //   description:\n    //     \"Ethically sourced, certified diamonds with unparalleled quality and brilliance.\",\n    //   image: \"/image/tache_diamond_rough_cut.png\",\n    //   cta: \"Discover Our Collection\",\n    // },\n    {\n        id: \"shapes\",\n        title: \"Iconic Diamond Cuts\",\n        subtitle: \"Timeless Elegance, Modern Precision\",\n        description: \"From the classic brilliance of round cuts to the contemporary allure of emerald and cushion shapes. Each cut is masterfully executed to maximize fire, brilliance, and scintillation.\",\n        image: \"/image/.jpg\",\n        cta: \"Discover Shapes\"\n    },\n    {\n        id: \"craftsmanship\",\n        title: \"Artisanal Excellence\",\n        subtitle: \"Heritage Craftsmanship, Revolutionary Technology\",\n        description: \"Our master craftsmen blend centuries-old techniques with cutting-edge technology, creating diamonds that rival nature's finest. Every facet is precisely cut to achieve maximum optical performance and breathtaking beauty.\",\n        image: \"/image/shaping_phase_tache-768x768.png\",\n        cta: \"View Our Process\"\n    },\n    {\n        id: \"colors\",\n        title: \"Rare Fancy Colors\",\n        subtitle: \"Nature's Spectrum, Perfected\",\n        description: \"Experience the extraordinary with our collection of fancy colored diamonds. From vivid yellows to rare pinks and blues, each colored diamond represents the pinnacle of gemological achievement.\",\n        image: \"/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png\",\n        cta: \"Explore Colors\"\n    }\n];\nfunction SimpleHome() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHome.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SimpleHome.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"full-section relative w-full h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png\",\n                        alt: \"Diamond Atelier Homepage Banner\",\n                        fill: true,\n                        className: \"absolute inset-0 w-full h-full object-cover\",\n                        priority: true,\n                        quality: 90\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full opacity-60\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight,\n                                    scale: 0\n                                },\n                                animate: {\n                                    y: [\n                                        null,\n                                        -100\n                                    ],\n                                    opacity: [\n                                        0.6,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 3,\n                                    ease: \"easeOut\"\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col items-center justify-center z-20 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat\",\n                                            style: {\n                                                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',\n                                                letterSpacing: \"0.1em\"\n                                            },\n                                            children: \"DIAMONDS THAT DESERVE YOU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 0.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center max-w-4xl mx-auto mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.3em] leading-relaxed font-montserrat\",\n                                        children: [\n                                            \" \",\n                                            \"10,000+ Certified Stones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg lg:text-xl text-black/70 font-light tracking-wide leading-relaxed font-montserrat\",\n                                        children: \"Where precision meets perfection in every facet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 1,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/shapes\",\n                                        className: \"group relative px-8 py-3 bg-Black border border-white/30 text-Black hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"EXPlORE SHAPES\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-Black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"https://inventory.diamondatelier.in/\",\n                                        className: \"group relative px-8 py-3 bg-black/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: \"VIEW INVENTORY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            delay: 1.5\n                        },\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                        onClick: ()=>setCurrentIndex(1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs tracking-widest mb-2 font-light\",\n                                    children: \"SCROLL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 bg-gradient-to-br from-gray-50 to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center justify-between gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-black\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                children: sections[0].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: sections[0].title,\n                                            level: \"h1\",\n                                            className: \"text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat\",\n                                            showUnderline: false,\n                                            animationDelay: 0.2,\n                                            triggerOnMount: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat\",\n                                            children: sections[0].description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/shapes\",\n                                            className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                            children: sections[0].cta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-64 mb-6 overflow-hidden rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: sections[0].image,\n                                                        alt: sections[0].title,\n                                                        fill: true,\n                                                        className: \"object-cover\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-light mb-4 font-montserrat\",\n                                                            children: \"Premium Diamond Cuts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 font-light font-montserrat\",\n                                                            children: \"Masterfully crafted for maximum brilliance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                        children: sections[0].subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    text: sections[0].title,\n                                    level: \"h1\",\n                                    className: \"text-4xl sm:text-5xl mb-8 font-light leading-tight font-montserrat\",\n                                    showUnderline: false,\n                                    animationDelay: 0.2,\n                                    triggerOnMount: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-xl p-6 max-w-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 mb-4 overflow-hidden rounded-xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: sections[0].image,\n                                                    alt: sections[0].title,\n                                                    fill: true,\n                                                    className: \"object-cover\",\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-light mb-2 font-montserrat\",\n                                                children: \"Premium Diamond Cuts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm font-light font-montserrat\",\n                                                children: \"Masterfully crafted for maximum brilliance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                    children: sections[0].description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/shapes\",\n                                    className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                    children: sections[0].cta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            sections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 \".concat(index % 2 === 0 ? 'bg-white' : 'bg-gray-50'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:grid lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-black \".concat(index % 2 === 0 ? 'order-2' : 'order-1'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                    children: section.subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl xl:text-5xl mb-6 font-light leading-tight font-montserrat\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 font-montserrat\",\n                                                children: section.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                        children: section.cta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                        children: \"Learn More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(index % 2 === 0 ? 'order-1' : 'order-2'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-96 overflow-hidden rounded-2xl shadow-2xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 max-w-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-medium mb-2 font-montserrat\",\n                                                            children: [\n                                                                section.id === 'shapes' && 'Premium Cuts',\n                                                                section.id === 'craftsmanship' && 'Expert Crafted',\n                                                                section.id === 'colors' && 'Rare Colors'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 font-light font-montserrat\",\n                                                            children: [\n                                                                section.id === 'shapes' && 'Round, Princess, Emerald & More',\n                                                                section.id === 'craftsmanship' && 'Traditional meets Technology',\n                                                                section.id === 'colors' && 'Yellow, Pink, Blue & Beyond'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden text-center text-black\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                            children: section.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl mb-6 font-light leading-tight font-montserrat\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-64 overflow-hidden rounded-2xl shadow-xl mx-auto max-w-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: section.image,\n                                                    alt: section.title,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-4 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                children: section.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                children: \"Learn More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this)\n                }, section.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative w-full h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"url('/image/Expert.jpg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/40 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 drop-shadow-lg font-montserrat\",\n                                        children: \"Expert Eyes on Every Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-black mb-8 leading-relaxed drop-shadow-md font-montserrat\",\n                                        children: \"Our in-house gemologists personally inspect and verify every lab-grown diamond. You'll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHome, \"2oCXE2WiAW05zG4oFnHELxlAGiA=\");\n_c = SimpleHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbW1vbi9TaW1wbGVIb21lLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDMkM7QUFDWjtBQUNpQjtBQUNMO0FBQ2Y7QUFDc0I7QUFFbEQsTUFBTVEsV0FBVztJQUNmLElBQUk7SUFDSixnQkFBZ0I7SUFDaEIsMkNBQTJDO0lBQzNDLDZDQUE2QztJQUM3QyxpQkFBaUI7SUFDakIseUZBQXlGO0lBQ3pGLGlEQUFpRDtJQUNqRCxvQ0FBb0M7SUFDcEMsS0FBSztJQUNMO1FBQ0VDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLGFBQ0U7UUFDRkMsT0FBTztRQUNQQyxLQUFLO0lBQ1A7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxhQUNFO1FBQ0ZDLE9BQU87UUFDUEMsS0FBSztJQUNQO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsYUFDRTtRQUNGQyxPQUNFO1FBQ0ZDLEtBQUs7SUFDUDtDQUNEO0FBRWMsU0FBU0M7O0lBQ3RCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUV6Q0MsZ0RBQVNBO2dDQUFDO1lBQ1JrQixZQUFZO1FBQ2Q7K0JBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDbkIsa0RBQUtBO3dCQUNKb0IsS0FBSTt3QkFDSkMsS0FBSTt3QkFDSkMsSUFBSTt3QkFDSkgsV0FBVTt3QkFDVkksUUFBUTt3QkFDUkMsU0FBUzs7Ozs7O2tDQUlYLDhEQUFDTjt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O29CQUdkSCwwQkFDQyw4REFBQ0U7d0JBQUlDLFdBQVU7a0NBQ1o7K0JBQUlNLE1BQU07eUJBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUN0Qiw4REFBQ3hCLGlEQUFNQSxDQUFDYyxHQUFHO2dDQUVUQyxXQUFVO2dDQUNWVSxTQUFTO29DQUNQQyxHQUFHQyxLQUFLQyxNQUFNLEtBQUtDLE9BQU9DLFVBQVU7b0NBQ3BDQyxHQUFHSixLQUFLQyxNQUFNLEtBQUtDLE9BQU9HLFdBQVc7b0NBQ3JDQyxPQUFPO2dDQUNUO2dDQUNBQyxTQUFTO29DQUNQSCxHQUFHO3dDQUFDO3dDQUFNLENBQUM7cUNBQUk7b0NBQ2ZJLFNBQVM7d0NBQUM7d0NBQUs7cUNBQUU7b0NBQ2pCRixPQUFPO3dDQUFDO3dDQUFHO3dDQUFHO3FDQUFFO2dDQUNsQjtnQ0FDQUcsWUFBWTtvQ0FDVkMsVUFBVSxJQUFJVixLQUFLQyxNQUFNLEtBQUs7b0NBQzlCVSxRQUFRQztvQ0FDUkMsT0FBT2IsS0FBS0MsTUFBTSxLQUFLO29DQUN2QmEsTUFBTTtnQ0FDUjsrQkFqQktqQjs7Ozs7Ozs7OztrQ0F3QmIsOERBQUNWO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ2YsaURBQU1BLENBQUNjLEdBQUc7Z0NBQ1RXLFNBQVM7b0NBQUVVLFNBQVM7b0NBQUdKLEdBQUc7Z0NBQUc7Z0NBQzdCRyxTQUFTO29DQUFFQyxTQUFTO29DQUFHSixHQUFHO2dDQUFFO2dDQUM1QkssWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBS0ksTUFBTTtnQ0FBVTtnQ0FDN0MxQixXQUFVOzBDQUVWLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMyQjs0Q0FDQzNCLFdBQVU7NENBQ1Y0QixPQUFPO2dEQUNMLGdGQUFnRjtnREFDaEZDLGVBQWU7NENBQ2pCO3NEQUNEOzs7Ozs7c0RBS0QsOERBQUM5Qjs0Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS25CLDhEQUFDZixpREFBTUEsQ0FBQ2MsR0FBRztnQ0FDVFcsU0FBUztvQ0FBRVUsU0FBUztvQ0FBR0osR0FBRztnQ0FBRztnQ0FDN0JHLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdKLEdBQUc7Z0NBQUU7Z0NBQzVCSyxZQUFZO29DQUFFQyxVQUFVO29DQUFLRyxPQUFPO29DQUFLQyxNQUFNO2dDQUFVO2dDQUN6RDFCLFdBQVU7O2tEQUVWLDhEQUFDMkI7d0NBQUczQixXQUFVOzs0Q0FLTDs0Q0FBSTs7Ozs7OztrREFHYiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQzhCO3dDQUFFOUIsV0FBVTtrREFBb0g7Ozs7Ozs7Ozs7OzswQ0FNbkksOERBQUNmLGlEQUFNQSxDQUFDYyxHQUFHO2dDQUNUVyxTQUFTO29DQUFFVSxTQUFTO29DQUFHSixHQUFHO2dDQUFHO2dDQUM3QkcsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0osR0FBRztnQ0FBRTtnQ0FDNUJLLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUtHLE9BQU87b0NBQUdDLE1BQU07Z0NBQVU7Z0NBQ3ZEMUIsV0FBVTs7a0RBRVYsOERBQUNoQixrREFBSUE7d0NBQ0grQyxNQUFLO3dDQUNML0IsV0FBVTs7MERBRVYsOERBQUNnQztnREFBS2hDLFdBQVU7MERBQWdCOzs7Ozs7MERBQ2hDLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUdqQiw4REFBQ2hCLGtEQUFJQTt3Q0FDSCtDLE1BQUs7d0NBQ0wvQixXQUFVO2tEQUVWLDRFQUFDZ0M7NENBQUtoQyxXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXRDLDhEQUFDZixpREFBTUEsQ0FBQ2MsR0FBRzt3QkFDVFcsU0FBUzs0QkFBRVUsU0FBUzt3QkFBRTt3QkFDdEJELFNBQVM7NEJBQUVDLFNBQVM7d0JBQUU7d0JBQ3RCQyxZQUFZOzRCQUFFQyxVQUFVOzRCQUFLRyxPQUFPO3dCQUFJO3dCQUN4Q3pCLFdBQVU7d0JBQ1ZpQyxTQUFTLElBQU1DLGdCQUFnQjtrQ0FFL0IsNEVBQUNqRCxpREFBTUEsQ0FBQ2MsR0FBRzs0QkFDVG9CLFNBQVM7Z0NBQUVILEdBQUc7b0NBQUM7b0NBQUc7b0NBQUc7aUNBQUU7NEJBQUM7NEJBQ3hCSyxZQUFZO2dDQUFFQyxVQUFVO2dDQUFHQyxRQUFRQztnQ0FBVUUsTUFBTTs0QkFBWTs0QkFDL0QxQixXQUFVOzs4Q0FFViw4REFBQ2dDO29DQUFLaEMsV0FBVTs4Q0FBMEM7Ozs7Ozs4Q0FHMUQsOERBQUNqQix1RkFBV0E7b0NBQUNpQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLN0IsOERBQUNtQztnQkFBUW5DLFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2dDO2dEQUFLaEMsV0FBVTswREFDYmIsUUFBUSxDQUFDLEVBQUUsQ0FBQ0csUUFBUTs7Ozs7Ozs7Ozs7c0RBR3pCLDhEQUFDUix3REFBZUE7NENBQ2RzRCxNQUFNakQsUUFBUSxDQUFDLEVBQUUsQ0FBQ0UsS0FBSzs0Q0FDdkJnRCxPQUFNOzRDQUNOckMsV0FBVTs0Q0FDVnNDLGVBQWU7NENBQ2ZDLGdCQUFnQjs0Q0FDaEJDLGdCQUFnQjs7Ozs7O3NEQUVsQiw4REFBQ1Y7NENBQUU5QixXQUFVO3NEQUNWYixRQUFRLENBQUMsRUFBRSxDQUFDSSxXQUFXOzs7Ozs7c0RBRTFCLDhEQUFDUCxrREFBSUE7NENBQUMrQyxNQUFLOzRDQUFVL0IsV0FBVTtzREFDNUJiLFFBQVEsQ0FBQyxFQUFFLENBQUNNLEdBQUc7Ozs7Ozs7Ozs7Ozs4Q0FLcEIsOERBQUNNO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ25CLGtEQUFLQTt3REFDSm9CLEtBQUtkLFFBQVEsQ0FBQyxFQUFFLENBQUNLLEtBQUs7d0RBQ3RCVSxLQUFLZixRQUFRLENBQUMsRUFBRSxDQUFDRSxLQUFLO3dEQUN0QmMsSUFBSTt3REFDSkgsV0FBVTt3REFDVkksUUFBUTs7Ozs7Ozs7Ozs7OERBR1osOERBQUNMO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3lDOzREQUFHekMsV0FBVTtzRUFBMkM7Ozs7OztzRUFDekQsOERBQUM4Qjs0REFBRTlCLFdBQVU7c0VBQTJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFsRSw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2dDO3dDQUFLaEMsV0FBVTtrREFDYmIsUUFBUSxDQUFDLEVBQUUsQ0FBQ0csUUFBUTs7Ozs7Ozs7Ozs7OENBR3pCLDhEQUFDUix3REFBZUE7b0NBQ2RzRCxNQUFNakQsUUFBUSxDQUFDLEVBQUUsQ0FBQ0UsS0FBSztvQ0FDdkJnRCxPQUFNO29DQUNOckMsV0FBVTtvQ0FDVnNDLGVBQWU7b0NBQ2ZDLGdCQUFnQjtvQ0FDaEJDLGdCQUFnQjs7Ozs7OzhDQUVsQiw4REFBQ3pDO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDbkIsa0RBQUtBO29EQUNKb0IsS0FBS2QsUUFBUSxDQUFDLEVBQUUsQ0FBQ0ssS0FBSztvREFDdEJVLEtBQUtmLFFBQVEsQ0FBQyxFQUFFLENBQUNFLEtBQUs7b0RBQ3RCYyxJQUFJO29EQUNKSCxXQUFVO29EQUNWSSxRQUFROzs7Ozs7Ozs7OzswREFHWiw4REFBQ3FDO2dEQUFHekMsV0FBVTswREFBMEM7Ozs7OzswREFDeEQsOERBQUM4QjtnREFBRTlCLFdBQVU7MERBQW1EOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHcEUsOERBQUM4QjtvQ0FBRTlCLFdBQVU7OENBQ1ZiLFFBQVEsQ0FBQyxFQUFFLENBQUNJLFdBQVc7Ozs7Ozs4Q0FFMUIsOERBQUNQLGtEQUFJQTtvQ0FBQytDLE1BQUs7b0NBQVUvQixXQUFVOzhDQUM1QmIsUUFBUSxDQUFDLEVBQUUsQ0FBQ00sR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPdkJOLFNBQVN1RCxLQUFLLENBQUMsR0FBR25DLEdBQUcsQ0FBQyxDQUFDNEIsU0FBU1Esc0JBQy9CLDhEQUFDUjtvQkFFQ25DLFdBQVcsOERBRVYsT0FEQzJDLFFBQVEsTUFBTSxJQUFJLGFBQWE7OEJBR2pDLDRFQUFDNUM7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBRWIsOERBQUNEO3dDQUFJQyxXQUFXLGNBQXNELE9BQXhDMkMsUUFBUSxNQUFNLElBQUksWUFBWTs7MERBQzFELDhEQUFDNUM7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNnQztvREFBS2hDLFdBQVU7OERBQ2JtQyxRQUFRN0MsUUFBUTs7Ozs7Ozs7Ozs7MERBR3JCLDhEQUFDc0Q7Z0RBQUc1QyxXQUFVOzBEQUNYbUMsUUFBUTlDLEtBQUs7Ozs7OzswREFFaEIsOERBQUN5QztnREFBRTlCLFdBQVU7MERBQ1ZtQyxRQUFRNUMsV0FBVzs7Ozs7OzBEQUV0Qiw4REFBQ1E7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDNkM7d0RBQU83QyxXQUFVO2tFQUNmbUMsUUFBUTFDLEdBQUc7Ozs7OztrRUFFZCw4REFBQ29EO3dEQUFPN0MsV0FBVTtrRUFBMEk7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPaEssOERBQUNEO3dDQUFJQyxXQUFXLEdBQTJDLE9BQXhDMkMsUUFBUSxNQUFNLElBQUksWUFBWTtrREFDL0MsNEVBQUM1Qzs0Q0FBSUMsV0FBVTs7OERBRWIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ25CLGtEQUFLQTs0REFDSm9CLEtBQUtrQyxRQUFRM0MsS0FBSzs0REFDbEJVLEtBQUtpQyxRQUFROUMsS0FBSzs0REFDbEJjLElBQUk7NERBQ0pILFdBQVU7Ozs7OztzRUFFWiw4REFBQ0Q7NERBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs4REFJakIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzhDOzREQUFHOUMsV0FBVTs7Z0VBQ1htQyxRQUFRL0MsRUFBRSxLQUFLLFlBQVk7Z0VBQzNCK0MsUUFBUS9DLEVBQUUsS0FBSyxtQkFBbUI7Z0VBQ2xDK0MsUUFBUS9DLEVBQUUsS0FBSyxZQUFZOzs7Ozs7O3NFQUU5Qiw4REFBQzBDOzREQUFFOUIsV0FBVTs7Z0VBQ1ZtQyxRQUFRL0MsRUFBRSxLQUFLLFlBQVk7Z0VBQzNCK0MsUUFBUS9DLEVBQUUsS0FBSyxtQkFBbUI7Z0VBQ2xDK0MsUUFBUS9DLEVBQUUsS0FBSyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUXRDLDhEQUFDVztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDZ0M7NENBQUtoQyxXQUFVO3NEQUNibUMsUUFBUTdDLFFBQVE7Ozs7Ozs7Ozs7O2tEQUdyQiw4REFBQ3NEO3dDQUFHNUMsV0FBVTtrREFDWG1DLFFBQVE5QyxLQUFLOzs7Ozs7a0RBSWhCLDhEQUFDVTt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDbkIsa0RBQUtBO29EQUNKb0IsS0FBS2tDLFFBQVEzQyxLQUFLO29EQUNsQlUsS0FBS2lDLFFBQVE5QyxLQUFLO29EQUNsQmMsSUFBSTtvREFDSkgsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSW5CLDhEQUFDOEI7d0NBQUU5QixXQUFVO2tEQUNWbUMsUUFBUTVDLFdBQVc7Ozs7OztrREFFdEIsOERBQUNRO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzZDO2dEQUFPN0MsV0FBVTswREFDZm1DLFFBQVExQyxHQUFHOzs7Ozs7MERBRWQsOERBQUNvRDtnREFBTzdDLFdBQVU7MERBQTBJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUJBN0Y3Sm1DLFFBQVEvQyxFQUFFOzs7OzswQkFzR25CLDhEQUFDK0M7Z0JBQVFuQyxXQUFVOztrQ0FDakIsOERBQUNEO3dCQUNDQyxXQUFVO3dCQUNWNEIsT0FBTzs0QkFDTG1CLGlCQUFpQjt3QkFDbkI7Ozs7OztrQ0FFRiw4REFBQ2hEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDNEM7d0NBQUc1QyxXQUFVO2tEQUE0Rjs7Ozs7O2tEQUcxRyw4REFBQzhCO3dDQUFFOUIsV0FBVTtrREFBeUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZcEc7R0E1V3dCTjtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pblxcRGVza3RvcFxcTmV3IGZvbGRlclxcTmV3IGZvbGRlclxcZGlhbW9uZC1hdGVsaWVyLXdlYnNpdGUtdXBkYXRlLW1hc3Rlclxcc3JjXFxjb21wb25lbnRzXFxjb21tb25cXFNpbXBsZUhvbWUuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgdXNlU3RhdGUsdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCBBbmltYXRlZEhlYWRpbmcgZnJvbSBcIi4vQW5pbWF0ZWRIZWFkaW5nXCI7XG5pbXBvcnQgeyBDaGV2cm9uRG93biB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIlxuaW1wb3J0IHsgbW90aW9uLCB1c2VJblZpZXcgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuXG5jb25zdCBzZWN0aW9ucyA9IFtcbiAgLy8ge1xuICAvLyAgIGlkOiBcImhlcm9cIixcbiAgLy8gICB0aXRsZTogXCJFeHF1aXNpdGUgTGFiIEdyb3duIERpYW1vbmRzXCIsXG4gIC8vICAgc3VidGl0bGU6IFwiRXZlcnkgRGlhbW9uZCBUZWxscyBhIFN0b3J5XCIsXG4gIC8vICAgZGVzY3JpcHRpb246XG4gIC8vICAgICBcIkV0aGljYWxseSBzb3VyY2VkLCBjZXJ0aWZpZWQgZGlhbW9uZHMgd2l0aCB1bnBhcmFsbGVsZWQgcXVhbGl0eSBhbmQgYnJpbGxpYW5jZS5cIixcbiAgLy8gICBpbWFnZTogXCIvaW1hZ2UvdGFjaGVfZGlhbW9uZF9yb3VnaF9jdXQucG5nXCIsXG4gIC8vICAgY3RhOiBcIkRpc2NvdmVyIE91ciBDb2xsZWN0aW9uXCIsXG4gIC8vIH0sXG4gIHtcbiAgICBpZDogXCJzaGFwZXNcIixcbiAgICB0aXRsZTogXCJJY29uaWMgRGlhbW9uZCBDdXRzXCIsXG4gICAgc3VidGl0bGU6IFwiVGltZWxlc3MgRWxlZ2FuY2UsIE1vZGVybiBQcmVjaXNpb25cIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiRnJvbSB0aGUgY2xhc3NpYyBicmlsbGlhbmNlIG9mIHJvdW5kIGN1dHMgdG8gdGhlIGNvbnRlbXBvcmFyeSBhbGx1cmUgb2YgZW1lcmFsZCBhbmQgY3VzaGlvbiBzaGFwZXMuIEVhY2ggY3V0IGlzIG1hc3RlcmZ1bGx5IGV4ZWN1dGVkIHRvIG1heGltaXplIGZpcmUsIGJyaWxsaWFuY2UsIGFuZCBzY2ludGlsbGF0aW9uLlwiLFxuICAgIGltYWdlOiBcIi9pbWFnZS8uanBnXCIsXG4gICAgY3RhOiBcIkRpc2NvdmVyIFNoYXBlc1wiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiY3JhZnRzbWFuc2hpcFwiLFxuICAgIHRpdGxlOiBcIkFydGlzYW5hbCBFeGNlbGxlbmNlXCIsXG4gICAgc3VidGl0bGU6IFwiSGVyaXRhZ2UgQ3JhZnRzbWFuc2hpcCwgUmV2b2x1dGlvbmFyeSBUZWNobm9sb2d5XCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIk91ciBtYXN0ZXIgY3JhZnRzbWVuIGJsZW5kIGNlbnR1cmllcy1vbGQgdGVjaG5pcXVlcyB3aXRoIGN1dHRpbmctZWRnZSB0ZWNobm9sb2d5LCBjcmVhdGluZyBkaWFtb25kcyB0aGF0IHJpdmFsIG5hdHVyZSdzIGZpbmVzdC4gRXZlcnkgZmFjZXQgaXMgcHJlY2lzZWx5IGN1dCB0byBhY2hpZXZlIG1heGltdW0gb3B0aWNhbCBwZXJmb3JtYW5jZSBhbmQgYnJlYXRodGFraW5nIGJlYXV0eS5cIixcbiAgICBpbWFnZTogXCIvaW1hZ2Uvc2hhcGluZ19waGFzZV90YWNoZS03Njh4NzY4LnBuZ1wiLFxuICAgIGN0YTogXCJWaWV3IE91ciBQcm9jZXNzXCIsXG4gIH0sXG4gIHtcbiAgICBpZDogXCJjb2xvcnNcIixcbiAgICB0aXRsZTogXCJSYXJlIEZhbmN5IENvbG9yc1wiLFxuICAgIHN1YnRpdGxlOiBcIk5hdHVyZSdzIFNwZWN0cnVtLCBQZXJmZWN0ZWRcIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiRXhwZXJpZW5jZSB0aGUgZXh0cmFvcmRpbmFyeSB3aXRoIG91ciBjb2xsZWN0aW9uIG9mIGZhbmN5IGNvbG9yZWQgZGlhbW9uZHMuIEZyb20gdml2aWQgeWVsbG93cyB0byByYXJlIHBpbmtzIGFuZCBibHVlcywgZWFjaCBjb2xvcmVkIGRpYW1vbmQgcmVwcmVzZW50cyB0aGUgcGlubmFjbGUgb2YgZ2Vtb2xvZ2ljYWwgYWNoaWV2ZW1lbnQuXCIsXG4gICAgaW1hZ2U6XG4gICAgICBcIi9pbWFnZXMvQ29sb3JzL25hdHVyYWwtY29sb3ItZG1kcy1jb2xvci1kaWFtb25kcy1yb3VnaC1kaWFtb25kcy1yZW1vdmViZy1wcmV2aWV3LnBuZ1wiLFxuICAgIGN0YTogXCJFeHBsb3JlIENvbG9yc1wiLFxuICB9LFxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2ltcGxlSG9tZSgpIHtcbiAgY29uc3QgW2FjdGl2ZVNlY3Rpb24sIHNldEFjdGl2ZVNlY3Rpb25dID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SXNDbGllbnQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgbWluLWgtc2NyZWVuIHB0LTIwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZ1bGwtc2VjdGlvbiByZWxhdGl2ZSB3LWZ1bGwgaC1zY3JlZW4gb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHsvKiBCYWNrZ3JvdW5kIEltYWdlICovfVxuICAgICAgICA8SW1hZ2VcbiAgICAgICAgICBzcmM9XCIvaW1hZ2UvSG9tZXBhZ2VfQmFubmVyXzIwMjVfRGVza3RvcF9jMGRkZmE4MTk0LnBuZ1wiXG4gICAgICAgICAgYWx0PVwiRGlhbW9uZCBBdGVsaWVyIEhvbWVwYWdlIEJhbm5lclwiXG4gICAgICAgICAgZmlsbFxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgIHByaW9yaXR5XG4gICAgICAgICAgcXVhbGl0eT17OTB9XG4gICAgICAgIC8+XG5cbiAgICAgICAgey8qIFNvcGhpc3RpY2F0ZWQgT3ZlcmxheSBHcmFkaWVudHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iIGZyb20tYmxhY2svMzAgdmlhLXRyYW5zcGFyZW50IHRvLWJsYWNrLzYwIHBvaW50ZXItZXZlbnRzLW5vbmVcIiAvPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsYWNrLzIwIHZpYS10cmFuc3BhcmVudCB0by1ibGFjay8yMCBwb2ludGVyLWV2ZW50cy1ub25lXCIgLz5cblxuICAgICAgICB7LyogQW5pbWF0ZWQgUGFydGljbGVzL1NwYXJrbGVzICovfVxuICAgICAgICB7aXNDbGllbnQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvdmVyZmxvdy1oaWRkZW4gcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAge1suLi5BcnJheSgxMildLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB3LTEgaC0xIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBvcGFjaXR5LTYwXCJcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7XG4gICAgICAgICAgICAgICAgICB4OiBNYXRoLnJhbmRvbSgpICogd2luZG93LmlubmVyV2lkdGgsXG4gICAgICAgICAgICAgICAgICB5OiBNYXRoLnJhbmRvbSgpICogd2luZG93LmlubmVySGVpZ2h0LFxuICAgICAgICAgICAgICAgICAgc2NhbGU6IDAsXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICAgICAgICB5OiBbbnVsbCwgLTEwMF0sXG4gICAgICAgICAgICAgICAgICBvcGFjaXR5OiBbMC42LCAwXSxcbiAgICAgICAgICAgICAgICAgIHNjYWxlOiBbMCwgMSwgMF0sXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMyArIE1hdGgucmFuZG9tKCkgKiAyLFxuICAgICAgICAgICAgICAgICAgcmVwZWF0OiBJbmZpbml0eSxcbiAgICAgICAgICAgICAgICAgIGRlbGF5OiBNYXRoLnJhbmRvbSgpICogMyxcbiAgICAgICAgICAgICAgICAgIGVhc2U6IFwiZWFzZU91dFwiLFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogTWFpbiBDb250ZW50IENvbnRhaW5lciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotMjAgcHgtNlwiPlxuICAgICAgICAgIHsvKiBMb2dvIFNlY3Rpb24gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogNTAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMS41LCBlYXNlOiBcImVhc2VPdXRcIiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItOFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBncm91cFwiPlxuICAgICAgICAgICAgICA8aDFcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBzbTp0ZXh0LTJ4bCBtZDp0ZXh0LTN4bCBsZzp0ZXh0LTR4bCB4bDp0ZXh0LTV4bCBmb250LWxpZ2h0IGxlYWRpbmctdGlnaHQgdHJhY2tpbmctWzAuMmVtXSB0ZXh0LWJsYWNrLzk1IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTcwMCBmb250LW1vbnRzZXJyYXRcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAvLyB0ZXh0U2hhZG93OiAnMCAwIDQwcHggcmdiYSgyNTUsMjU1LDI1NSwwLjYpLCAwIDAgODBweCByZ2JhKDI1NSwyNTUsMjU1LDAuMyknLFxuICAgICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogXCIwLjFlbVwiLFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBESUFNT05EUyBUSEFUIERFU0VSVkUgWU9VXG4gICAgICAgICAgICAgIDwvaDE+XG5cbiAgICAgICAgICAgICAgey8qIFByZW1pdW0gR2xvdyBFZmZlY3QgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLWJsYWNrLzEwIHRvLXRyYW5zcGFyZW50IG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tNzAwIGJsdXItM3hsXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogRWxlZ2FudCBUYWdsaW5lICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEuNSwgZGVsYXk6IDAuNSwgZWFzZTogXCJlYXNlT3V0XCIgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LTR4bCBteC1hdXRvIG1iLTEyXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIG1kOnRleHQtMnhsIGxnOnRleHQtMnhsIHhsOnRleHQtM3hsIGZvbnQtbGlnaHQgdGV4dC1ibGFjay85MCB0cmFja2luZy1bMC4zZW1dIGxlYWRpbmctcmVsYXhlZCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgey8qIDxQZXJtYW5lbnRDb3VudGVyXG4gICAgICAgICAgICAgICAgdGFyZ2V0TnVtYmVyPXsxMDAwMH1cbiAgICAgICAgICAgICAgICBzdWZmaXg9XCIrIENFUlRJRklFRCBTVE9ORVNcIlxuICAgICAgICAgICAgICAgIGR1cmF0aW9uPXsyMDAwfVxuICAgICAgICAgICAgICAvPiAqL317XCIgXCJ9XG4gICAgICAgICAgICAgIDEwLDAwMCsgQ2VydGlmaWVkIFN0b25lc1xuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLXB4IGJnLWdyYWRpZW50LXRvLXIgZnJvbS10cmFuc3BhcmVudCB2aWEtYmxhY2svNjAgdG8tdHJhbnNwYXJlbnQgbXgtYXV0byBtdC02IG1iLTZcIj48L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIG1kOnRleHQtbGcgbGc6dGV4dC14bCB0ZXh0LWJsYWNrLzcwIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZSBsZWFkaW5nLXJlbGF4ZWQgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgIFdoZXJlIHByZWNpc2lvbiBtZWV0cyBwZXJmZWN0aW9uIGluIGV2ZXJ5IGZhY2V0XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgey8qIENhbGwgdG8gQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMS41LCBkZWxheTogMSwgZWFzZTogXCJlYXNlT3V0XCIgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQgc206Z2FwLTZcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvc2hhcGVzXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgcHgtOCBweS0zIGJnLUJsYWNrIGJvcmRlciBib3JkZXItd2hpdGUvMzAgdGV4dC1CbGFjayBob3ZlcjpiZy13aGl0ZSBob3Zlcjp0ZXh0LWJsYWNrIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0cmFja2luZy13aWRlciBmb250LWxpZ2h0IG92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5FWFBsT1JFIFNIQVBFUzwvc3Bhbj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLUJsYWNrIHRyYW5zZm9ybSBzY2FsZS14LTAgZ3JvdXAtaG92ZXI6c2NhbGUteC0xMDAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNTAwIG9yaWdpbi1sZWZ0XCI+PC9kaXY+XG4gICAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCJodHRwczovL2ludmVudG9yeS5kaWFtb25kYXRlbGllci5pbi9cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBweC04IHB5LTMgYmctYmxhY2svMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvMjAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRleHQtc20gc206dGV4dC1iYXNlIHRyYWNraW5nLXdpZGVyIGZvbnQtbGlnaHRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+VklFVyBJTlZFTlRPUlk8L3NwYW4+XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2Nyb2xsIEluZGljYXRvciAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAxLjUsIGRlbGF5OiAxLjUgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tOCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiB6LTEwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50SW5kZXgoMSl9XG4gICAgICAgID5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgYW5pbWF0ZT17eyB5OiBbMCwgOCwgMF0gfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDIsIHJlcGVhdDogSW5maW5pdHksIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHRleHQtd2hpdGUvNjAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdHJhY2tpbmctd2lkZXN0IG1iLTIgZm9udC1saWdodFwiPlxuICAgICAgICAgICAgICBTQ1JPTExcbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAtIFJlZGVzaWduZWQgd2l0aG91dCBjaXJjdWxhciBpbWFnZSAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC00IGxnOnB4LTggYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTUwIHRvLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICB7LyogRGVza3RvcCBMYXlvdXQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdhcC0xNlwiPlxuICAgICAgICAgICAgey8qIExlZnQgLSBUZXh0IENvbnRlbnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWJsYWNrXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMmVtXSBmb250LWxpZ2h0IGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgICAge3NlY3Rpb25zWzBdLnN1YnRpdGxlfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxBbmltYXRlZEhlYWRpbmdcbiAgICAgICAgICAgICAgICB0ZXh0PXtzZWN0aW9uc1swXS50aXRsZX1cbiAgICAgICAgICAgICAgICBsZXZlbD1cImgxXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTV4bCB4bDp0ZXh0LTZ4bCAyeGw6dGV4dC03eGwgbWItOCBmb250LWxpZ2h0IGxlYWRpbmctdGlnaHQgZm9udC1tb250c2VycmF0XCJcbiAgICAgICAgICAgICAgICBzaG93VW5kZXJsaW5lPXtmYWxzZX1cbiAgICAgICAgICAgICAgICBhbmltYXRpb25EZWxheT17MC4yfVxuICAgICAgICAgICAgICAgIHRyaWdnZXJPbk1vdW50PXt0cnVlfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHhsOnRleHQtMnhsIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0IHRleHQtZ3JheS03MDAgbWItMTIgbWF4LXctMnhsIGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgIHtzZWN0aW9uc1swXS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3NoYXBlc1wiIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibGFjayB0ZXh0LXdoaXRlIHB4LTggcHktNCB0ZXh0LWxnIGZvbnQtbWVkaXVtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4xZW1dIGhvdmVyOmJnLWdyYXktODAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbnNbMF0uY3RhfVxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFJpZ2h0IC0gTW9kZXJuIENhcmQgRGVzaWduICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBtYXgtdy1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LTJ4bCBwLTggYm9yZGVyIGJvcmRlci1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLTY0IG1iLTYgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtzZWN0aW9uc1swXS5pbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3NlY3Rpb25zWzBdLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIHByaW9yaXR5XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtbGlnaHQgbWItNCBmb250LW1vbnRzZXJyYXRcIj5QcmVtaXVtIERpYW1vbmQgQ3V0czwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZm9udC1saWdodCBmb250LW1vbnRzZXJyYXRcIj5NYXN0ZXJmdWxseSBjcmFmdGVkIGZvciBtYXhpbXVtIGJyaWxsaWFuY2U8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBNb2JpbGUgTGF5b3V0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHRleHQtY2VudGVyIHRleHQtYmxhY2tcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gdXBwZXJjYXNlIHRyYWNraW5nLVswLjJlbV0gZm9udC1saWdodCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbnNbMF0uc3VidGl0bGV9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPEFuaW1hdGVkSGVhZGluZ1xuICAgICAgICAgICAgICB0ZXh0PXtzZWN0aW9uc1swXS50aXRsZX1cbiAgICAgICAgICAgICAgbGV2ZWw9XCJoMVwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtNHhsIHNtOnRleHQtNXhsIG1iLTggZm9udC1saWdodCBsZWFkaW5nLXRpZ2h0IGZvbnQtbW9udHNlcnJhdFwiXG4gICAgICAgICAgICAgIHNob3dVbmRlcmxpbmU9e2ZhbHNlfVxuICAgICAgICAgICAgICBhbmltYXRpb25EZWxheT17MC4yfVxuICAgICAgICAgICAgICB0cmlnZ2VyT25Nb3VudD17dHJ1ZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy14bCBwLTYgbWF4LXctc21cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtNDggbWItNCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz17c2VjdGlvbnNbMF0uaW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgIGFsdD17c2VjdGlvbnNbMF0udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgcHJpb3JpdHlcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1saWdodCBtYi0yIGZvbnQtbW9udHNlcnJhdFwiPlByZW1pdW0gRGlhbW9uZCBDdXRzPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gZm9udC1saWdodCBmb250LW1vbnRzZXJyYXRcIj5NYXN0ZXJmdWxseSBjcmFmdGVkIGZvciBtYXhpbXVtIGJyaWxsaWFuY2U8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0IHRleHQtZ3JheS03MDAgbWItOCBtYXgtdy0yeGwgbXgtYXV0byBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAge3NlY3Rpb25zWzBdLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9zaGFwZXNcIiBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYmctYmxhY2sgdGV4dC13aGl0ZSBweC04IHB5LTQgdGV4dC1sZyBmb250LW1lZGl1bSB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMWVtXSBob3ZlcjpiZy1ncmF5LTgwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgIHtzZWN0aW9uc1swXS5jdGF9XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogT3RoZXIgU2VjdGlvbnMgLSBSZWRlc2lnbmVkIHdpdGhvdXQgY2lyY3VsYXIgaW1hZ2VzICovfVxuICAgICAge3NlY3Rpb25zLnNsaWNlKDEpLm1hcCgoc2VjdGlvbiwgaW5kZXgpID0+IChcbiAgICAgICAgPHNlY3Rpb25cbiAgICAgICAgICBrZXk9e3NlY3Rpb24uaWR9XG4gICAgICAgICAgY2xhc3NOYW1lPXtgbWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTQgbGc6cHgtOCAke1xuICAgICAgICAgICAgaW5kZXggJSAyID09PSAwID8gJ2JnLXdoaXRlJyA6ICdiZy1ncmF5LTUwJ1xuICAgICAgICAgIH1gfVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgey8qIERlc2t0b3AgTGF5b3V0IC0gTW9kZXJuIEdyaWQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBsZzpncmlkIGxnOmdyaWQtY29scy0yIGdhcC0xNiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgey8qIFRleHQgQ29udGVudCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0ZXh0LWJsYWNrICR7aW5kZXggJSAyID09PSAwID8gJ29yZGVyLTInIDogJ29yZGVyLTEnfWB9PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4yZW1dIGZvbnQtbGlnaHQgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLnN1YnRpdGxlfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCB4bDp0ZXh0LTV4bCBtYi02IGZvbnQtbGlnaHQgbGVhZGluZy10aWdodCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB4bDp0ZXh0LXhsIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0IHRleHQtZ3JheS03MDAgbWItOCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ibGFjayB0ZXh0LWJsYWNrIHB4LTggcHktMyB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdXBwZXJjYXNlIHRyYWNraW5nLVswLjFlbV0gaG92ZXI6YmctYmxhY2sgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLmN0YX1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHB4LTggcHktMyB0ZXh0LWJhc2UgZm9udC1saWdodCB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMWVtXSBob3Zlcjp0ZXh0LWJsYWNrIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgICAgTGVhcm4gTW9yZVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBNb2Rlcm4gSW1hZ2UgTGF5b3V0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YCR7aW5kZXggJSAyID09PSAwID8gJ29yZGVyLTEnIDogJ29yZGVyLTInfWB9PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBNYWluIEltYWdlIENvbnRhaW5lciAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgaC05NiBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC0yeGwgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICBzcmM9e3NlY3Rpb24uaW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgYWx0PXtzZWN0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJsYWNrLzIwIHRvLXRyYW5zcGFyZW50XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEZsb2F0aW5nIEluZm8gQ2FyZCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS02IC1yaWdodC02IGJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXhsIHAtNiBtYXgtdy14c1wiPlxuICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yIGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLmlkID09PSAnc2hhcGVzJyAmJiAnUHJlbWl1bSBDdXRzJ31cbiAgICAgICAgICAgICAgICAgICAgICB7c2VjdGlvbi5pZCA9PT0gJ2NyYWZ0c21hbnNoaXAnICYmICdFeHBlcnQgQ3JhZnRlZCd9XG4gICAgICAgICAgICAgICAgICAgICAge3NlY3Rpb24uaWQgPT09ICdjb2xvcnMnICYmICdSYXJlIENvbG9ycyd9XG4gICAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBmb250LWxpZ2h0IGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLmlkID09PSAnc2hhcGVzJyAmJiAnUm91bmQsIFByaW5jZXNzLCBFbWVyYWxkICYgTW9yZSd9XG4gICAgICAgICAgICAgICAgICAgICAge3NlY3Rpb24uaWQgPT09ICdjcmFmdHNtYW5zaGlwJyAmJiAnVHJhZGl0aW9uYWwgbWVldHMgVGVjaG5vbG9neSd9XG4gICAgICAgICAgICAgICAgICAgICAge3NlY3Rpb24uaWQgPT09ICdjb2xvcnMnICYmICdZZWxsb3csIFBpbmssIEJsdWUgJiBCZXlvbmQnfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1vYmlsZSBMYXlvdXQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiB0ZXh0LWNlbnRlciB0ZXh0LWJsYWNrXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMmVtXSBmb250LWxpZ2h0IGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgICAge3NlY3Rpb24uc3VidGl0bGV9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIHNtOnRleHQtNHhsIG1iLTYgZm9udC1saWdodCBsZWFkaW5nLXRpZ2h0IGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgIHtzZWN0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICA8L2gyPlxuXG4gICAgICAgICAgICAgIHsvKiBNb2JpbGUgSW1hZ2UgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgaC02NCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC0yeGwgc2hhZG93LXhsIG14LWF1dG8gbWF4LXctc21cIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9e3NlY3Rpb24uaW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgIGFsdD17c2VjdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgZmlsbFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svMjAgdG8tdHJhbnNwYXJlbnRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0IHRleHQtZ3JheS03MDAgbWItOCBtYXgtdy0yeGwgbXgtYXV0byBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbi5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTQgbWF4LXctc20gbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ibGFjayB0ZXh0LWJsYWNrIHB4LTggcHktMyB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdXBwZXJjYXNlIHRyYWNraW5nLVswLjFlbV0gaG92ZXI6YmctYmxhY2sgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgICB7c2VjdGlvbi5jdGF9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHB4LTggcHktMyB0ZXh0LWJhc2UgZm9udC1saWdodCB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMWVtXSBob3Zlcjp0ZXh0LWJsYWNrIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgIExlYXJuIE1vcmVcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuICAgICAgKSl9XG5cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBoLTk2IG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LWZ1bGwgaC1mdWxsIGJnLWNvdmVyIGJnLWNlbnRlciBiZy1uby1yZXBlYXRcIlxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6IFwidXJsKCcvaW1hZ2UvRXhwZXJ0LmpwZycpXCIsXG4gICAgICAgICAgfX1cbiAgICAgICAgPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsYWNrLzQwIHRvLXRyYW5zcGFyZW50XCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBoLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHctZnVsbFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0yeGxcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIHNtOnRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWJsYWNrIG1iLTYgZHJvcC1zaGFkb3ctbGcgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgRXhwZXJ0IEV5ZXMgb24gRXZlcnkgU3RvbmVcbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWJsYWNrIG1iLTggbGVhZGluZy1yZWxheGVkIGRyb3Atc2hhZG93LW1kIGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgIE91ciBpbi1ob3VzZSBnZW1vbG9naXN0cyBwZXJzb25hbGx5IGluc3BlY3QgYW5kIHZlcmlmeSBldmVyeVxuICAgICAgICAgICAgICAgIGxhYi1ncm93biBkaWFtb25kLiBZb3UmYXBvcztsbCByZWNlaXZlIGEgZGV0YWlsZWQgcmVwb3J0XG4gICAgICAgICAgICAgICAgY292ZXJpbmcgYnJpbGxpYW5jZSwgY3V0LCBhbmQgcXVhbGl0eSDigJQgZmFyIGJleW9uZCB3aGF0IGFcbiAgICAgICAgICAgICAgICBjZXJ0aWZpY2F0ZSBhbG9uZSBjYW4gc2hvdy5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSW1hZ2UiLCJBbmltYXRlZEhlYWRpbmciLCJDaGV2cm9uRG93biIsIkxpbmsiLCJtb3Rpb24iLCJ1c2VJblZpZXciLCJzZWN0aW9ucyIsImlkIiwidGl0bGUiLCJzdWJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaW1hZ2UiLCJjdGEiLCJTaW1wbGVIb21lIiwiYWN0aXZlU2VjdGlvbiIsInNldEFjdGl2ZVNlY3Rpb24iLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwic3JjIiwiYWx0IiwiZmlsbCIsInByaW9yaXR5IiwicXVhbGl0eSIsIkFycmF5IiwibWFwIiwiXyIsImkiLCJpbml0aWFsIiwieCIsIk1hdGgiLCJyYW5kb20iLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwieSIsImlubmVySGVpZ2h0Iiwic2NhbGUiLCJhbmltYXRlIiwib3BhY2l0eSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZGVsYXkiLCJlYXNlIiwiaDEiLCJzdHlsZSIsImxldHRlclNwYWNpbmciLCJwIiwiaHJlZiIsInNwYW4iLCJvbkNsaWNrIiwic2V0Q3VycmVudEluZGV4Iiwic2VjdGlvbiIsInRleHQiLCJsZXZlbCIsInNob3dVbmRlcmxpbmUiLCJhbmltYXRpb25EZWxheSIsInRyaWdnZXJPbk1vdW50IiwiaDMiLCJzbGljZSIsImluZGV4IiwiaDIiLCJidXR0b24iLCJoNCIsImJhY2tncm91bmRJbWFnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/SimpleHome.jsx\n"));

/***/ })

});