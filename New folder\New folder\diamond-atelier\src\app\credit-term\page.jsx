import React from "react";
import ClientCreditTermsPage from "./ClientCreditTermsPage";

export const metadata = {
  title: "Credit & Memo Terms - Diamond Atelier | Business Terms & Conditions",
  description: "Review Diamond Atelier's credit and memo terms for business partnerships. Comprehensive terms and conditions for diamond trade and business transactions.",
  keywords: [
    "credit terms",
    "memo terms",
    "business terms",
    "diamond trade terms",
    "payment conditions",
    "diamond atelier terms",
    "business partnership"
  ],
  openGraph: {
    title: "Credit & Memo Terms - Diamond Atelier | Business Terms & Conditions",
    description: "Review Diamond Atelier's credit and memo terms for business partnerships. Comprehensive terms and conditions for diamond trade and business transactions.",
    images: [
      {
        url: '/images/credit-terms-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Credit & Memo Terms - Diamond Atelier',
      },
    ],
  },
};

async function getCreditTermsData() {
  const terms = [
    {
      id: 1,
      title: "Term",
      description: "Our Credit-Memo term is from 7 to 30 days",
      icon: "📅",
      details: [
        "Flexible credit period ranging from 7 to 30 days",
        "Terms determined based on business relationship",
        "Extension possible with prior approval",
        "Clear payment schedule provided"
      ]
    },
    {
      id: 2,
      title: "References",
      description: "Minimum 3 references are required for Credit & Memo",
      icon: "📋",
      details: [
        "Three business references mandatory",
        "References must be from established diamond traders",
        "Contact information and trade history required",
        "Verification process conducted for all references"
      ]
    },
    {
      id: 3,
      title: "UPS Policy",
      description: "We don't ship stones through UPS and if in case you are returning any stones we don't want you to send us through UPS.",
      icon: "📦",
      details: [
        "UPS shipping not accepted for diamond shipments",
        "Returns must not be sent via UPS",
        "Secure courier services recommended",
        "Insurance required for all shipments"
      ]
    },
    {
      id: 4,
      title: "Credit Limit",
      description: "Final Limits will be decided by Diamond Atelier and no objections will be entertained on it.",
      icon: "💳",
      details: [
        "Credit limits determined by Diamond Atelier",
        "Based on financial assessment and trade history",
        "Non-negotiable final decision",
        "Regular review and adjustment possible"
      ]
    },
    {
      id: 5,
      title: "Shipment Charges",
      description: "The client shall bear the shipment charges for delivery and return of the diamonds.",
      icon: "🚚",
      details: [
        "All shipping costs borne by client",
        "Includes both delivery and return charges",
        "Insurance costs additional",
        "Express delivery options available"
      ]
    }
  ];

  const heroImage = "/images/homepageimages/creditrule.jpg";

  return {
    terms,
    heroImage
  };
}

// Server-side component
export default async function Page() {
  // Get data on server
  const { terms, heroImage } = await getCreditTermsData();

  return (
    <ClientCreditTermsPage 
      terms={terms}
      heroImage={heroImage}
    />
  );
}
