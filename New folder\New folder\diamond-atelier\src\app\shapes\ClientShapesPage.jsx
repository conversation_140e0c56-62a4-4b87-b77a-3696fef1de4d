"use client";
import React, { useEffect, useState, useMemo } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import LoadingScreen from "../../utils/LoadingScreen";
import OptimizedImage from "@/components/common/OptimizedImage";
import INPOptimizer from "@/components/common/INPOptimizer";
import PerformanceMonitor from "@/components/common/PerformanceMonitor";

export default function ClientShapesPage({ shapes, sideStone, exoticShapes }) {

  const [loading, setLoading] = useState(true);

  // Memoize expensive calculations
  const totalShapes = useMemo(() => shapes?.length || 0, [shapes]);
  const totalSideStones = useMemo(() => sideStone?.length || 0, [sideStone]);
  const totalExoticShapes = useMemo(() => exoticShapes?.length || 0, [exoticShapes]);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 200);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <LoadingScreen />;

  return (
    <>
      <INPOptimizer /> {/* Optimize interactions for better performance */}
      <PerformanceMonitor pageName="Shapes Page" />
      <div className="bg-black min-h-screen overflow-x-hidden overflow-y-auto w-full">
      {/* Full Screen Hero Section */}
      <section
        className="relative w-full overflow-hidden"
        style={{ height: 'calc(100vh - 110px)' }}
      >
        {/* Background Video */}
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          onError={(e) => {
            console.log('Video failed to load:', e);
            // Hide video if it fails to load
            e.target.style.display = 'none';
          }}
        >
          <source src="/images/about/Shapes.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Fallback background in case video doesn't load */}
        <div
          className="absolute inset-0 w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-800"
          style={{ zIndex: -1 }}
        />

        {/* Improved Title Overlay */}
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-black/50">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="text-center px-6"

            
          >
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.1, delay: 0.05 }}
              className="w-32 h-0.5 bg-white mx-auto mb-12 origin-center"
            />

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.1, delay: 0.1 }}
              className="text-2xl md:text-3xl lg:text-4xl font-thin text-white mb-6 tracking-[0.3em] leading-tight"
            >
              MASTERPIECE DIAMOND SHAPES
            </motion.h1>
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.1, delay: 0.15 }}
              className="w-24 h-0.5 bg-white/70 mx-auto mb-8 origin-center"
            />

            {/* <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.1, delay: 0.2 }}
              className="text-base md:text-lg text-gray-200 max-w-4xl mx-auto font-light tracking-wide leading-relaxed"
            >
              Discover the artistry of perfection in our curated collection of lab-grown diamonds.
              Each meticulously crafted shape represents centuries of cutting mastery, designed to maximize
              brilliance, fire, and scintillation. From timeless classics to contemporary innovations,
              every diamond tells a story of precision and beauty.
            </motion.p> */}
          </motion.div>
        </div>
      </section>

      {/* Fancy Shapes Information Section */}
      <section className="bg-black text-white py-12 px-6 lg:px-12 overflow-x-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Text Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="space-y-8"
            >
              <motion.div
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="w-24 h-0.5 bg-gradient-to-r from-white via-white/60 to-transparent"
              />

              <motion.h2
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-4xl md:text-3xl font-thin text-white tracking-[0.2em] leading-tight"
              >
                FANCY SHAPES
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-gray-300 text-lg font-light leading-relaxed tracking-wide"
              >
                Often referred to as diamond cuts, diamonds are carved into a variety of shapes.
                From the soft curves of an oval cut to the angular points of a princess cut,
                there are diamond shapes to suit all taste and design.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="pt-4"
              >
                <div className="flex items-center space-x-4">
                  <Sparkles className="w-6 h-6 text-white/60" />
                  <span className="text-white/80 font-light tracking-wider">
                    Discover Your Perfect Cut
                  </span>
                </div>
              </motion.div>
            </motion.div>

            {/* Image Content */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
              className="relative"
            >
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-900/30 to-black/30 p-6 backdrop-blur-sm border border-white/10">
                <OptimizedImage
                  src="/images/Colors/facyshape.png"
                  alt="Fancy diamond shapes collection"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover rounded-xl shadow-2xl"
                  priority={false}
                />

                {/* Subtle overlay for better text contrast */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent rounded-2xl pointer-events-none"></div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 border-t-2 border-r-2 border-white/20"></div>
              <div className="absolute -bottom-4 -left-4 w-8 h-8 border-b-2 border-l-2 border-white/20"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Professional Basic Shapes Section */}
      <section
        className="min-h-screen bg-black text-white py-16 px-6 lg:px-12 overflow-x-hidden"
      >
        <div className="max-w-7xl mx-auto below-header">
          {/* Stunning Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="text-center mb-20 relative"
          >


            <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.05 }}
              className="w-24 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"
            />

            <motion.h2
              initial={{ opacity: 0, letterSpacing: "0.5em" }}
              whileInView={{ opacity: 1, letterSpacing: "0.3em" }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.1 }}
              className="text-3xl md:text-4xl font-thin text-white mb-3 tracking-[0.3em] relative z-10"
            >
              BASIC SHAPES
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.15 }}
              className="text-gray-400 text-lg font-light tracking-wide mb-6 relative z-10"
            >
              Timeless elegance in every cut
            </motion.p>

            <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.2 }}
              className="w-16 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"
            />
          </motion.div>

          {/* Premium Diamond Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-10 w-full overflow-hidden">
            {shapes.map((shape, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.15,
                  delay: index * 0.01,
                  ease: "easeOut"
                }}
                className="group flex flex-col items-center text-center"
              >
                {/* Ultra Luxurious Diamond Container */}
                <div className="relative">
                  {/* Multiple glow layers */}
                  {/* <div className="absolute inset-0 bg-white/5 rounded-full blur-2xl group-hover:bg-white/15 transition-all duration-700 scale-150 animate-pulse"></div>
                  <div className="absolute inset-0 bg-white/10 rounded-full blur-xl group-hover:bg-white/25 transition-all duration-500 scale-125"></div> */}



                  {/* Clean diamond container */}
                  <div className="w-36 h-36 sm:w-40 sm:h-40 md:w-44 md:h-44 relative flex items-center justify-center transform group-hover:scale-105 transition-all duration-500">
                    {/* Diamond image */}
                    {shape.img && shape.img.src ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <OptimizedImage
                          src={shape.img}
                          alt={shape.alt || shape.title}
                          width={140}
                          height={140}
                          className="max-w-full max-h-full object-contain group-hover:scale-110 transition-all duration-700 filter drop-shadow-2xl"
                          priority={false}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Gem className="w-12 h-12 text-gray-400 group-hover:text-white transition-colors duration-500" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Premium Shape Name */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.1, delay: index * 0.02 + 0.05 }}
                  className="mt-8 relative"
                >
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.1, delay: index * 0.02 + 0.1 }}
                    className="w-20 h-0.5 bg-gradient-to-r from-transparent via-white/60 to-transparent mx-auto mb-4 group-hover:via-white transition-all duration-500"
                  />

                  <h3 className="text-white text-sm font-extralight tracking-[0.2em] uppercase group-hover:text-gray-200 group-hover:tracking-[0.3em] transition-all duration-500 relative">
                    {shape.title}
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></span>
                  </h3>

                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.1, delay: index * 0.02 + 0.15 }}
                    className="w-12 h-0.5 bg-gradient-to-r from-transparent via-white/40 to-transparent mx-auto mt-2 group-hover:via-white/80 transition-all duration-500"
                  />
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Side Stone Section */}
      <section
        className="min-h-screen bg-black text-white py-16 px-6 lg:px-12 overflow-x-hidden"
      >
        <div className="max-w-7xl mx-auto below-header">
          {/* Stunning Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="text-center mb-20 relative"
          >


            <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.05 }}
              className="w-24 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"
            />

            <motion.h2
              initial={{ opacity: 0, letterSpacing: "0.5em" }}
              whileInView={{ opacity: 1, letterSpacing: "0.3em" }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.1 }}
              className="text-3xl md:text-4xl font-thin text-white mb-3 tracking-[0.3em] relative z-10"
            >
              SIDE STONE SHAPES
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.15 }}
              className="text-gray-400 text-lg font-light tracking-wide mb-6 relative z-10"
            >
              Perfect accents for extraordinary brilliance
            </motion.p>

            <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.2 }}
              className="w-16 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"
            />
          </motion.div>

          {/* Premium Diamond Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-10 w-full overflow-hidden">
            {sideStone.map((shape, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.15,
                  delay: index * 0.01,
                  ease: "easeOut"
                }}
                className="group flex flex-col items-center text-center"
              >
                {/* Ultra Luxurious Diamond Container */}
                <div className="relative">
                  {/* <div className="absolute inset-0 bg-white/5 rounded-full blur-2xl group-hover:bg-white/15 transition-all duration-700 scale-150 animate-pulse"></div>
                  <div className="absolute inset-0 bg-white/10 rounded-full blur-xl group-hover:bg-white/25 transition-all duration-500 scale-125"></div> */}



                  <div className="w-36 h-36 sm:w-40 sm:h-40 md:w-44 md:h-44 relative flex items-center justify-center transform group-hover:scale-105 transition-all duration-500">
                    {shape.img && shape.img.src ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <OptimizedImage
                          src={shape.img}
                          alt={shape.alt || shape.title}
                          width={140}
                          height={140}
                          className="max-w-full max-h-full object-contain group-hover:scale-110 transition-all duration-700 filter drop-shadow-2xl"
                          priority={false}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Sparkles className="w-12 h-12 text-gray-400 group-hover:text-white transition-colors duration-500" />
                      </div>
                    )}
                  </div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.1, delay: index * 0.02 + 0.05 }}
                  className="mt-8 relative"
                >
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.1, delay: index * 0.02 + 0.1 }}
                    className="w-20 h-0.5 bg-gradient-to-r from-transparent via-white/60 to-transparent mx-auto mb-4 group-hover:via-white transition-all duration-500"
                  />

                  <h3 className="text-white text-sm font-extralight tracking-[0.2em] uppercase group-hover:text-gray-200 group-hover:tracking-[0.3em] transition-all duration-500 relative">
                    {shape.title}
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></span>
                  </h3>

                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.1, delay: index * 0.02 + 0.15 }}
                    className="w-12 h-0.5 bg-gradient-to-r from-transparent via-white/40 to-transparent mx-auto mt-2 group-hover:via-white/80 transition-all duration-500"
                  />
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Exotic Shapes Section */}
      <section
        className="min-h-screen bg-black text-white py-16 px-6 lg:px-12 overflow-x-hidden"
      >
        <div className="max-w-7xl mx-auto below-header">
          {/* Stunning Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="text-center mb-20 relative"
          >


            <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.05 }}
              className="w-24 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"
            />

            <motion.h2
              initial={{ opacity: 0, letterSpacing: "0.5em" }}
              whileInView={{ opacity: 1, letterSpacing: "0.3em" }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.1 }}
              className="text-3xl md:text-4xl font-thin text-white mb-3 tracking-[0.3em] relative z-10"
            >
              EXOTIC SHAPES
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.15 }}
              className="text-gray-400 text-lg font-light tracking-wide mb-6 relative z-10"
            >
              Rare treasures for the extraordinary
            </motion.p>

            <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.1, delay: 0.2 }}
              className="w-16 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"
            />
          </motion.div>

          {/* Premium Diamond Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-10 w-full overflow-hidden">
            {exoticShapes.map((shape, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.15,
                  delay: index * 0.01,
                  ease: "easeOut"
                }}
                className="group flex flex-col items-center text-center"
              >
                {/* Ultra Luxurious Diamond Container */}
                <div className="relative">
                  {/* <div className="absolute inset-0 bg-white/5 rounded-full blur-2xl group-hover:bg-white/15 transition-all duration-700 scale-150 animate-pulse"></div>
                  <div className="absolute inset-0 bg-white/10 rounded-full blur-xl group-hover:bg-white/25 transition-all duration-500 scale-125"></div> */}



                  <div className="w-36 h-36 sm:w-40 sm:h-40 md:w-44 md:h-44 relative flex items-center justify-center transform group-hover:scale-105 transition-all duration-500">
                    {shape.img && shape.img.src ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <OptimizedImage
                          src={shape.img}
                          alt={shape.alt || shape.title}
                          width={140}
                          height={140}
                          className="max-w-full max-h-full object-contain group-hover:scale-110 transition-all duration-700 filter drop-shadow-2xl"
                          priority={false}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Star className="w-12 h-12 text-gray-400 group-hover:text-white transition-colors duration-500" />
                      </div>
                    )}
                  </div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.1, delay: index * 0.02 + 0.05 }}
                  className="mt-8 relative"
                >
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.1, delay: index * 0.02 + 0.1 }}
                    className="w-20 h-0.5 bg-gradient-to-r from-transparent via-white/60 to-transparent mx-auto mb-4 group-hover:via-white transition-all duration-500"
                  />

                  <h3 className="text-white text-sm font-extralight tracking-[0.2em] uppercase group-hover:text-gray-200 group-hover:tracking-[0.3em] transition-all duration-500 relative">
                    {shape.title}
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></span>
                  </h3>

                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.1, delay: index * 0.02 + 0.15 }}
                    className="w-12 h-0.5 bg-gradient-to-r from-transparent via-white/40 to-transparent mx-auto mt-2 group-hover:via-white/80 transition-all duration-500"
                  />
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
      </div>
    </>
  );
}


