export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,mdx}",
    "./src/components/**/*.{js,jsx,mdx}",
    "./src/app/**/*.{js,jsx,mdx}",
    "./src/layout/**/*.{js,jsx,mdx}",
  ],
  safelist: [
    // Ensure footer gradient classes are never purged
    'bg-gradient-to-br',
    'from-gray-900',
    'via-black',
    'to-gray-900',
    'text-white',
    'footer-dark-bg'
  ],
  mode: "jit",
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
      screens: {
        '3xl': '1920px',     // Large desktop
        '4k': '2560px',      // 2K/4K monitors  
        '5k': '3840px',      // True 4K screens
      },
      fontSize: {
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
      },
      fontFamily: {
        montserrat: ['var(--font-montserrat)',"sans-serif"],
        ebgaramond: ["var(--font-ebgaramond)", "Georgia", "serif"],
        montserratClassic: ["Montserrat Classic", "sans-serif"],
        arimo: ["var(--font-arimo)", "sans-serif"],
        migra: ["var(--font-migra)", "serif"],
        oldstandardtt: ["Old Standard TT", "serif"], 
        opensans: ["Open Sans", "sans-serif"],
      },
      animation: {
        'border-run': 'border-run 2s linear infinite',
      },
      keyframes: {
        'border-run': {
          '0%': { 'background-position': '0% 0%' },
          '100%': { 'background-position': '100% 100%' }
        }
      },
    },
  },
  plugins: [require('@tailwindcss/aspect-ratio')],
} 



// tailwind.config.js
// export default {
//   content: [
//     "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
//     "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
//     "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
//     "./src/layout/**/*.{js,ts,jsx,tsx,mdx}",
//   ],
//   mode: "jit",
//   theme: {
//     extend: {
//       colors: {
//         background: "var(--background)",
//         foreground: "var(--foreground)",
//       },

//       // ✅ Added both 2K (2560px) and 4K (3840px) screen sizes
//       screens: {
       
       
//         '2xl': '1536px',     // Already in Tailwind by default
//         '3xl': '1920px',     // Optional large monitor
//         '4k': '2560px',      // For 2K monitors
//         '5k': '3840px',      // For true 4K screens
//       },

//       fontSize: {
//         sm: '0.875rem',
//         base: '1rem',
//         lg: '1.125rem',
//         xl: '1.25rem',
//         '2xl': '1.5rem',
//         '3xl': '1.875rem',
//        '4xl': 'clamp(2.25rem, 2vw, 3rem)', // scales from 36px to 48px
//         '5xl': 'clamp(3rem, 3vw, 4rem)',    // scales from 48px to 64px
//         '6xl': '3.75rem',
//         '7xl': '4.5rem',
//         '8xl': '6rem',
//       },

//       fontFamily: {
//         // Existing fonts
//         montserrat: ['var(--font-montserrat)', "sans-serif"],
//         ebgaramond: ["var(--font-ebgaramond)", "Georgia", "serif"],
//         montserratClassic: ["Montserrat Classic", "sans-serif"],
//         arimo: ["var(--font-arimo)", "sans-serif"],
//         migra: ["var(--font-migra)", "serif"],
//         oldstandardtt: ["Old Standard TT", "serif"],
//         opensans: ["Open Sans", "sans-serif"],

//         // Modern Production Fonts
//         poppins: ['var(--font-poppins)', 'sans-serif'],
//         playfair: ['var(--font-playfair)', 'serif'],
//         'dm-sans': ['var(--font-dm-sans)', 'sans-serif'],
//         'space-grotesk': ['var(--font-space-grotesk)', 'sans-serif'],
//         inter: ['var(--font-inter)', 'sans-serif'],
//       },

//       animation: {
//         'border-run': 'border-run 2s linear infinite',
//       },

//       keyframes: {
//         'border-run': {
//           '0%': { 'background-position': '0% 0%' },
//           '100%': { 'background-position': '100% 100%' },
//         },
//       },
//     },
//   },
//   plugins: [require('@tailwindcss/aspect-ratio')],
// };
