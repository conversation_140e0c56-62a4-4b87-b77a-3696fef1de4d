"use client";
import React, { useState, useEffect, useRef } from 'react';

const SmoothCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const ref = useRef(null);
  const rafRef = useRef(null);

  useEffect(() => {
    // Session storage for one-time behavior
    const counterKey = `smooth_counter_${targetNumber}_${suffix.replace(/\s+/g, '_')}`;
    const pageKey = 'page_timestamp';
    
    const currentTime = Date.now();
    const lastPageTime = sessionStorage.getItem(pageKey);
    
    // Check if new page load (>3 seconds gap)
    const isNewPageLoad = !lastPageTime || (currentTime - parseInt(lastPageTime)) > 3000;
    
    if (isNewPageLoad) {
      // Fresh page - clear all counters
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('smooth_counter_')) {
          sessionStorage.removeItem(key);
        }
      });
      sessionStorage.setItem(pageKey, currentTime.toString());
    }
    
    // Check if this counter already animated
    if (sessionStorage.getItem(counterKey)) {
      setCount(targetNumber);
      return;
    }

    // Intersection Observer with optimized settings
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isAnimating) {
          setIsAnimating(true);
          sessionStorage.setItem(counterKey, 'animated');
          
          // Ultra-smooth RAF animation
          const startTime = performance.now();
          
          const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Smooth easing function
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const newCount = Math.floor(easeOutCubic * targetNumber);
            
            setCount(newCount);
            
            if (progress < 1) {
              rafRef.current = requestAnimationFrame(animate);
            } else {
              setCount(targetNumber);
              setIsAnimating(false);
            }
          };
          
          rafRef.current = requestAnimationFrame(animate);
        }
      },
      { 
        threshold: 0.05, // Earlier trigger
        rootMargin: '100px 0px' // Start animation well before visible
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [targetNumber, suffix, duration, isAnimating]);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit',
        willChange: 'contents' // Optimize for frequent updates
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

export default SmoothCounter;
