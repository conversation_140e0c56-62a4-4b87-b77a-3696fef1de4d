// SEO Utility Functions and Constants

export const SEO_CONSTANTS = {
  SITE_NAME: "Diamond Atelier",
  SITE_URL: "https://diamondatelier.in",
  DEFAULT_TITLE: "Diamond Atelier - Premium Lab Grown Diamonds",
  DEFAULT_DESCRIPTION: "Discover premium lab-grown diamonds at Diamond Atelier. 10,000+ certified stones, ethical sourcing, exceptional quality.",
  DEFAULT_KEYWORDS: [
    "lab grown diamonds",
    "synthetic diamonds", 
    "ethical diamonds",
    "certified diamonds",
    "diamond manufacturer",
    "CVD diamonds",
    "HPHT diamonds"
  ],
  TWITTER_HANDLE: "@diamondatelier",
  FACEBOOK_APP_ID: "your-facebook-app-id",
};

// Generate structured data for different page types
export const generateStructuredData = {
  // Organization Schema
  organization: () => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": SEO_CONSTANTS.SITE_NAME,
    "url": SEO_CONSTANTS.SITE_URL,
    "logo": `${SEO_CONSTANTS.SITE_URL}/images/logo/Diamond Atelier logo (white).png`,
    "description": SEO_CONSTANTS.DEFAULT_DESCRIPTION,
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+91-XXXXXXXXXX",
      "contactType": "customer service",
      "availableLanguage": ["English", "Hindi"]
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "IN",
      "addressRegion": "Gujarat"
    },
    "sameAs": [
      "https://www.linkedin.com/company/diamond-atelier",
      "https://www.instagram.com/diamond_atelier",
      "https://www.facebook.com/diamondatelier"
    ]
  }),

  // Product Schema for diamonds
  product: (productData) => ({
    "@context": "https://schema.org",
    "@type": "Product",
    "name": productData.name || "Lab Grown Diamond",
    "description": productData.description || "Premium certified lab-grown diamond",
    "brand": {
      "@type": "Brand",
      "name": SEO_CONSTANTS.SITE_NAME
    },
    "manufacturer": {
      "@type": "Organization", 
      "name": SEO_CONSTANTS.SITE_NAME
    },
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceCurrency": "USD",
      "seller": {
        "@type": "Organization",
        "name": SEO_CONSTANTS.SITE_NAME
      }
    }
  }),

  // Article Schema for education pages
  article: (articleData) => ({
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": articleData.title,
    "description": articleData.description,
    "author": {
      "@type": "Organization",
      "name": SEO_CONSTANTS.SITE_NAME
    },
    "publisher": {
      "@type": "Organization",
      "name": SEO_CONSTANTS.SITE_NAME,
      "logo": {
        "@type": "ImageObject",
        "url": `${SEO_CONSTANTS.SITE_URL}/images/logo/Diamond Atelier logo (white).png`
      }
    },
    "datePublished": articleData.datePublished || new Date().toISOString(),
    "dateModified": articleData.dateModified || new Date().toISOString(),
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": articleData.url || SEO_CONSTANTS.SITE_URL
    }
  }),

  // FAQ Schema
  faq: (faqData) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqData.map(item => ({
      "@type": "Question",
      "name": item.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer
      }
    }))
  }),

  // Breadcrumb Schema
  breadcrumb: (breadcrumbs) => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  })
};

// Generate meta tags for different page types
export const generateMetaTags = {
  home: () => ({
    title: "Premium Lab Grown Diamonds - Certified & Ethical | Diamond Atelier",
    description: "Discover 10,000+ certified lab-grown diamonds at Diamond Atelier. Premium quality, ethical sourcing, expert manufacturing.",
    keywords: [
      "lab grown diamonds",
      "certified diamonds",
      "diamond manufacturer",
      "ethical diamonds",
      "synthetic diamonds"
    ]
  }),

  about: () => ({
    title: "About Diamond Atelier - Leading Lab Grown Diamond Manufacturer",
    description: "Learn about Diamond Atelier, India's premier lab-grown diamond manufacturer. Our story, expertise, and commitment to ethical diamond production.",
    keywords: [
      "about diamond atelier",
      "lab grown diamond manufacturer",
      "diamond company India",
      "ethical diamond production"
    ]
  }),

  contact: () => ({
    title: "Contact Diamond Atelier - Get in Touch with Diamond Experts", 
    description: "Contact Diamond Atelier for premium lab-grown diamonds. Get expert consultation, quotes, and personalized service.",
    keywords: [
      "contact diamond atelier",
      "diamond consultation",
      "diamond quotes",
      "lab grown diamond inquiry"
    ]
  }),

  education: (topic) => ({
    title: `${topic} - Diamond Education | Diamond Atelier`,
    description: `Learn about ${topic.toLowerCase()} in our comprehensive diamond education guide. Expert insights from Diamond Atelier.`,
    keywords: [
      `diamond ${topic.toLowerCase()}`,
      "diamond education",
      "diamond guide",
      "lab grown diamonds"
    ]
  })
};

// URL utilities
export const generateCanonicalUrl = (path) => {
  return `${SEO_CONSTANTS.SITE_URL}${path}`;
};

// Social media image utilities
export const generateOGImage = (title, description) => {
  // This would integrate with a service like Vercel OG or similar
  const params = new URLSearchParams({
    title: title,
    description: description
  });
  return `/api/og?${params.toString()}`;
};

const seoUtils = {
  SEO_CONSTANTS,
  generateStructuredData,
  generateMetaTags,
  generateCanonicalUrl,
  generateOGImage
};

export default seoUtils;
