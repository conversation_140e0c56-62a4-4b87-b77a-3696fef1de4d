"use client";
import React, { useState, useRef, useEffect } from 'react';

/**
 * OptimizedVideo Component
 * 
 * A performance-optimized video component that provides:
 * - Lazy loading with intersection observer
 * - Multiple format support (WebM, MP4)
 * - Automatic pause when out of view
 * - Loading states and error handling
 * - Bandwidth-aware loading
 */
const OptimizedVideo = ({
  src,
  poster,
  className = '',
  autoPlay = false,
  loop = true,
  muted = true,
  playsInline = true,
  controls = false,
  preload = 'none',
  playbackRate = 1,
  onPlay,
  onPause,
  onEnded,
  onError,
  pauseWhenOutOfView = true,
  loadOnlyInView = true,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!loadOnlyInView);
  const videoRef = useRef(null);

  // Intersection Observer for lazy loading and auto-pause
  useEffect(() => {
    if (!videoRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const inView = entry.isIntersecting;
        setIsInView(inView);

        if (videoRef.current) {
          if (inView) {
            // Resume video when in view
            if (autoPlay && videoRef.current.paused) {
              videoRef.current.play().catch(console.warn);
            }
          } else if (pauseWhenOutOfView) {
            // Pause video when out of view to save resources
            if (!videoRef.current.paused) {
              videoRef.current.pause();
            }
          }
        }
      },
      {
        rootMargin: '100px', // Start loading 100px before entering viewport
        threshold: 0.25 // Trigger when 25% of video is visible
      }
    );

    observer.observe(videoRef.current);
    return () => observer.disconnect();
  }, [autoPlay, pauseWhenOutOfView]);

  // Set playback rate when video loads
  useEffect(() => {
    if (videoRef.current && isLoaded) {
      videoRef.current.playbackRate = playbackRate;
    }
  }, [playbackRate, isLoaded]);

  // Generate multiple video sources for better compression
  const generateSources = (originalSrc) => {
    if (typeof originalSrc !== 'string') return [{ src: originalSrc }];
    
    const basePath = originalSrc.replace(/\.[^/.]+$/, '');
    
    return [
      { src: `${basePath}.webm`, type: 'video/webm' },
      { src: `${basePath}.mp4`, type: 'video/mp4' },
      { src: originalSrc, type: 'video/mp4' } // fallback
    ];
  };

  const handleLoadedData = () => {
    setIsLoaded(true);
    if (videoRef.current) {
      videoRef.current.playbackRate = playbackRate;
    }
  };

  const handlePlay = (e) => {
    setIsPlaying(true);
    onPlay?.(e);
  };

  const handlePause = (e) => {
    setIsPlaying(false);
    onPause?.(e);
  };

  const handleEnded = (e) => {
    setIsPlaying(false);
    onEnded?.(e);
  };

  const handleError = (e) => {
    setHasError(true);
    onError?.(e);
  };

  // Loading placeholder
  const LoadingPlaceholder = () => (
    <div 
      className={`bg-gray-800 animate-pulse flex items-center justify-center ${className}`}
      style={{ aspectRatio: '16/9' }}
    >
      <div className="text-gray-400 text-sm">Loading video...</div>
    </div>
  );

  // Error fallback
  const ErrorFallback = () => (
    <div 
      className={`bg-gray-900 flex items-center justify-center ${className}`}
      style={{ aspectRatio: '16/9' }}
    >
      <div className="text-gray-500 text-sm">Video failed to load</div>
    </div>
  );

  if (hasError) {
    return <ErrorFallback />;
  }

  if (!isInView && loadOnlyInView) {
    return <LoadingPlaceholder />;
  }

  const sources = generateSources(src);

  return (
    <div className="relative">
      {!isLoaded && <LoadingPlaceholder />}
      
      <video
        ref={videoRef}
        className={`${className} ${!isLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        autoPlay={autoPlay && isInView}
        loop={loop}
        muted={muted}
        playsInline={playsInline}
        controls={controls}
        preload={preload}
        poster={poster}
        onLoadedData={handleLoadedData}
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
        onError={handleError}
        {...props}
      >
        {sources.map((source, index) => (
          <source key={index} src={source.src} type={source.type} />
        ))}
        Your browser does not support the video tag.
      </video>

      {/* Loading indicator overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      )}
    </div>
  );
};

export default OptimizedVideo;

// Utility function to check if user prefers reduced data usage
export const useReducedDataMode = () => {
  const [reducedData, setReducedData] = useState(false);

  useEffect(() => {
    // Check for save-data header or slow connection
    if ('connection' in navigator) {
      const connection = navigator.connection;
      const slowConnection = connection.effectiveType === 'slow-2g' || 
                           connection.effectiveType === '2g' || 
                           connection.saveData;
      setReducedData(slowConnection);
    }
  }, []);

  return reducedData;
};
