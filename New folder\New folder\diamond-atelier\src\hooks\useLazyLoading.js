"use client";
import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
/**
 * Custom hook for lazy loading images
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.rootMargin - Margin around the root (default: '50px')
 * @param {number} options.threshold - Threshold for intersection (default: 0.1)
 * @param {boolean} options.triggerOnce - Whether to trigger only once (default: true)
 * @returns {Object} - { ref, isInView, isLoaded }
 */
export const useLazyLoading = (options = {}) => {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    triggerOnce = true
  } = options;

  const [isInView, setIsInView] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          if (triggerOnce) {
            observer.disconnect();
          }
        } else if (!triggerOnce) {
          setIsInView(false);
        }
      },
      {
        rootMargin,
        threshold
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [rootMargin, threshold, triggerOnce]);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  return {
    ref,
    isInView,
    isLoaded,
    handleLoad
  };
};

/**
 * LazyImage Component
 * A simple lazy loading image component for regular img tags
 */
export const LazyImage = ({
  src,
  alt,
  className = '',
  width,
  height,
  style,
  onLoad,
  onError,
  placeholder = true,
  ...props
}) => {
  const { ref, isInView, isLoaded, handleLoad } = useLazyLoading();
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = (e) => {
    handleLoad();
    onLoad?.(e);
  };

  const handleImageError = (e) => {
    setHasError(true);
    onError?.(e);
  };

  // Placeholder component
  const Placeholder = () => (
    <div 
      className={`bg-gray-800 animate-pulse flex items-center justify-center ${className}`}
      style={{ width, height, ...style }}
    >
      {placeholder && (
        <div className="text-gray-400 text-sm">Loading...</div>
      )}
    </div>
  );

  // Error fallback
  const ErrorFallback = () => (
    <div 
      className={`bg-gray-900 flex items-center justify-center ${className}`}
      style={{ width, height, ...style }}
    >
      <div className="text-gray-500 text-sm">Failed to load</div>
    </div>
  );

  if (hasError) {
    return <ErrorFallback />;
  }

  return (
    <div ref={ref} className="relative">
      {!isInView ? (
        <Placeholder />
      ) : (
        <>
          {!isLoaded && <Placeholder />}
  <Image
          src={src}
          alt={alt}
          className={className}
          width={width}
          height={height}
          style={{
            ...style,
            opacity: isLoaded ? 1 : 0,
            transition: 'opacity 0.3s ease-in-out'
          }}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
          {...props}
        />
        </>
      )}
    </div>
  );
};

/**
 * Utility function to add lazy loading to existing img elements
 * Call this in useEffect to upgrade existing images
 */
export const addLazyLoadingToImages = (containerSelector = 'body') => {
  const images = document.querySelectorAll(`${containerSelector} img[data-lazy]`);
  
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        const src = img.getAttribute('data-src');
        
        if (src) {
          img.src = src;
          img.removeAttribute('data-src');
          img.removeAttribute('data-lazy');
          imageObserver.unobserve(img);
        }
      }
    });
  }, {
    rootMargin: '50px'
  });

  images.forEach(img => imageObserver.observe(img));

  return () => {
    imageObserver.disconnect();
  };
};

export default useLazyLoading;
