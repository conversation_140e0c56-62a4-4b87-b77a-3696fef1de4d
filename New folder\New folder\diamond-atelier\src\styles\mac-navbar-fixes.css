/* Mac-specific navbar performance and visibility fixes */

/* Force hardware acceleration on Mac */
header {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* Mac Safari backdrop-filter optimization */
@supports (-webkit-backdrop-filter: blur(10px)) {
  .mac-navbar-blur {
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    backdrop-filter: blur(20px) saturate(180%);
  }
}

/* Mac text rendering optimization */
.mac-text-optimize {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Mac scroll performance */
.mac-scroll-optimize {
  -webkit-overflow-scrolling: touch;
  will-change: transform;
}

/* Mac color profile fixes */
.mac-white-text {
  color: rgb(255, 255, 255) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Mac logo visibility enhancement */
.mac-logo-enhance {
  filter: brightness(0) invert(1) drop-shadow(0 0 30px rgba(255,255,255,0.9)) !important;
  -webkit-filter: brightness(0) invert(1) drop-shadow(0 0 30px rgba(255,255,255,0.9)) !important;
}

/* Mac navigation hover states */
.mac-nav-hover:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: rgb(255, 255, 255) !important;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

/* Mac active navigation state */
.mac-nav-active {
  background-color: rgba(255, 255, 255, 0.25) !important;
  color: rgb(255, 255, 255) !important;
  box-shadow: 0 2px 10px rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

/* Mac mobile menu optimization */
.mac-mobile-menu {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Mac transition optimization */
.mac-smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mac-specific media queries */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  /* Retina display optimizations */
  .mac-retina-text {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* Mac dark mode compatibility */
@media (prefers-color-scheme: dark) {
  .mac-dark-mode-text {
    color: rgb(255, 255, 255) !important;
  }
}

/* Mobile-specific fixes */
@media (max-width: 640px) {
  /* Hamburger button mobile fixes */
  .mobile-hamburger-fix {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 2px solid rgba(255, 255, 255, 0.5) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    backdrop-filter: blur(10px) !important;
  }

  /* Mobile logo text protection */
  .mobile-logo-safe {
    padding-left: 60px !important;
    padding-right: 60px !important;
  }

  /* Mobile header container */
  .mobile-header-container {
    padding-left: 64px !important;
    padding-right: 16px !important;
  }
}
