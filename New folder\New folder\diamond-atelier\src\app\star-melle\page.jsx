import React from "react";
import ClientStarMellePage from "./ClientStarMellePage";

// Server-side data preparation
async function getStarMelleData() {
  const heroContent = {
    title: "Star Meleee Diamonds",
    subtitle: "Exquisite Small Diamonds for Extraordinary Brilliance",
    description: "Discover our premium collection of star melle diamonds - perfectly cut small diamonds that add unmatched sparkle and elegance to your finest jewelry pieces."
  };

  const features = [
    {
      title: "Premium Quality",
      description: "Each star meleee diamond is carefully selected for exceptional clarity, cut, and brilliance",
      icon: "star"
    },
    {
      title: "Precision Cut",
      description: "Expert craftsmanship ensures maximum light reflection and fire in every small diamond",
      icon: "diamond"
    },
    {
      title: "Certified Excellence",
      description: "All our star melle diamonds come with proper certification and quality assurance",
      icon: "certificate"
    },
    {
      title: "Versatile Applications",
      description: "Perfect for accent stones, pavé settings, and intricate jewelry designs",
      icon: "settings"
    }
  ];

  const specifications = [
    {
      category: "Size Range",
      details: ["0.005ct to 0.25ct", "Various pointer sizes available", "Custom sizing options"]
    },
    {
      category: "Quality Grades",
      details: ["Color: D to J", "Clarity: FL to SI2", "Cut: Excellent to Good"]
    },
    {
      category: "Applications",
      details: ["Pavé Settings", "Accent Stones", "Halo Designs", "Eternity Bands"]
    },
    {
      category: "Certification",
      details: ["GIA Certified", "IGI Certified", "Quality Guaranteed"]
    }
  ];

  const collections = [
    {
      name: "Classic Round Meleee",
      description: "Traditional round brilliant cut melle diamonds for timeless elegance",
      image: "/images/melle/round-melle.jpg",
      sizes: "0.005ct - 0.25ct",
      applications: ["Pavé Settings", "Halo Rings", "Tennis Bracelets"]
    },
    {
      name: "Princess Cut Meleee",
      description: "Square princess cut melle diamonds for modern sophisticated designs",
      image: "/images/melle/princess-melle.jpg",
      sizes: "0.01ct - 0.20ct",
      applications: ["Channel Settings", "Geometric Designs", "Contemporary Jewelry"]
    },
    {
      name: "Baguette Meleee",
      description: "Elegant rectangular baguette cut melle diamonds for linear designs",
      image: "/images/melle/baguette-melle.jpg",
      sizes: "0.02ct - 0.15ct",
      applications: ["Side Stones", "Art Deco Designs", "Linear Patterns"]
    }
  ];

  return {
    heroContent,
    features,
    specifications,
    collections
  };
}

// Server-side component
export default async function Page() {
  const pageData = await getStarMelleData();
  return <ClientStarMellePage {...pageData} />;
}
