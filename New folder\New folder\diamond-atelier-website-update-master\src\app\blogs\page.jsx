import React from 'react'
import Image from 'next/image'
import background from '../../../public/images/banner/blog-bg.png'
import diamond from '../../../public/images/banner/diamond-blog.png'
import dynamic from 'next/dynamic';

// Dynamic import for AOS wrapper
const AOSWrapper = dynamic(() => import("@/components/common/AOSWrapper"));

// Metadata for SEO
export const metadata = {
  title: "Diamond Atelier Blog - Lab Grown Diamond Insights & News",
  description: "Explore Diamond Atelier's blog for insights on lab grown diamonds, industry news, and expert tips. Where artists shape stones into beautiful diamonds.",
  keywords: "diamond blog, lab grown diamonds news, diamond insights, diamond atelier blog, diamond industry",
  openGraph: {
    title: "Diamond Atelier Blog - Lab Grown Diamond Insights",
    description: "Where artists shape stones - Explore our blog for diamond insights and industry news",
    type: "website",
  },
};

const page = () => {
    return (
        <AOSWrapper>
            <div className='relative h-screen'>
                <div className="absolute inset-0 -z-10">
                    <Image
                        src={background}
                        alt="background Image"
                        layout="fill"
                        objectFit="fill"
                        priority
                    />
                    <div className='relative md:pt-48 pt-24 xl:pt-48 '>
                        <h2 data-aos="fade-down" className='text-[#6b6d6d] text-center text-2xl font-montserrat font-medium'>At Diamond Atelier</h2>
                        <p data-aos="fade-down" className='text-[#6b6d6d] text-center text-6xl font-medium' style={{ fontFamily: 'Edwardian Script ITC' }}>artist shapes stones</p>
                        <Image
                            src={diamond}
                            alt='diamond'
                            className="object-contain mx-auto"
                            data-aos="fade-down"
                        />
                    </div>
                </div>
            </div>
        </AOSWrapper>
    )
}

export default page