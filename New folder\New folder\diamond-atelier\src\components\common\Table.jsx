

import React from "react";
import { motion } from "framer-motion";

const Table = ({ tableData }) => {
    return (
        <div className="p-6 cursor-pointer">
            {tableData.map(({ headers, rows, colSpans }, tableIndex) => (
                <motion.div
                    key={tableIndex}
                    className="w-full overflow-x-auto mt-6 rounded-lg border border-[#777678]"
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: tableIndex * 0.3 }}
                    viewport={{ once: true }}
                    
                >
                    {/* Responsive Scrollable Table */}
                    <div className="min-w-[700px] w-full">
                        <table className="w-full border-collapse text-sm font-montserrat">
                            <thead>
                                {headers.map((headerRow, rowIndex) => (
                                    <tr key={rowIndex} className="bg-[#777678] text-white">
                                        {headerRow.map((header, colIndex) => (
                                            <th
                                                key={colIndex}
                                                className="p-3 border border-[#6b6d69] text-center whitespace-nowrap"
                                                rowSpan={colSpans?.rowSpan?.[rowIndex]?.[colIndex] || 1}
                                                colSpan={colSpans?.colSpan?.[rowIndex]?.[colIndex] || 1}
                                            >
                                                {header}
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            </thead>
                            <tbody>
                                {rows.map((row, rowIndex) => (
                                    <tr
                                        key={rowIndex}
                                        className={`${rowIndex % 2 === 0 ? "bg-white" : "bg-[#f2f2f2]"} text-center`}
                                    >
                                        {row.map((cell, colIndex) => (
                                            <td
                                                key={colIndex}
                                                className={`p-3 border border-[#777678] whitespace-nowrap ${
                                                    colIndex === 0
                                                        ? "text-[#6b6d6d] font-medium bg-white"
                                                        : "bg-black text-white"
                                                }`}
                                            >
                                                {cell}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </motion.div>
            ))}
        </div>
    );
};

export default Table;
