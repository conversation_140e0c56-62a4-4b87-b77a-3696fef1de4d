"use client";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { FaFileContract, FaShieldAlt, FaCheckCircle, FaInfoCircle, FaHandshake, FaClock } from "react-icons/fa";
import Image from "next/image";
import LoadingScreen from "../../utils/LoadingScreen";

export default function ClientCreditTermsPage({ terms}) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 800);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <LoadingScreen />;

  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: "easeOut" } }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section - Inspired by Tiffany & Co elegance */}
      <section className="relative pt-20 sm:pt-24 md:pt-32 pb-12 sm:pb-16 md:pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Sophisticated Background Gradient - Cartier inspired */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
          <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_30%,rgba(255,255,255,0.02)_50%,transparent_70%)]"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* Elegant Badge - Harry Winston inspired */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-6">
              <FaFileContract className="w-5 h-5 text-white" />
              <span className="text-white font-medium tracking-wider uppercase text-sm">
                Business Terms
              </span>
            </div>

            {/* Sophisticated Typography - De Beers inspired */}
            <h1 className="text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light text-white mb-6 sm:mb-8 tracking-[0.1em] sm:tracking-[0.2em] uppercase leading-tight">
              CREDIT & MEMO TERMS
            </h1>
            <div className="h-px w-24 sm:w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6 sm:mb-8"></div>

            <p className="text-base sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6 font-light max-w-4xl mx-auto tracking-wide px-2">
              Professional Diamond Trade Agreements
            </p>

            <p className="text-sm sm:text-base text-white/70 max-w-3xl mx-auto leading-relaxed mb-8 px-2">
              Comprehensive business terms and conditions designed to facilitate smooth diamond trade relationships.
              Clear, fair, and professional terms for all our business partners.
            </p>

            {/* Luxury Feature Badges - Bulgari inspired */}
            <div className="flex flex-wrap justify-center gap-4 mt-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex items-center space-x-3 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20"
              >
                <FaShieldAlt className="text-white w-4 h-4" />
                <span className="text-white font-light tracking-wide uppercase text-sm">Secure Terms</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="flex items-center space-x-3 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20"
              >
                <FaHandshake className="text-white w-4 h-4" />
                <span className="text-white font-light tracking-wide uppercase text-sm">Fair Partnership</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex items-center space-x-3 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20"
              >
                <FaClock className="text-white w-4 h-4" />
                <span className="text-white font-light tracking-wide uppercase text-sm">Flexible Terms</span>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Terms Section - Inspired by luxury jewelry houses */}
      <section className="relative py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.05)_0%,transparent_70%)]"></div>
        </div>

        <div className="relative max-w-7xl mx-auto z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
              Our Business <span className="italic font-light">Terms</span>
            </h2>
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6"></div>
            <p className="text-base text-white/80 max-w-3xl mx-auto tracking-wide">
              Understanding our credit and memo terms ensures smooth business transactions and long-term partnerships.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {terms.map((term, index) => (
              <motion.div
                key={term.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-lg overflow-hidden border border-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105"
              >
                <div className="p-6">
                  <div className="text-center mb-6">
                    <div className="text-3xl mb-4 opacity-80">{term.icon}</div>
                    <h3 className="text-xl font-light text-white mb-3 tracking-wide uppercase">{term.title}</h3>
                    <p className="text-white/80 text-sm leading-relaxed">{term.description}</p>
                  </div>

                  <div className="space-y-3">
                    {term.details.map((detail, idx) => (
                      <motion.div
                        key={idx}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.6, delay: (index * 0.2) + (idx * 0.1) }}
                        className="flex items-start space-x-3"
                      >
                        <FaCheckCircle className="text-white/70 flex-shrink-0 mt-1 w-3 h-3" />
                        <span className="text-white/70 text-xs leading-relaxed">{detail}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Important Notice Section */}
      <section className="relative py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/70 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.03)_0%,transparent_70%)]"></div>
        </div>

        <div className="relative max-w-7xl mx-auto z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="bg-gradient-to-br from-red-900/10 to-red-800/5 border border-red-700/20 rounded-lg p-8 backdrop-blur-sm"
          >
            <div className="flex items-center space-x-4 mb-6">
              <FaInfoCircle className="text-red-400/80 text-xl" />
              <h3 className="text-xl font-light text-white tracking-wide uppercase">Important Notice</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-white/70">
              <div>
                <h4 className="font-light text-white mb-2 text-sm uppercase tracking-wide">Credit Approval Process</h4>
                <p className="text-xs leading-relaxed">All credit applications are subject to approval and verification. Processing time may vary based on documentation completeness.</p>
              </div>
              <div>
                <h4 className="font-light text-white mb-2 text-sm uppercase tracking-wide">Terms Compliance</h4>
                <p className="text-xs leading-relaxed">Strict adherence to all terms and conditions is mandatory. Violations may result in immediate termination of credit facilities.</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact Section - Luxury CTA inspired by top diamond houses */}
      <section className="relative py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/30 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
        </div>

        <div className="relative max-w-4xl mx-auto text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm p-8 md:p-12 rounded-lg border border-white/20"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
              Ready to Apply for <span className="italic font-light">Credit?</span>
            </h2>
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6"></div>
            <p className="text-base text-white/80 mb-8 max-w-2xl mx-auto tracking-wide">
              Contact our team to discuss credit applications, memo agreements, and start your business partnership with Diamond Atelier.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push('/contact')}
                className="bg-gradient-to-r from-white to-gray-100 text-black px-8 py-3 rounded-lg font-medium hover:from-gray-100 hover:to-white transition-all duration-300 tracking-wide uppercase text-sm cursor-pointer"
              >
                Apply for Credit
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push('/contact')}
                className="bg-transparent border border-white/30 text-white px-8 py-3 rounded-lg font-medium hover:bg-white/10 hover:border-white/50 transition-all duration-300 tracking-wide uppercase text-sm cursor-pointer"
              >
                Contact Support
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
