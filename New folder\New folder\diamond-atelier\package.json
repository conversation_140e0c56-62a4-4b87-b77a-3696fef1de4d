{"name": "diamond-atelier-website", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "build:static": "next build", "build:server": "next build && next start", "analyze": "ANALYZE=true next build", "analyze:server": "BUNDLE_ANALYZE=server next build", "analyze:browser": "BUNDLE_ANALYZE=browser next build", "add-lazy-loading": "node scripts/add-lazy-loading.js", "compress-evolution": "node scripts/compress-evolution-image.js", "optimize-vitals": "node scripts/optimize-core-web-vitals.js"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "aos": "^2.3.4", "axios": "^1.10.0", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.6.2", "lottie-react": "^2.4.1", "lucide-react": "^0.510.0", "next": "15.2.4", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-parallax-tilt": "^1.7.295", "three": "^0.176.0", "three-stdlib": "^2.36.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.1", "@tailwindcss/postcss": "^4", "@types/node": "22.14.0", "@types/react": "19.1.0", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4"}}