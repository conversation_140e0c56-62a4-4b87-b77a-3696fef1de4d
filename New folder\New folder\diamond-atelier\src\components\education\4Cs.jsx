"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";

const The4Cs = () => {
  const [activeGrade, setActiveGrade] = useState("excellent");

  const handleButtonClick = (target) => {
    setActiveGrade(target);
  };

  const gradeMap = {
    excellent: "/images/4cs/01ideal.gif",
    veryGood: "/images/4cs/02Shallow.gif",
    good: "/images/4cs/03DeepCut.gif",
    fair: "/images/4cs/4cs2.jpg",
    poor: "/images/4cs/4c4.jpg",
  };

  const gradeLabels = {
    excellent: "Excellent",
    veryGood: "Very Good",
    good: "Good",
    fair: "Fair",
    poor: "Poor",
  };

  const sections = [
    { id: "difference", title: "Difference" },
    { id: "methods", title: "Methods" },
    { id: "whyLab", title: "Why Lab Grown" },
    { id: "myths", title: "Myths & Facts" },
    { id: "evolution", title: "Evolution" },
    { id: "the4cs", title: "The 4Cs" },
    { id: "anatomy", title: "Anatomy" },
  ];

  const ButtonComponent = ({ target, isActive }) => (
    <button
      aria-pressed={isActive}
      className={`relative px-4 md:px-6 py-2.5 md:py-3 text-sm md:text-base font-light tracking-wide transition-all duration-500 rounded-lg overflow-hidden group
        ${
          isActive
            ? "bg-gradient-to-r from-amber-500 to-amber-600 text-black shadow-lg shadow-amber-500/25 border border-amber-400"
            : "bg-slate-800/60 text-slate-300 border border-slate-600/50 hover:border-amber-400/50 hover:text-amber-200"
        }`}
      onClick={() => handleButtonClick(target)}
    >
      <span className="relative z-10">{gradeLabels[target]}</span>
      {!isActive && (
        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/0 to-amber-500/0 group-hover:from-amber-500/10 group-hover:to-amber-500/5 transition-all duration-500"></div>
      )}
    </button>
  );

  return (
    <div className="min-h-screen bg-black text-white font-serif">
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-black"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-light text-white mb-8 tracking-[0.2em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              THE 4CS OF DIAMONDS
            </h1>
            <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base md:text-lg text-white/90 mb-6 font-light max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Discover the four pillars that define diamond perfection
            </p>
          </motion.div>
          {/* Video Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="mt-16 max-w-2xl mx-auto"
          >
            <div className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <video
                className="w-full h-auto rounded-lg max-h-[400px] object-contain"
                muted
                autoPlay
                playsInline
                loop
                preload="auto"
                aria-label="Understanding the 4Cs of diamonds"
              >
                <source src="/images/4cs/4cs.mp4" type="video/mp4" />
              </video>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-8"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              UNDERSTANDING DIAMOND QUALITY STANDARDS
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base md:text-lg text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              A diamond's quality is determined by the 4C's: Cut, Color, Clarity, and Carat. The grades a diamond carries in these four areas give a clear idea of the diamond's quality, and by extension the right price you should be willing to pay.
            </p>
            <div className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-4xl mx-auto">
              <p className="text-sm text-white/80 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                The 4Cs are Cut, Color, Clarity, and Carat. Below is an interactive guide to understanding diamond cut grades and their impact on brilliance and fire.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Cut Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              TYPES OF CUT
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
          </motion.div>

          {/* Cut Types */}
          <div className="space-y-16">
            {/* Ideal Cut */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20"
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-lg font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                    Ideal Cut
                  </h3>
                  <p className="text-sm text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                    In a well cut diamond, the light strikes each pavilion facet at an angle which allows most of the light to reflect back to the crown (top), creating maximum brilliance and fire.
                  </p>
                </div>
                <div className="flex justify-center">
                  <div className="w-40 h-40 bg-white/10 rounded-lg p-4 border border-white/20">
                    <img
                      src="/images/4cs/01ideal.gif"
                      alt="Ideal Cut Diamond"
                      className="w-full h-full object-contain"
                      draggable={false}
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Shallow Cut */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20"
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-lg font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                    Shallow Cut
                  </h3>
                  <p className="text-sm text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                    If the diamond cut is too shallow, entering light strikes the pavilion facet at a low angle and passes through the facet, reducing brilliance and sparkle.
                  </p>
                </div>
                <div className="flex justify-center">
                  <div className="w-40 h-40 bg-white/10 rounded-lg p-4 border border-white/20">
                    <img
                      src="/images/4cs/02Shallow.gif"
                      alt="Shallow Cut Diamond"
                      className="w-full h-full object-contain"
                      draggable={false}
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Deep Cut */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20"
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-lg font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                    Deep Cut
                  </h3>
                  <p className="text-sm text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                    If the diamond cut is too deep, entering light strikes the first pavilion facet at an angle sharp enough to reflect to the second pavilion, reducing the diamond's brilliance.
                  </p>
                </div>
                <div className="flex justify-center">
                  <div className="w-40 h-40 bg-white/10 rounded-lg p-4 border border-white/20">
                    <img
                      src="/images/4cs/03DeepCut.gif"
                      alt="Deep Cut Diamond"
                      className="w-full h-full object-contain"
                      draggable={false}
                    />
                  </div>
                </div>
              </div>
            </motion.div>

          </div>
        </div>
      </section>

      {/* Cut Grade Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              CUT GRADE
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
          </motion.div>

          {/* Cut Grades */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Excellent */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500"
            >
              <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                EXCELLENT
              </h3>
              <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
              <p className="text-xs text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Maximum scintillation, brilliance and fire. Light enters and reflects back with minimal loss. Top 3% of diamonds.
              </p>
            </motion.div>

            {/* Very Good */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500"
            >
              <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                VERY GOOD
              </h3>
              <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
              <p className="text-xs text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Reflects most light with superior sparkle, brilliance and fire. Similar appearance to ideal cut.
              </p>
            </motion.div>

            {/* Good */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500"
            >
              <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                GOOD
              </h3>
              <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
              <p className="text-xs text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Reflecting most of the light entering the diamond, this diamond cut has above average beauty. Won't have as much brilliance and fire as higher cuts.
              </p>
            </motion.div>

            {/* Fair */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500"
            >
              <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                FAIR
              </h3>
              <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
              <p className="text-xs text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Light exits through bottom or sides instead of top. Still quality but not as well performing as finer cuts.
              </p>
            </motion.div>

            {/* Poor */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500"
            >
              <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                POOR
              </h3>
              <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
              <p className="text-xs text-white/90 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Majority of light lost through bottom or sides. Noticeable decrease in sparkle visible to untrained eye.
              </p>
            </motion.div>
          </div>

          {/* Cut Grade Chart */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="mt-16 flex justify-center"
          >
            <div className="bg-white/10 rounded-lg p-6 border border-white/20">
              <img
                src="/images/4cs/4cs2.jpg"
                alt="Cut Grade Chart"
                className="w-full max-w-2xl h-auto object-contain"
                draggable={false}
              />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Color and Clarity Sections */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto space-y-20">
          {/* Color Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                COLOR
              </h2>
              <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
              <p className="text-sm text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Diamond color grading evaluates the absence of color in a diamond, with D being completely colorless and Z having noticeable color.
              </p>
            </div>
            <div className="flex justify-center">
              <div className="bg-white/10 rounded-lg p-6 border border-white/20">
                <img
                  src="/images/4cs/colorshade.png"
                  alt="Diamond Color Scale D to N"
                  className="w-full max-w-4xl h-auto object-contain"
                  draggable={false}
                />
              </div>
            </div>
          </motion.div>

          {/* Clarity Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                CLARITY
              </h2>
              <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
              <p className="text-sm text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                A Diamond's Clarity grade evaluates how clean a diamond is from both inclusions and blemishes.
              </p>
            </div>
            <div className="flex justify-center">
              <div className="bg-white/10 rounded-lg p-6 border border-white/20">
                <img
                  src="/images/4cs/4c4.jpg"
                  alt="Diamond Clarity Scale IF to I3"
                  className="w-full max-w-4xl h-auto object-contain"
                  draggable={false}
                />
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default The4Cs;
