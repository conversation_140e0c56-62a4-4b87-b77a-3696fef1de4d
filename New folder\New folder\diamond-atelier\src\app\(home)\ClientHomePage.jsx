"use client";
import React, { useEffect, useState, useRef, useMemo, useCallback } from "react";
import Link from "next/link";
import { motion, useInView } from "framer-motion";
import { ChevronDown } from "lucide-react";
import LoadingScreen from "../../utils/LoadingScreen";
import { useHeaderHeight } from "@/hooks/useHeaderHeight";
import MemoryOptimizedImage from "@/components/common/MemoryOptimizedImage";
import INPOptimizer from "@/components/common/INPOptimizer";
import PermanentCounter, { resetAllCounters } from "@/components/PermanentCounter";
import logo from "../../../public/images/logo/Diamond Atelier logo (white).png";
// Using TestCounter component for reliable counter animations

const fadeRight = {
  hidden: { opacity: 0, x: -40 },
  visible: { opacity: 1, x: 0, transition: { duration: 1.2, ease: "easeOut" } },
};

const fadeLeft = {
  hidden: { opacity: 0, x: 40 },
  visible: { opacity: 1, x: 0, transition: { duration: 1.2, ease: "easeOut" } },
};

const textContainer = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.35 } },
};

const fadeUpText = {
  hidden: { opacity: 0, y: 25 },
  visible: { opacity: 1, y: 0, transition: { duration: 1, ease: "easeOut" } },
};

// Performance-optimized video component with better lazy loading - Memoized
const LazyVideo = React.memo(({ src, playbackRate = 1, loop = true }) => {
  const ref = useRef(null);
  const videoRef = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  useEffect(() => {
    if (isInView && videoRef.current) {
      // Set playback rate after video starts playing
      const handlePlay = () => {
        if (videoRef.current) {
          videoRef.current.playbackRate = playbackRate;
        }
      };
      videoRef.current.addEventListener('play', handlePlay);
      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener('play', handlePlay);
        }
      };
    }
  }, [isInView, playbackRate]);

  return (
    <div ref={ref} className="w-full">
      {isInView ? (
        <video
          ref={videoRef}
          src={src}
          muted
          autoPlay
          playsInline
          loop={loop}
          preload="metadata" // Better than "none" for performance
          className="w-full max-w-[400px] xs:max-w-[450px] sm:max-w-[550px] md:max-w-[500px] lg:max-w-[600px] xl:max-w-[700px] 2xl:max-w-[900px] 3xl:max-w-[1100px] 4k:max-w-[1800px] 5k:max-w-[1800px] aspect-[4/3] object-contain rounded-lg shadow-xl"
        />
      ) : (
        // Placeholder while loading
        <div className="w-full max-w-[400px] xs:max-w-[450px] sm:max-w-[550px] md:max-w-[500px] lg:max-w-[600px] xl:max-w-[700px] 2xl:max-w-[900px] 3xl:max-w-[1100px] 4k:max-w-[1800px] 5k:max-w-[1800px] aspect-[4/3] bg-gray-800 rounded-lg shadow-xl animate-pulse flex items-center justify-center">
          <div className="text-gray-400 text-sm">Loading video...</div>
        </div>
      )}
    </div>
  );
});

export default function ClientHomePage({ sections, matchingLayoutVideos }) {
  const [isLoading, setIsLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [videoSlideIndex] = useState(0); // Fixed to 0 since slider buttons are disabled

  // Memoize expensive calculations
  const totalSections = useMemo(() => sections.length + 2, [sections.length]);

  // Initialize dynamic header height calculation
  useHeaderHeight();

  // Removed unused scroll handler for better performance

  // advanceSlide function removed as it's not being used (buttons are commented out)

  useEffect(() => {
    // Reduced loading time for better performance
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800); // Reduced from 2000ms to 800ms

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    let isScrolling = false;
    let lastScrollTime = 0;
    const scrollCooldown = 1000;

    const scrollToSection = (direction) => {
      const currentTime = Date.now();
      if (currentTime - lastScrollTime < scrollCooldown) return;
      lastScrollTime = currentTime;
      if (direction === "down") {
        setCurrentIndex((prev) => Math.min(prev + 1, totalSections - 1));
      } else {
        setCurrentIndex((prev) => Math.max(prev - 1, 0));
      }
    };

    const handleScroll = (e) => {
      e.preventDefault();
      if (isScrolling) return;
      isScrolling = true;
      if (e.deltaY > 0) scrollToSection("down");
      else scrollToSection("up");
      setTimeout(() => {
        isScrolling = false;
      }, 50);
    };

    const handleKeyDown = (e) => {
      if (e.key === "ArrowDown" || e.key === "ArrowUp") {
        e.preventDefault();
        scrollToSection(e.key === "ArrowDown" ? "down" : "up");
      }
    };

    window.addEventListener("wheel", handleScroll, { passive: false });
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("wheel", handleScroll);
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [totalSections]);

  useEffect(() => {
    const scrollToCurrentSection = () => {
      const elements = document.querySelectorAll(".full-section");
      if (currentIndex < elements.length) {
        elements[currentIndex]?.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    };
    scrollToCurrentSection();
  }, [currentIndex]);

  if (isLoading) {
    return <LoadingScreen />;
  }
  
  return (
    <>
      <INPOptimizer /> 
      <div className="bg-black text-white font-montserrat">
        <div className="full-section relative w-full h-screen overflow-hidden">
          {/* Background Video */}
          <video
            className="absolute inset-0 w-full h-full object-cover"
            autoPlay
            muted
            loop
            playsInline
            preload="metadata"
          >
            <source src="/images/about/YouCut_20220725_145211608.mp4" type="video/mp4" />
          </video>

        {/* Sophisticated Overlay Gradients */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none" />

        {/* Animated Particles/Sparkles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-60"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                scale: 0
              }}
              animate={{
                y: [null, -100],
                opacity: [0.6, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: "easeOut"
              }}
            />
          ))}
        </div>

        {/* Main Content Container */}
        <div className="absolute inset-0 flex flex-col items-center justify-center z-20 px-6">

          {/* Logo Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="text-center mb-8"
          >
            <div className="relative group">
            <h1
              className="text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-white/95 transition-all duration-700"
              style={{
                fontFamily: 'Times New Roman, serif',
                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',
                letterSpacing: '0.2em'
              }}
            >
            DIAMONDS THAT DESERVE YOU
          </h1>

              {/* Premium Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl"></div>
            </div>
               
          </motion.div>
       

          {/* Elegant Tagline */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 0.5, ease: "easeOut" }}
            className="text-center max-w-4xl mx-auto mb-12"
          >
            <h1 className="text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-white/90 tracking-[0.3em] leading-relaxed">
              <PermanentCounter
                targetNumber={10000}
                suffix="+ CERTIFIED STONES"
                duration={2000}
              />
            </h1>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent mx-auto mt-6 mb-6"></div>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-white/70 font-light tracking-wide leading-relaxed">
              Where precision meets perfection in every facet
            </p>
          </motion.div>

          {/* Call to Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 1, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-4 sm:gap-6"
          >
            <Link
              href="/shapes"
              className="group relative px-8 py-3 bg-transparent border border-white/30 text-white hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden"
            >
              <span className="relative z-10">EXPLORE SHAPES</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
            </Link>

            <Link
              href="https://inventory.diamondatelier.in/"
              className="group relative px-8 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light"
            >
              <span className="relative z-10">VIEW INVENTORY</span>
            </Link>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5, delay: 1.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer"
          onClick={() => setCurrentIndex(1)}
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300"
          >
            <span className="text-xs tracking-widest mb-2 font-light">SCROLL</span>
            <ChevronDown className="w-5 h-5" />
          </motion.div>
        </motion.div>
      </div>

      {sections.map((item, index) => (
        <section
          key={index}
          className="full-section grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 items-center justify-center min-h-[60vh] sm:min-h-[70vh] md:min-h-screen px-3 xs:px-4 sm:px-6 md:px-8 lg:px-12 xl:px-20 2xl:px-32 4k:px-48 5k:px-64 gap-4 xs:gap-6 sm:gap-8 md:gap-12 lg:gap-16 xl:gap-20 4k:gap-32 5k:gap-48 py-4 sm:py-6 md:py-8 lg:py-12 xl:py-16"
        >
          <motion.div
            variants={item.imagePosition === "left" ? fadeRight : fadeLeft}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className={`w-full flex justify-center items-center ${
              item.imagePosition === "left" ? "md:order-2" : "md:order-1"
            }`}
          >
            {item.type === "image" && (
              <div className="relative w-full max-w-[400px] xs:max-w-[450px] sm:max-w-[550px] md:max-w-[500px] lg:max-w-[600px] xl:max-w-[700px] 2xl:max-w-[900px] 3xl:max-w-[1100px] 4k:max-w-[1800px] 5k:max-w-[1800px] aspect-[4/3] rounded-lg overflow-hidden shadow-xl">
                <MemoryOptimizedImage
                  src={item.media}
                  alt={item.title}
                  width={800}
                  height={600}
                  priority={index < 2}
                  quality={index < 2 ? 90 : 75}
                  className="w-full h-full object-contain rounded-lg"
                />
              </div>
            )}
            {item.type === "video" && (
              <LazyVideo
                src={item.media}
                playbackRate={item.playbackRate}
                loop={item.title !== "30+ colors"}
              />
            )}
            {item.type === "video-slider" && (
              <div className="relative w-full max-w-[400px] xs:max-w-[450px] sm:max-w-[550px] md:max-w-[500px] lg:max-w-[600px] xl:max-w-[700px] 2xl:max-w-[900px] 3xl:max-w-[1100px] 4k:max-w-[1800px] 5k:max-w-[1800px]">
                <LazyVideo src={matchingLayoutVideos[videoSlideIndex]} />
                {/* <button
                  onClick={() => advanceSlide("prev")}
                  className="absolute top-1/2 left-1 xs:left-2 -translate-y-1/2 bg-white bg-opacity-80 text-black p-1 xs:p-1.5 sm:p-2 md:p-2.5 lg:p-3 4k:p-4 5k:p-5 rounded-r hover:bg-opacity-90 transition-all duration-200 shadow-lg"
                >
                  <ChevronLeft className="w-3 h-3 xs:w-4 xs:h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 lg:w-7 lg:h-7 4k:w-8 4k:h-8 5k:w-10 5k:h-10" />
                </button>
                <button
                  onClick={() => advanceSlide("next")}
                  className="absolute top-1/2 right-1 xs:right-2 -translate-y-1/2 bg-white bg-opacity-80 text-black p-1 xs:p-1.5 sm:p-2 md:p-2.5 lg:p-3 4k:p-4 5k:p-5 rounded-l hover:bg-opacity-90 transition-all duration-200 shadow-lg"
                >
                  <ChevronRight className="w-3 h-3 xs:w-4 xs:h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 lg:w-7 lg:h-7 4k:w-8 4k:h-8 5k:w-10 5k:h-10" />
                </button> */}
              </div>
            )}
          </motion.div>

          <motion.div
            variants={textContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className={`w-full space-y-2 xs:space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-7 xl:space-y-8 4k:space-y-12 5k:space-y-16 ${
              item.imagePosition === "left" ? "md:order-1" : "md:order-2"
            }`}
          >
            {/* Decorative line above title */}
            {/* <motion.div
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 1, delay: 0.3 }}
              className="w-20 h-0.5 bg-white mx-0 mb-4 origin-left"
            /> */}

            <motion.h2
              variants={fadeUpText}
              className="text-2xl xs:text-3xl sm:text-4xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl 4k:text-7xl 5k:text-8xl font-thin leading-tight tracking-[0.2em] text-white"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              {item.title === "100+ shapes" ? (
                <>
                  <PermanentCounter
                    targetNumber={100}
                    suffix="+ SHAPES"
                    duration={1200}
                  />
                </>
              ) : item.title === "10,000 + certified stones" ? (
                <span className="inline-block">
                  <div>
                    <div style={{ fontFamily: 'Times New Roman, serif' }}>
                      <PermanentCounter
                        targetNumber={10000}
                        suffix="+ CERTIFIED"
                        duration={1800}
                      />
                      <br />
                      <span>STONES</span>
                    </div>
                  </div>
                </span>
              ) : item.title === "30+ colors" ? (
                <>
                  <PermanentCounter
                    targetNumber={30}
                    suffix="+ COLORS"
                    duration={800}
                  />
                </>
              ) : (
                item.title
              )}
            </motion.h2>
            <motion.h3
              variants={fadeUpText}
              className="text-lg xs:text-xl sm:text-2xl md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl 4k:text-5xl 5k:text-6xl text-gray-200 leading-relaxed font-light tracking-wide"
              style={{ fontFamily: 'Times New Roman, serif' }}
            >
              {item.subtitle}
            </motion.h3>
            <motion.p
              variants={fadeUpText}
              className="text-sm xs:text-base sm:text-lg md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 4k:text-3xl 5k:text-4xl text-gray-300 leading-relaxed font-light tracking-wide mb-3 xs:mb-4 sm:mb-5 md:mb-4 lg:mb-5 xl:mb-6"
            >
              {item.description}
            </motion.p>

            {/* LEARN MORE Button */}
            <motion.div
              variants={fadeUpText}
              className="mt-2 xs:mt-3 sm:mt-4"
            >
              {item.title === "30+ colors" ? (
                <Link href="/other-fancy-color-learn-more" className="group relative px-4 py-2 xs:px-5 xs:py-2.5 sm:px-6 sm:py-3 md:px-5 md:py-2 lg:px-6 lg:py-2.5 xl:px-7 xl:py-3 bg-white border border-gray-300 text-black font-light uppercase tracking-[0.15em] text-xs xs:text-xs sm:text-sm md:text-xs lg:text-sm xl:text-sm transition-all duration-300 hover:bg-gray-100 hover:border-gray-400 shadow-sm hover:shadow-md overflow-hidden inline-block">
                  <span className="relative z-10" style={{ fontFamily: 'Times New Roman, serif' }}>
                    LEARN MORE
                  </span>
                </Link>
              ) : item.title === "Star melee" ? (
                <Link href="/star-melle" className="group relative px-4 py-2 xs:px-5 xs:py-2.5 sm:px-6 sm:py-3 md:px-5 md:py-2 lg:px-6 lg:py-2.5 xl:px-7 xl:py-3 bg-white border border-gray-300 text-black font-light uppercase tracking-[0.15em] text-xs xs:text-xs sm:text-sm md:text-xs lg:text-sm xl:text-sm transition-all duration-300 hover:bg-gray-100 hover:border-gray-400 shadow-sm hover:shadow-md overflow-hidden inline-block">
                  <span className="relative z-10" style={{ fontFamily: 'Times New Roman, serif' }}>
                    LEARN MORE
                  </span>
                </Link>
              ) : item.title === "Matching layout" ? (
                <Link href="/matching-other-layout" className="group relative px-4 py-2 xs:px-5 xs:py-2.5 sm:px-6 sm:py-3 md:px-5 md:py-2 lg:px-6 lg:py-2.5 xl:px-7 xl:py-3 bg-white border border-gray-300 text-black font-light uppercase tracking-[0.15em] text-xs xs:text-xs sm:text-sm md:text-xs lg:text-sm xl:text-sm transition-all duration-300 hover:bg-gray-100 hover:border-gray-400 shadow-sm hover:shadow-md overflow-hidden inline-block">
                  <span className="relative z-10" style={{ fontFamily: 'Times New Roman, serif' }}>
                    LEARN MORE
                  </span>
                </Link>
              ) : item.title === "100+ shapes" ? (
                <Link href="/shapes" className="group relative px-4 py-2 xs:px-5 xs:py-2.5 sm:px-6 sm:py-3 md:px-5 md:py-2 lg:px-6 lg:py-2.5 xl:px-7 xl:py-3 bg-white border border-gray-300 text-black font-light uppercase tracking-[0.15em] text-xs xs:text-xs sm:text-sm md:text-xs lg:text-sm xl:text-sm transition-all duration-300 hover:bg-gray-100 hover:border-gray-400 shadow-sm hover:shadow-md overflow-hidden inline-block">
                  <span className="relative z-10" style={{ fontFamily: 'Times New Roman, serif' }}>
                    LEARN MORE
                  </span>
                </Link>
              ) : (
                <button className="group relative px-4 py-2 xs:px-5 xs:py-2.5 sm:px-6 sm:py-3 md:px-5 md:py-2 lg:px-6 lg:py-2.5 xl:px-7 xl:py-3 bg-white border border-gray-300 text-black font-light uppercase tracking-[0.15em] text-xs xs:text-xs sm:text-sm md:text-xs lg:text-sm xl:text-sm transition-all duration-300 hover:bg-gray-100 hover:border-gray-400 shadow-sm hover:shadow-md overflow-hidden">
                  <span className="relative z-10" style={{ fontFamily: 'Times New Roman, serif' }}>
                    LEARN MORE
                  </span>
                </button>
              )}
            </motion.div>
          </motion.div>
        </section>
      ))}


      </div>
    </>
  );
}
