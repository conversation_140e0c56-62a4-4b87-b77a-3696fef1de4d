"use client";
import React, { useState, useEffect, useRef } from 'react';

// Global state to track which counters have animated
const animatedCounters = new Set();

const PermanentCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const ref = useRef(null);
  const counterId = `${targetNumber}_${suffix}`;

  useEffect(() => {
    // Check if this counter has already animated
    if (animatedCounters.has(counterId)) {
      setCount(targetNumber);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !animatedCounters.has(counterId)) {
          // Mark as animated
          animatedCounters.add(counterId);
          
          // Start animation
          const steps = 60;
          const stepValue = targetNumber / steps;
          const stepDelay = duration / steps;
          
          let currentStep = 0;
          
          const timer = setInterval(() => {
            currentStep++;
            const newCount = Math.floor(currentStep * stepValue);
            
            if (currentStep >= steps) {
              setCount(targetNumber);
              clearInterval(timer);
            } else {
              setCount(newCount);
            }
          }, stepDelay);
          
          // Disconnect observer after animation starts
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [targetNumber, duration, counterId]);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit'
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

// Function to reset all counters (for page refresh)
export const resetAllCounters = () => {
  animatedCounters.clear();
};

export default PermanentCounter;
