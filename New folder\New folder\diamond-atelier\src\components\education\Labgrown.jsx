

"use client";

import React, { useEffect, useState } from "react";
import Difference from "../../components/education/DifferenceBetweenDiamonds";
import Methods from "../education/Method";
import WhyLab from "../education/WhyLabGrown";
import Myths from "../education/Myths";
import Evolution from "../education/Evolution";
import Anatomy from "../education/Anatomy";
import The4cs from "./4Cs";
import LoadingScreen from "../../utils/LoadingScreen";
import { useEducationSpacing } from "@/hooks/useHeaderHeight";

const sections = [
  { id: "difference", title: "Difference", icon: "💎", component: () => <Difference key="difference-component" /> },
  { id: "methods", title: "Methods", icon: "⚙️", component: () => <Methods /> },
  { id: "whyLab", title: "Why Lab", icon: "❓", component: () => <WhyLab /> },
  { id: "myths", title: "Myths", icon: "📜", component: () => <Myths /> },
  { id: "evolution", title: "Evolution", icon: "⚡", component: () => <Evolution /> },
  { id: "the4cs", title: "The 4Cs", icon: "4️⃣", component: () => <The4cs /> },
  { id: "anatomy", title: "Anatomy", icon: "🔬", component: () => <Anatomy /> },
];

const Labgrown = () => {
  const [active, setActive] = useState(sections[0].id);
  const [loading, setLoading] = useState(true);

  // Dynamic spacing for education page
  const educationSpacing = useEducationSpacing(20); // 20px extra buffer

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY < 100) {
        setActive(sections[0].id);
        return;
      }
      let maxVisibleHeight = 0;
      let mostVisibleId = active;

      sections.forEach(({ id }) => {
        const el = document.getElementById(id);
        if (el) {
          const rect = el.getBoundingClientRect();
          const visibleTop = Math.max(rect.top, 0);
          const visibleBottom = Math.min(rect.bottom, window.innerHeight);
          const visibleHeight = Math.max(visibleBottom - visibleTop, 0);
          if (visibleHeight > maxVisibleHeight) {
            maxVisibleHeight = visibleHeight;
            mostVisibleId = id;
          }
        }
      });
      setActive(mostVisibleId);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [active]);

  const scrollToSection = (id) => {
    const el = document.getElementById(id);
    if (el) el.scrollIntoView({ behavior: "smooth" });
  };
  useEffect(() => {
    // Simulate page load or replace with actual resource checks
    const timer = setTimeout(() => setLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <LoadingScreen />;
  return (
    <div className="overflow-x-hidden bg-black text-white pb-15 md:pb-0">
      {/* Education Navigation - Fixed Below Main Header */}
      <nav className="hidden sm:flex fixed z-30 w-full bg-black/95 backdrop-blur-md shadow-lg border-b border-white/20 items-center px-2 sm:px-3 md:px-4 lg:px-6 xl:px-8 education-nav"
           style={{
             top: 'var(--education-nav-top)', // Dynamic top position
             height: '60px' // Fixed height for consistency
           }}>
        <div className="flex justify-center flex-1 gap-1 sm:gap-2 md:gap-3 lg:gap-4 text-xs sm:text-sm md:text-base lg:text-lg font-semibold w-full max-w-7xl mx-auto overflow-x-auto scrollbar-hide">
          <div className="flex gap-1 sm:gap-2 md:gap-3 lg:gap-4 min-w-max px-2">
            {sections.map(({ id, title }) => (
              <button
                key={id}
                onClick={() => scrollToSection(id)}
                className={`relative px-2 py-1.5 sm:px-3 sm:py-2 md:px-4 md:py-2 lg:px-6 lg:py-3 rounded-full transition-all duration-300 whitespace-nowrap text-center flex-shrink-0
                  ${
                    active === id
                      ? "bg-white text-black shadow-lg scale-105"
                      : "text-gray-400 hover:text-white hover:bg-white/20 hover:scale-102"
                  }`}
              >
                <span className="block">{title}</span>
                {active === id && (
                  <span
                    className="absolute bottom-[-8px] left-1/2 -translate-x-1/2 w-6 sm:w-8 h-[2px] sm:h-[3px] rounded-full bg-white shadow-sm"
                    aria-hidden="true"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Modern Glassmorphism Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 md:hidden z-50">
        {/* Glassmorphism container */}
        <div className="bg-black/40 backdrop-blur-2xl border-t border-white/10 px-3 py-4 shadow-2xl">

          {/* Navigation items container with horizontal scroll */}
          <div className="flex items-center justify-start gap-2 w-full overflow-x-auto scrollbar-hide px-4">
            <div className="flex gap-2 min-w-max">
              {/* Navigation buttons */}
              {sections.map(({ id, icon, title }, index) => (
                <button
                  key={id}
                  onClick={() => scrollToSection(id)}
                  className={`relative flex flex-col items-center justify-center px-3 py-2 rounded-2xl transition-all duration-500 group min-w-[60px] flex-shrink-0 ${
                    active === id
                      ? "bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm shadow-lg shadow-white/10 scale-105"
                      : "hover:bg-white/5"
                  }`}
                >
                {/* Icon container with glow effect */}
                <div className={`relative transition-all duration-300 ease-out ${
                  active === id ? "scale-110" : "scale-100 group-hover:scale-105"
                }`}>
                  {/* Glow effect for active item */}
                  {active === id && (
                    <div className="absolute inset-0 bg-white/20 rounded-full blur-md scale-150"></div>
                  )}

                  <span className={`relative text-lg transition-all duration-300 ${
                    active === id
                      ? "text-white drop-shadow-lg"
                      : "text-gray-400 group-hover:text-white"
                  }`}>
                    {icon}
                  </span>
                </div>

                {/* Title with better typography */}
                <span className={`text-[9px] font-medium tracking-wide transition-all duration-300 mt-1 ${
                  active === id
                    ? "text-white opacity-100 scale-100"
                    : "text-gray-500 opacity-70 scale-95 group-hover:opacity-100 group-hover:text-gray-300"
                }`}>
                  {title.length > 8 ? title.slice(0, 8) + '...' : title}
                </span>

                {/* Active indicator dot */}
                {active === id && (
                  <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full shadow-lg shadow-white/50"></div>
                )}

                {/* Ripple effect on touch */}
                <div className="absolute inset-0 rounded-2xl overflow-hidden">
                  <div className="absolute inset-0 bg-white/10 scale-0 group-active:scale-100 transition-transform duration-200 rounded-2xl"></div>
                </div>
              </button>
              ))}
            </div>
          </div>

          {/* Progress indicator */}
          <div className="flex justify-center items-center mt-3">
            <div className="flex space-x-1.5">
              {sections.map(({ id }, index) => (
                <div
                  key={id}
                  className={`transition-all duration-500 ease-out ${
                    active === id
                      ? "w-6 h-1 bg-gradient-to-r from-white to-gray-300 rounded-full shadow-sm"
                      : "w-1.5 h-1.5 bg-white/20 rounded-full hover:bg-white/40"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>


      {/* Clean Education Sections */}
      <div className="pb-32 w-full px-6 lg:px-8 xl:px-12" style={educationSpacing}>
        {sections.map(({ id, component, title, icon }, index) => (
          <section
            key={id}
            id={id}
            className="min-h-[100vh] scroll-mt-[220px] mb-16 lg:mb-20"
          >
            {/* Simple Professional Header */}
            <div className="mb-8 lg:mb-12">
              <h2 className="text-2xl lg:text-3xl xl:text-4xl font-bold text-white mb-2">
                {title}
              </h2>
              <div className="h-0.5 w-16 bg-gradient-to-r from-white to-transparent"></div>
            </div>

            {/* Clean Content Area */}
            <div className="text-white text-base md:text-lg lg:text-xl xl:text-2xl">
              {component()}
            </div>
          </section>
        ))}
      </div>
    </div>
  );
};

export default Labgrown;
