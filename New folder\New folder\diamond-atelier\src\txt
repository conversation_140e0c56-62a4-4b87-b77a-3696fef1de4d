

🎯 1. Admin Dashboard (Secure)
Login protected:

Admin can:
➕ Create Job Openings
✏️ Update Job Openings
❌ Delete Job Openings

Jobs stored in MongoDB

🎯 2. Careers Page (User-facing)
Shows live job openings.
Each job has an Apply Form

Form submission:
📤 Sends resume & info to company email






🔹 Step 1: Careers Page – Live Job Listings
Visitors to the Careers page will see only the active job openings (flag = ON).
Each job appears as a card showing:

Job Title, Location, Qualification, Experience, Salary

A button: “Show Details”

🔹 Step 2: View Job Details & Apply
When the user clicks "Show Details":

Full job description appears

A form appears to apply for that job

Candidates can:

Enter their details

Upload their resume (.pdf, .doc, .docx)

Submit the application

📩 What Happens on Submit:
✅ Resume and details are emailed directly to your HR team

✅ Application is stored in your system (optional)

✅ The application count for that job is updated

🔐 Admin Dashboard – Full Control Panel
An Admin login is available to manage job postings:

Admin Can:
➕ Create new job openings

✏️ Edit existing jobs

❌ Delete old jobs

🔢 See how many candidates applied for each job

🔁 Toggle Job Status (using a flag: ON/OFF)

⚙️ New Feature: Job Status Toggle (Flag ON/OFF)
What it does:
✅ Flag ON (Active):

Job is visible on the careers page

Candi<PERSON> can apply

Application count is tracked

❌ Flag OFF (Inactive):

Job is hidden from the public site

Candi<PERSON> cannot apply

Application count is reset to 0 (optional)

Useful when hiring is done or paused

🔁 HR/Admin can turn the job ON again anytime

✅ Benefits for Your Business
🔐 Admin-only control over job status & visibility

📊 Real-time view of how many people applied per job

📨 Resumes sent directly to your HR inbox

⏱️ Post-hiring cleanup is easy — just switch OFF the job flag

🧼 No need to delete jobs manually — just hide them with a toggle

