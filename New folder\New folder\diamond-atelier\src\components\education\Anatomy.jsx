"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import OptimizedImage, { generateBlurDataURL } from "@/components/common/OptimizedImage";

const Anatomy = () => {
  const [activeImage, setActiveImage] = useState("starLength");

  const handleButtonClick = (target) => {
    setActiveImage(target);
  };

  const imageMap = {
    starLength: "/images/anatomy/01StarLenght.png",
    tableSize: "/images/anatomy/02TableSize.png",
    crownAngle: "/images/anatomy/03CrownAngle.png",
    crownHeight: "/images/anatomy/04CrownHeight.png",
    pavilionAngle: "/images/anatomy/05PavilionAngle.png",
    pavilionDepth: "/images/anatomy/06PavilionDepth.png",
    culet: "/images/anatomy/07Culet.png",
    lowerGirdle: "/images/anatomy/08LowerGridle.png",
    girdleThickness: "/images/anatomy/09GirdleThickness.png",
    totalDepth: "/images/anatomy/10TotalLength.png",
    all: "/images/anatomy/11All.png",
  };

  const buttonLabels = {
    starLength: "Star Length",
    tableSize: "Table Size",
    crownAngle: "Crown Angle",
    crownHeight: "Crown Height",
    pavilionAngle: "Pavilion Angle",
    pavilionDepth: "Pavilion Depth",
    culet: "Culet",
    lowerGirdle: "Lower Girdle / Half Facet",
    girdleThickness: "Girdle Thickness",
    totalDepth: "Total Depth",
    all: "All",
  };

  const sections = [
    { id: "difference", title: "Difference" },
    { id: "methods", title: "Methods" },
    { id: "whyLab", title: "Why Lab Grown" },
    { id: "myths", title: "Myths & Facts" },
    { id: "evolution", title: "Evolution" },
    { id: "the4cs", title: "The 4Cs" },
    { id: "anatomy", title: "Anatomy" },
  ];

  const ButtonComponent = ({ target, isActive }) => (
    <button
      aria-pressed={isActive}
      className={`relative px-4 md:px-6 py-2.5 md:py-3 text-sm md:text-base font-light tracking-wide transition-all duration-500 rounded-lg overflow-hidden group
        ${
          isActive
            ? "bg-white text-black shadow-lg border border-white"
            : "bg-black/40 text-white/80 border border-white/30 hover:border-white/50 hover:text-white hover:bg-white/10"
        }`}
      onClick={() => handleButtonClick(target)}
      style={{ fontFamily: 'Times New Roman, serif' }}
    >
      <span className="relative z-10">{buttonLabels[target]}</span>
      {!isActive && (
        <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover:from-white/10 group-hover:to-white/5 transition-all duration-500"></div>
      )}
    </button>
  );

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-black"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-16"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-light text-white mb-6 tracking-[0.15em] uppercase">
              DIAMOND ANATOMY
            </h1>
            <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-lg md:text-xl text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide">
              Understanding the intricate structure and components that define a diamond's brilliance
            </p>
          </motion.div>

          {/* Video Section */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-2xl mx-auto"
          >
            <div className="relative bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <video
                className="w-full h-auto rounded-xl shadow-2xl"
                muted
                autoPlay
                playsInline
                loop
                preload="auto"
                aria-label="360 degree view of round diamond"
              >
                <source src="/images/anatomy/round-diamond.mp4" type="video/mp4" />
              </video>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Interactive Analysis Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              INTERACTIVE ANATOMY EXPLORER
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base md:text-lg text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Discover the intricate architecture of nature's most precious creation through our interactive diamond anatomy guide
            </p>
          </motion.div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <p className="text-base md:text-lg text-white/90 font-light leading-relaxed max-w-4xl mx-auto mb-8 tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              While every diamond is unique, all diamonds share certain structural features. A diamond's anatomy, or its basic structure, determines its proportions, brilliance, dispersion and scintillation.
            </p>

            <p className="text-base md:text-lg text-white/90 font-light leading-relaxed max-w-4xl mx-auto mb-12 tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Each part of the diamond has a specific name, and having a basic understanding of how each part contributes to the diamond as a whole will help you find your perfect diamond.
            </p>

            <div className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-4xl mx-auto">
              <p className="text-sm text-white/80 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                A diamond is comprised of eight main components: Table, Crown, Girdle, Pavilion, and Culet. Below is a detailed analysis of each part and its precise location.
              </p>
            </div>
          </motion.div>

          {/* Interactive Viewer */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-6xl mx-auto"
          >
            <div className="flex flex-col xl:flex-row items-center gap-12">
              {/* Image */}
              <div className="xl:w-1/2 relative">
                <div className="relative max-w-lg mx-auto bg-black/40 backdrop-blur-sm rounded-lg p-4 border border-white/30">
                  <OptimizedImage
                    src={imageMap[activeImage]}
                    alt={`Diamond Anatomy - ${buttonLabels[activeImage]}`}
                    width={500}
                    height={400}
                    className="w-full h-auto object-contain rounded-xl shadow-2xl"
                    priority={false}
                    placeholder="blur"
                    blurDataURL={generateBlurDataURL()}
                    draggable={false}
                  />
                </div>
              </div>

              {/* Buttons */}
              <div className="xl:w-1/2 flex flex-wrap justify-center xl:justify-start gap-3 md:gap-4">
                {Object.keys(imageMap).map((key) => (
                  <ButtonComponent
                    key={key}
                    target={key}
                    isActive={activeImage === key}
                  />
                ))}
              </div>
            </div>
          </motion.div>

        </div>
      </section>

      {/* Component Details Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              DIAMOND COMPONENTS
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
          </motion.div>

          {/* Table & Crown */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {[{
              title: "Table",
              points: ["Largest facet of a diamond", "Always a flat surface", "Resembles a table top"],
              image: "/images/anatomy/table.png"
            }, {
              title: "Crown",
              points: ["Extends from the table", "Ends at the top of the girdle", "Step or brilliant cut facets"],
              image: "/images/anatomy/crown.png"
            }].map(({ title, points, image }) => (
              <motion.div
                key={title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 hover:border-white/40 transition-all duration-500"
              >
                <div className="text-center mb-8">
                  <h3 className="text-xl md:text-2xl font-light text-white mb-4 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                    {title}
                  </h3>
                  <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"></div>
                </div>
                <div className="flex flex-col lg:flex-row items-center gap-8">
                  <div className="lg:w-2/3 space-y-4 flex-1">
                    {points.map((pt) => (
                      <div key={pt} className="flex items-start gap-4">
                        <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                        <p className="text-white/80 font-light text-base leading-relaxed" style={{ fontFamily: 'Times New Roman, serif' }}>{pt}</p>
                      </div>
                    ))}
                  </div>
                  <div className="lg:w-1/3 relative">
                    <div className="relative bg-black/40 backdrop-blur-sm rounded-lg p-4 border border-white/30">
                      <img
                        src={image}
                        alt={`${title} facet of diamond`}
                        className="w-full rounded-lg shadow-xl"
                        loading="lazy"
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Pavilion & Culet */}
          <div className="grid md:grid-cols-2 gap-8">
            {[{
              title: "Pavilion",
              points: ["Located at the bottom", "Bridges the girdle & culet", "Determines light reflection", "Quality cuts allow for max light"],
              image: "/images/anatomy/pavilion.png"
            }, {
              title: "Culet",
              points: ["Smallest facet of a diamond", "Located at the very bottom tip", "Used to protect the pavilion", "Modern methods render it mostly unnecessary"],
              image: "/images/anatomy/culet.png"
            }].map(({ title, points, image }) => (
              <motion.div
                key={title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 hover:border-white/40 transition-all duration-500"
              >
                <div className="text-center mb-8">
                  <h3 className="text-xl md:text-2xl font-light text-white mb-4 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                    {title}
                  </h3>
                  <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"></div>
                </div>
                <div className="flex flex-col lg:flex-row items-center gap-8">
                  <div className="lg:w-2/3 space-y-4 flex-1">
                    {points.map((pt) => (
                      <div key={pt} className="flex items-start gap-4">
                        <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                        <p className="text-white/80 font-light text-base leading-relaxed" style={{ fontFamily: 'Times New Roman, serif' }}>{pt}</p>
                      </div>
                    ))}
                  </div>
                  <div className="lg:w-1/3 relative">
                    <div className="relative bg-black/40 backdrop-blur-sm rounded-lg p-4 border border-white/30">
                      <OptimizedImage
                        src={image}
                        alt={`${title} facet of diamond`}
                        width={300}
                        height={200}
                        className="w-full rounded-lg shadow-xl"
                        priority={false}
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Anatomy;
