"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { <PERSON>aP<PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>ond, FaReg<PERSON>lock } from "react-icons/fa";
import { FaLocationDot } from "react-icons/fa6";
import { SlGlobe } from "react-icons/sl";
import { IoMdMail } from "react-icons/io";
import { motion, AnimatePresence } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";

export default function ClientContactPage({ background, countryCodes }) {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [countryCode, setCountryCode] = useState("+1");
  const [countrySearch, setCountrySearch] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [errors, setErrors] = useState({});

  const filteredCountries = countryCodes.filter(country => 
    country.country.toLowerCase().includes(countrySearch.toLowerCase()) ||
    country.code.includes(countrySearch)
  );

  const handleCountrySelect = (code) => {
    setCountryCode(code);
    setIsDropdownOpen(false);
    setCountrySearch("");
  };

  const validateForm = () => {
    const newErrors = {};

    if (!name.trim()) {
      newErrors.name = "Name is required";
    } else if (!/^[a-zA-Z\s]+$/.test(name.trim())) {
      newErrors.name = "Name can only contain letters and spaces";
    }

    if (!email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10,15}$/.test(phone.replace(/\s/g, ""))) {
      newErrors.phone = "Please enter a valid phone number";
    }

    if (!message.trim()) {
      newErrors.message = "Message is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNameChange = (e) => {
    const value = e.target.value;
    // Only allow letters and spaces
    if (/^[a-zA-Z\s]*$/.test(value)) {
      setName(value);
    }
  };

  const handlePhoneChange = (e) => {
    const value = e.target.value;
    // Only allow numbers
    if (/^\d*$/.test(value)) {
      setPhone(value);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          phone: `${countryCode} ${phone}`,
          message,
        }),
      });

      if (response.ok) {
        setShowSuccessPopup(true);
        setName("");
        setEmail("");
        setPhone("");
        setMessage("");
        setCountryCode("+1");
        setErrors({});
        
        setTimeout(() => {
          setShowSuccessPopup(false);
        }, 5000);
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    }
  };

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section */}
      <section className="relative pt-20 sm:pt-24 md:pt-32 pb-12 sm:pb-16 md:pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background with subtle overlay */}
        <div className="absolute inset-0 z-0">
          <Image
            src={background}
            alt="Contact Background"
            fill
            style={{ objectFit: "cover" }}
            priority
            className="opacity-30"
          />
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-6">
              <IoMdMail className="w-5 h-5 text-white" />
              <span className="text-white font-medium tracking-wider uppercase text-sm">
                Get In Touch
              </span>
            </div>

            <h1 className="text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light text-white mb-6 sm:mb-8 tracking-[0.1em] sm:tracking-[0.2em] uppercase leading-tight">
              CONTACT
            </h1>
            <div className="h-px w-24 sm:w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6 sm:mb-8"></div>

            <p className="text-base sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6 font-light max-w-4xl mx-auto tracking-wide px-2">
              Connect with Diamond Excellence
            </p>

            <p className="text-sm sm:text-base text-white/70 max-w-3xl mx-auto leading-relaxed px-2">
              Ready to discover the perfect diamond? Contact us today and let our
              experts guide you to your ideal choice.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Form & Info Section */}
      <section className="relative py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.05)_0%,transparent_70%)]"></div>
        </div>

        <div className="relative max-w-7xl mx-auto z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
                  Contact <span className="italic font-light">Information</span>
                </h2>
                <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-6"></div>
                <p className="text-white/80 text-base leading-relaxed tracking-wide">
                  We're here to help you find the perfect diamond. Reach out to
                  us through any of the following channels.
                </p>
              </div>

              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="flex items-center space-x-4 p-4 rounded-lg bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="bg-gradient-to-r from-white/20 to-white/10 p-3 rounded-full">
                    <FaPhone className="text-lg text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-light text-white tracking-wide uppercase">Phone</h3>
                    <p className="text-white/80 text-sm">+91 99099 60024</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="flex items-center space-x-4 p-4 rounded-lg bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="bg-gradient-to-r from-white/20 to-white/10 p-3 rounded-full">
                    <IoMdMail className="text-lg text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-light text-white tracking-wide uppercase">Email</h3>
                    <p className="text-white/80 text-sm">
                      <EMAIL>
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="flex items-center space-x-4 p-4 rounded-lg bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="bg-gradient-to-r from-white/20 to-white/10 p-3 rounded-full">
                    <FaLocationDot className="text-lg text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-light text-white tracking-wide uppercase">Address</h3>
                    <p className="text-white/80 text-sm">
                      38 West 48th Street, 5th Floor, New York, NY 10036, USA
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="flex items-center space-x-4 p-4 rounded-lg bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="bg-gradient-to-r from-white/20 to-white/10 p-3 rounded-full">
                    <SlGlobe className="text-lg text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-light text-white tracking-wide uppercase">Website</h3>
                    <p className="text-white/80 text-sm">www.diamondatelier.com</p>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/20"
            >
              <div className="mb-8">
                <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
                  Send us a <span className="italic font-light">Message</span>
                </h2>
                <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
                <p className="text-white/70 text-sm">
                  Fill out the form below and we'll get back to you within 24 hours.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  <label
                    htmlFor="name"
                    className="block text-sm font-light text-white/90 mb-2 tracking-wide uppercase"
                  >
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={handleNameChange}
                    className={`w-full px-4 py-3 bg-gradient-to-r from-white/10 to-white/5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300 text-white placeholder-white/50 ${
                      errors.name ? "border-red-500" : "border-white/20"
                    }`}
                    placeholder="Enter your full name"
                  />
                  {errors.name && (
                    <p className="text-red-400 text-xs mt-1">{errors.name}</p>
                  )}
                </motion.div>


                <div>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium mb-2"
                  >
                    Phone Number *
                  </label>
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-0">
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        className="w-full sm:w-auto px-4 py-3 bg-white/10 border border-white/20 sm:rounded-l-lg rounded-lg sm:rounded-r-none focus:outline-none focus:ring-2 focus:ring-white/50 min-w-[80px] text-center"
                      >
                        {countryCode}
                      </button>

                      {isDropdownOpen && (
                        <div className="absolute top-full left-0 mt-1 w-full sm:w-64 bg-gray-900 border border-white/20 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                          <div className="p-2">
                            <input
                              type="text"
                              placeholder="Search country..."
                              value={countrySearch}
                              onChange={(e) => setCountrySearch(e.target.value)}
                              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded text-sm focus:outline-none"
                            />
                          </div>
                          {filteredCountries.map((country, index) => (
                            <button
                              key={`${country.code}-${country.country}-${index}`}
                              type="button"
                              onClick={() => handleCountrySelect(country.code)}
                              className="w-full text-left px-4 py-2 hover:bg-white/10 text-sm"
                            >
                              {country.country} ({country.code})
                            </button>
                          ))}
                        </div>
                      )}
                    </div>

                    <input
                      type="tel"
                      id="phone"
                      value={phone}
                      onChange={handlePhoneChange}
                      className={`flex-1 px-4 py-3 bg-white/10 border sm:rounded-r-lg rounded-lg sm:rounded-l-none focus:outline-none focus:ring-2 focus:ring-white/50 transition-colors ${
                        errors.phone ? "border-red-500" : "border-white/20"
                      }`}
                      placeholder="Enter your phone number"
                    />
                  </div>
                  {errors.phone && (
                    <p className="text-red-400 text-sm mt-1">{errors.phone}</p>
                  )}
                </div>


                                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium mb-2"
                  >
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={`w-full px-4 py-3 bg-white/10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 transition-colors ${
                      errors.email ? "border-red-500" : "border-white/20"
                    }`}
                    placeholder="Enter your email address"
                  />
                  {errors.email && (
                    <p className="text-red-400 text-sm mt-1">{errors.email}</p>
                  )}
                </div>


                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium mb-2"
                  >
                    Message *
                  </label>
                  <textarea
                    id="message"
                    rows={5}
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    className={`w-full px-4 py-3 bg-white/10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 transition-colors resize-none ${
                      errors.message ? "border-red-500" : "border-white/20"
                    }`}
                    placeholder="Tell us about your diamond requirements..."
                  />
                  {errors.message && (
                    <p className="text-red-400 text-sm mt-1">
                      {errors.message}
                    </p>
                  )}
                </div>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  type="submit"
                  className="w-full bg-gradient-to-r from-white to-gray-100 text-black py-3 px-6 rounded-lg font-medium hover:from-gray-100 hover:to-white transition-all duration-300 tracking-wide uppercase text-sm"
                >
                  Send Message
                </motion.button>
              </form>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced Success Popup */}
      <AnimatePresence>
        {showSuccessPopup && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 px-4"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-2xl p-8 max-w-md w-full text-center border border-gray-700 shadow-2xl"
            >
              {/* Diamond Icon with Animation */}
              <motion.div
                initial={{ rotate: 0, scale: 0 }}
                animate={{ rotate: 360, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.8, type: "spring" }}
                className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-white to-gray-300 rounded-full flex items-center justify-center"
              >
                <FaGem className="text-black text-3xl" />
              </motion.div>

              {/* Success Message */}
              <motion.h3
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-2xl font-bold text-white mb-4 font-space-grotesk"
              >
                Message Received!
              </motion.h3>

              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="text-gray-300 mb-6 leading-relaxed"
              >
                Thank you for contacting Diamond Atelier. Our diamond experts will review your inquiry and get back to you within 48 hours.
              </motion.p>

              {/* Additional Info */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex items-center justify-center space-x-2 text-sm text-gray-400 mb-6"
              >
                <FaRegClock className="text-white" />
                <span>Response time: Within 48 hours</span>
              </motion.div>

              {/* Decorative Elements */}
              <div className="flex justify-center space-x-2 mb-4">
                {[...Array(3)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.7 + i * 0.1 }}
                    className="w-2 h-2 bg-gradient-to-r from-white to-gray-300 rounded-full"
                  />
                ))}
              </div>

              {/* Close Button */}
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                onClick={() => setShowSuccessPopup(false)}
                className="bg-gradient-to-r from-white to-gray-200 text-black px-6 py-2 rounded-full font-semibold text-sm hover:shadow-lg transition-all duration-300"
              >
                Continue Browsing
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
