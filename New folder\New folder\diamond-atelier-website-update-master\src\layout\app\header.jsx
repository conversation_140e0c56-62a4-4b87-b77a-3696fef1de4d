"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Menu, X } from "lucide-react";

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const menuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const pathname = usePathname();

  // Navigation routes
  const navigationRoutes = [
    { id: 1, label: "INVENTORY", path: "https://inventory.diamondatelier.in/", external: true },
    { id: 2, label: "SHAPES", path: "/shapes" },
    { id: 3, label: "EDUCATION", path: "/education" },
    { id: 4, label: "ABOUT", path: "/about" },
    { id: 5, label: "CONTACT", path: "/contact" }
  ];

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        hamburgerRef.current &&
        !hamburgerRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);
  const isActive = (item) => pathname === item.path;

  return (
    <header className="w-full bg-gray-50 border-b border-gray-200">
      <div className="max-w-7xl mx-auto">

        {/* Mobile Menu Button */}
        <div className="lg:hidden flex justify-between items-center px-6 py-4">
          <button
            ref={hamburgerRef}
            onClick={toggleMenu}
            className="p-2 text-gray-700 hover:text-black transition-colors"
            aria-label="Toggle menu"
          >
            {menuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
          <div className="text-lg font-light tracking-[0.2em] text-black">
            DIAMOND ATELIER
          </div>
          <div className="w-10"></div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          {/* Logo Section */}
          <div className="text-center py-8">
            <Link href="/" className="inline-block">
              <h1 className="text-2xl font-light tracking-[0.3em] text-black hover:tracking-[0.35em] transition-all duration-300">
                DIAMOND ATELIER
              </h1>
            </Link>
          </div>

          {/* Navigation Section */}
          <div className="border-t border-gray-200">
            <nav className="flex justify-center items-center py-4">
              <div className="flex items-center space-x-12">
                {navigationRoutes.map((item) => {
                  const linkClasses = `text-sm font-light text-gray-700 hover:text-black transition-colors duration-300 tracking-[0.1em] uppercase ${
                    isActive(item) ? "text-black font-medium" : ""
                  }`;

                  return item.external ? (
                    <a
                      key={item.id}
                      href={item.path}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={linkClasses}
                    >
                      {item.label}
                    </a>
                  ) : (
                    <Link key={item.id} href={item.path} className={linkClasses}>
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {menuOpen && (
        <>
          {/* Background Overlay */}
          <div
            className="lg:hidden fixed inset-0 bg-black/50 z-40"
            onClick={closeMenu}
          />

          {/* Menu Content */}
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white z-50 border-b border-gray-200 shadow-lg">
            <nav ref={menuRef} className="flex flex-col py-4 px-6">
              {navigationRoutes.map((item) => {
                const linkClasses = `py-3 text-base font-light text-gray-700 hover:text-black transition-colors tracking-[0.1em] uppercase border-b border-gray-100 last:border-b-0 ${
                  isActive(item) ? "text-black font-medium" : ""
                }`;

                return item.external ? (
                  <a
                    key={item.id}
                    href={item.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClasses}
                    onClick={closeMenu}
                  >
                    {item.label}
                  </a>
                ) : (
                  <Link
                    key={item.id}
                    href={item.path}
                    className={linkClasses}
                    onClick={closeMenu}
                  >
                    {item.label}
                  </Link>
                );
              })}
            </nav>
          </div>
        </>
      )}
    </header>
  );
};

export default Header;
