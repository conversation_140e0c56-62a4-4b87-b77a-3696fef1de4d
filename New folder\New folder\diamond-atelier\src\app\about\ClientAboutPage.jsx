"use client";

import React, { useRef, useEffect, useState } from "react";
// import { motion, useScroll, useTransform } from "framer-motion"; // Disabled to reduce memory usage
import Image from "next/image";
import LoadingScreen from "../../utils/LoadingScreen";
import { useAvoidHeaderOverlap } from "@/hooks/useHeaderHeight";
import MemoryOptimizedImage from "@/components/common/MemoryOptimizedImage";
import { performMemoryCleanup, reportMemoryUsage } from "@/utils/memoryCleanup";
import INPOptimizer from "@/components/common/INPOptimizer";
import './critical.css'; // Critical CSS for LCP optimization
// import PerformanceMonitor from "@/components/common/PerformanceMonitor"; // Disabled - causing memory leaks

const ClientAboutPage = ({ evolution, policy }) => {
  // DISABLED: Heavy scroll animations causing memory leaks
  // const { scrollYProgress } = useScroll();
  // const y1 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  // const opacity = useTransform(scrollYProgress, [0, 0.8, 1], [1, 0.9, 0.8]);

  const sectionsRef = useRef([]);
  const [loading, setLoading] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);
  // const isScrollingRef = useRef(false); // Removed - no longer needed

  // ✅ Detect screen size
  const [isDesktop, setIsDesktop] = useState(false);

  // ✅ Dynamic header spacing
  const headerSpacing = useAvoidHeaderOverlap(20); // 20px extra buffer
  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 768);
    };
    handleResize(); // initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // DISABLED: Smooth scroll function was causing INP issues
  // const scrollToSection = (index) => {
  //   if (isScrollingRef.current || !sectionsRef.current[index]) return;
  //   isScrollingRef.current = true;
  //   sectionsRef.current[index].scrollIntoView({
  //     behavior: "smooth",
  //     block: "start",
  //   });
  //   setTimeout(() => {
  //     isScrollingRef.current = false;
  //   }, 1000);
  // };

  // DISABLED: Wheel scroll handler was causing INP issues and calling removed scrollToSection
  // useEffect(() => {
  //   if (!isDesktop) return;

  //   let isScrolling = false;
  //   const handleWheel = (e) => {
  //     if (isScrolling || isScrollingRef.current) return;
  //     e.preventDefault();
  //     isScrolling = true;

  //     const direction = e.deltaY > 0 ? 1 : -1;
  //     const newIndex = Math.max(0, Math.min(3, activeIndex + direction));

  //     if (newIndex !== activeIndex) {
  //       setActiveIndex(newIndex);
  //       scrollToSection(newIndex);
  //     }

  //     setTimeout(() => {
  //       isScrolling = false;
  //     }, 100);
  //   };

  //   window.addEventListener("wheel", handleWheel, { passive: false });
  //   return () => window.removeEventListener("wheel", handleWheel);
  // }, [isDesktop, activeIndex]);

  // DISABLED: Keyboard navigation was calling removed scrollToSection function
  // useEffect(() => {
  //   const handleKeyDown = (e) => {
  //     if (e.key === "ArrowDown" || e.key === "ArrowUp") {
  //       e.preventDefault();
  //       const direction = e.key === "ArrowDown" ? 1 : -1;
  //       const newIndex = Math.max(0, Math.min(3, activeIndex + direction));
  //       if (newIndex !== activeIndex) {
  //         setActiveIndex(newIndex);
  //         scrollToSection(newIndex);
  //       }
  //     }
  //   };

  //   window.addEventListener("keydown", handleKeyDown);
  //   return () => window.removeEventListener("keydown", handleKeyDown);
  // }, [activeIndex]);

  // ✅ Section detection (activeIndex)
  useEffect(() => {
    let ticking = false;
    let timeoutId = null;

    // Throttled scroll handler with better performance
    const scrollHandler = () => {
      if (!ticking) {
        ticking = true;
        timeoutId = requestAnimationFrame(() => {
          try {
            const scrollPos = window.scrollY + window.innerHeight / 2;
            const index = sectionsRef.current.findIndex((section) => {
              if (!section) return false;
              const top = section.offsetTop;
              const bottom = top + section.offsetHeight;
              return scrollPos >= top && scrollPos < bottom;
            });

            if (index !== -1 && index !== activeIndex) {
              setActiveIndex(index);
            }
          } catch (error) {
            console.warn('Scroll handler error:', error);
          } finally {
            ticking = false;
          }
        });
      }
    };

    // Use passive listeners for better INP
    const options = {
      passive: true,
      capture: false
    };

    window.addEventListener("scroll", scrollHandler, options);

    return () => {
      window.removeEventListener("scroll", scrollHandler, options);
      if (timeoutId) {
        cancelAnimationFrame(timeoutId);
      }
    };
  }, [activeIndex]);

  useEffect(() => {
    // Optimize font loading for LCP
    if (typeof document !== 'undefined') {
      document.fonts.ready.then(() => {
        console.log('🔤 Fonts loaded - LCP should improve');
      });
    }

    const timer = setTimeout(() => setLoading(false), 300); // Further reduced for faster LCP

    // Memory cleanup on component mount
    reportMemoryUsage('About Page Mount');

    return () => {
      clearTimeout(timer);
      // Cleanup on unmount
      performMemoryCleanup();
    };
  }, []);

  if (loading) return <LoadingScreen />;

  return (
    <>
      <INPOptimizer /> {/* Optimize interactions for better INP */}
      {/* <PerformanceMonitor pageName="About Page" /> */} {/* Temporarily disabled - causing memory leaks */}
    <div
      className="bg-black text-white font-sans min-h-screen overflow-x-hidden"
      style={{
        scrollBehavior: "auto" // Disabled smooth scroll for INP optimization
        // Removed snap scrolling - major cause of INP issues
      }}
    >
      <main className="relative z-10"> {/* Removed motion.main to reduce memory usage */}
        {/* Luxury Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          {/* Premium Background Video
          <video
            className="absolute inset-0 w-full h-full object-cover opacity-40"
            autoPlay
            muted
            loop
            playsInline
            preload="metadata"
          >
            <source src="/images/about/Background.mp4" type="video/mp4" />
          </video> */}
           <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
        </div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/80"></div>

          {/* Luxury Overlay Pattern */}
          {/* <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpolygon points='50,0 60,40 100,50 60,60 50,100 40,60 0,50 40,40'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div> */}

          <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Luxury Brand Mark */}
            {/* <div className="mb-12">
              <div className="inline-block p-8 border border-white/20 rounded-full backdrop-blur-sm bg-white/5">
                <div className="w-16 h-16 border-2 border-white/40 rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 bg-gradient-to-br from-white to-white/60 rounded-full"></div>
                </div>
              </div>
            </div> */}

            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-extralight text-white mb-6 tracking-[0.3em] uppercase"
                style={{ fontFamily: 'Times New Roman, serif', letterSpacing: '0.25em' }}>
              DIAMOND ATELIER
            </h1>

            {/* Luxury Divider */}
            <div className="flex items-center justify-center mb-8">
              <div className="w-8 h-px bg-white/30"></div>
              <div className="w-2 h-2 bg-white/60 rounded-full mx-4"></div>
              <div className="w-8 h-px bg-white/30"></div>
            </div>

            <p className="text-lg md:text-xl text-white/80 font-extralight max-w-3xl mx-auto leading-relaxed mb-8"
               style={{ fontFamily: 'Times New Roman, serif', letterSpacing: '0.05em' }}>
              Since our inception, we have been dedicated to the art of creating extraordinary lab-grown diamonds that embody both innovation and timeless elegance.
            </p>

            <p className="text-sm md:text-base text-white/60 font-light max-w-2xl mx-auto leading-relaxed"
               style={{ fontFamily: 'Times New Roman, serif' }}>
              Crafting Excellence • Ethical Luxury • Sustainable Beauty
            </p>
          </div>
        </section>

        {/* Heritage & Craftsmanship Section */}
        <section className="py-32 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-gray-900">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              {/* Left Content */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-extralight text-white mb-6 tracking-[0.2em] uppercase"
                      style={{ fontFamily: 'Times New Roman, serif' }}>
                    Our Heritage
                  </h2>
                  <div className="flex items-center mb-8">
                    <div className="w-12 h-px bg-white/40"></div>
                    <div className="w-1 h-1 bg-white/60 rounded-full mx-3"></div>
                    <div className="w-12 h-px bg-white/40"></div>
                  </div>
                </div>

                <div className="space-y-6">
                  <p className="text-lg text-white/90 font-light leading-relaxed"
                     style={{ fontFamily: 'Times New Roman, serif', letterSpacing: '0.02em' }}>
                    Founded on the principles of excellence and innovation, Diamond Atelier represents the pinnacle of lab-grown diamond craftsmanship. Our journey began with a vision to create diamonds that honor both tradition and progress.
                  </p>

                  <p className="text-base text-white/70 font-light leading-relaxed"
                     style={{ fontFamily: 'Times New Roman, serif' }}>
                    Each diamond in our collection is meticulously crafted using cutting-edge technology, ensuring that every piece meets the highest standards of quality, brilliance, and ethical sourcing.
                  </p>
                </div>

                {/* Luxury Stats */}
                <div className="grid grid-cols-3 gap-8 pt-8 border-t border-white/10">
                  <div className="text-center">
                    <div className="text-2xl font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>10K+</div>
                    <div className="text-xs text-white/60 uppercase tracking-wider">Certified Diamonds</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>100+</div>
                    <div className="text-xs text-white/60 uppercase tracking-wider">Unique Shapes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>30+</div>
                    <div className="text-xs text-white/60 uppercase tracking-wider">Color Variations</div>
                  </div>
                </div>
              </div>

              {/* Right Visual - Luxury Showroom */}
              <div className="relative">
                <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-white/5 to-white/2 border border-white/10">
                  <MemoryOptimizedImage
                    src="/images/Group-44.png"
                    alt="Diamond Atelier Luxury Showroom Interior"
                    width={600}
                    height={450}
                    className="w-full h-full object-cover filter brightness-110 hover:brightness-125 transition-all duration-700"
                    priority={false}
                    quality={90}
                  />

                  {/* Elegant Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

                  {/* Luxury Badge */}
                  <div className="absolute top-6 left-6">
                    <div className="bg-white/90 backdrop-blur-sm text-black px-4 py-2 rounded-full text-sm font-light tracking-wider uppercase shadow-lg">
                      <span style={{ fontFamily: 'Times New Roman, serif' }}>Our Showroom</span>
                    </div>
                  </div>
                </div>

                {/* Floating Accent Elements */}
                <div className="absolute -top-4 -right-4 w-12 h-12 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20"></div>
                <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-xl backdrop-blur-sm"></div>
                <div className="absolute top-1/2 -left-8 w-6 h-6 bg-white/15 rounded-lg backdrop-blur-sm"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Artisanal Process Section */}
        <section className="py-32 px-4 sm:px-6 lg:px-8 bg-black relative overflow-hidden">
          {/* Luxury Background Pattern */}
          <div className="absolute inset-0 opacity-3">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <div className="text-center mb-20">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-extralight text-white mb-8 tracking-[0.25em] uppercase"
                  style={{ fontFamily: 'Times New Roman, serif' }}>
                The Art of Creation
              </h2>
              <div className="flex items-center justify-center mb-8">
                <div className="w-16 h-px bg-white/30"></div>
                <div className="w-2 h-2 bg-white/50 rounded-full mx-6"></div>
                <div className="w-16 h-px bg-white/30"></div>
              </div>
              <p className="text-lg text-white/80 font-light leading-relaxed max-w-3xl mx-auto"
                 style={{ fontFamily: 'Times New Roman, serif', letterSpacing: '0.02em' }}>
                Our meticulous four-stage process transforms carbon into extraordinary diamonds, each step guided by precision and artistry.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {evolution.map((item, index) => (
                <div
                  key={item.id}
                  className="group relative"
                >
                  {/* Luxury Card */}
                  <div className="bg-gradient-to-b from-white/5 to-white/2 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-700 text-center h-full">
                    {/* Stage Number */}
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="w-8 h-8 bg-gradient-to-br from-white/20 to-white/10 rounded-full flex items-center justify-center border border-white/20">
                        <span className="text-xs font-light text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
                          {index + 1}
                        </span>
                      </div>
                    </div>

                    {/* Diamond Image */}
                    <div className="relative h-56 rounded-xl overflow-hidden bg-gradient-to-br from-white/5 to-transparent flex items-center justify-center mb-8 group-hover:from-white/10 transition-all duration-700">
                      <MemoryOptimizedImage
                        src={item.img}
                        alt={item.text}
                        width={220}
                        height={220}
                        className="object-contain w-full h-full filter brightness-110 group-hover:brightness-125 transition-all duration-700"
                        priority={index === 0}
                        quality={85}
                      />

                      {/* Luxury Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    </div>

                    {/* Stage Title */}
                    <h3 className="text-lg font-light text-white mb-4 tracking-[0.1em] uppercase"
                        style={{ fontFamily: 'Times New Roman, serif' }}>
                      {item.text}
                    </h3>

                    {/* Elegant Divider */}
                    <div className="flex items-center justify-center">
                      <div className="w-8 h-px bg-white/20"></div>
                      <div className="w-1 h-1 bg-white/40 rounded-full mx-2"></div>
                      <div className="w-8 h-px bg-white/20"></div>
                    </div>
                  </div>

                  {/* Connection Line (except for last item) */}
                  {index < evolution.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-px bg-gradient-to-r from-white/20 to-transparent transform -translate-y-1/2 z-10"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Excellence Standards Section */}
        <section className="py-32 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-gray-900 to-black">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-extralight text-white mb-8 tracking-[0.25em] uppercase"
                  style={{ fontFamily: 'Times New Roman, serif' }}>
                Our Promise
              </h2>
              <div className="flex items-center justify-center mb-8">
                <div className="w-16 h-px bg-white/30"></div>
                <div className="w-2 h-2 bg-white/50 rounded-full mx-6"></div>
                <div className="w-16 h-px bg-white/30"></div>
              </div>
              <p className="text-lg text-white/80 font-light leading-relaxed max-w-3xl mx-auto"
                 style={{ fontFamily: 'Times New Roman, serif', letterSpacing: '0.02em' }}>
                Every aspect of our business reflects our unwavering commitment to excellence, integrity, and innovation.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {policy.map((item, index) => (
                <div
                  key={index}
                  className="group relative"
                >
                  {/* Luxury Card */}
                  <div className="bg-gradient-to-b from-white/8 to-white/3 backdrop-blur-sm rounded-3xl p-10 border border-white/15 hover:border-white/25 transition-all duration-700 text-center h-full relative overflow-hidden">
                    {/* Subtle Background Pattern */}
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/svg%3E")`,
                      }}></div>
                    </div>

                    {/* Icon Container */}
                    <div className="relative mb-8">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-br from-white/10 to-white/5 rounded-full flex items-center justify-center border border-white/20 group-hover:border-white/30 transition-all duration-700">
                        <Image
                          src={item.image}
                          alt={item.title}
                          className="w-12 h-12 object-contain filter brightness-110"
                        />
                      </div>

                      {/* Floating Accent */}
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-white/10 rounded-full backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-all duration-700"></div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10">
                      <h3 className="text-xl font-light text-white mb-6 tracking-[0.1em] uppercase"
                          style={{ fontFamily: 'Times New Roman, serif' }}>
                        {item.title}
                      </h3>

                      {/* Elegant Divider */}
                      <div className="flex items-center justify-center mb-6">
                        <div className="w-12 h-px bg-white/20"></div>
                        <div className="w-1.5 h-1.5 bg-white/40 rounded-full mx-3"></div>
                        <div className="w-12 h-px bg-white/20"></div>
                      </div>

                      <p className="text-sm text-white/70 font-light leading-relaxed"
                         style={{ fontFamily: 'Times New Roman, serif', letterSpacing: '0.01em' }}>
                        {item.description}
                      </p>
                    </div>

                    {/* Luxury Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-3xl"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Section 3 - Video */}
        {/* <section
          ref={(el) => (sectionsRef.current[2] = el)}
          className="snap-start relative min-h-screen flex items-center justify-center scroll-mt-24"
        >
          <video
            className="absolute inset-0 w-full h-full object-cover"
            autoPlay
            muted
            loop
            playsInline
          >
            <source src="/images/about/AboutLabGrown.mp4" type="video/mp4" />
          </video>
          <div className="absolute inset-0 bg-black/40"></div>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={
              activeIndex === 2
                ? { opacity: 1, scale: 1 }
                : { opacity: 0.7, scale: 0.9 }
            }
            transition={{ duration: 0.8 }}
            className="relative z-10 text-center px-4"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-4">
              Lab-Grown Excellence
            </h2>
            <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
              Discover the future of diamonds with our sustainable, ethically
              sourced lab-grown gems.
            </p>
          </motion.div>
        </section> */}

        {/* Section 4 - Final */}
        {/* <section
          ref={(el) => (sectionsRef.current[3] = el)}
          className="snap-start min-h-screen flex flex-col justify-center items-center text-center space-y-8 px-4 sm:px-6 md:px-12 lg:px-20 xl:px-28 2xl:px-36 4k:px-48 5k:px-64 scroll-mt-24"
        >
          <motion.h2
            initial={{ opacity: 0, y: 40 }}
            animate={
              activeIndex === 3 ? { opacity: 1, y: 0 } : { opacity: 0.7, y: 20 }
            }
            transition={{ duration: 0.8 }}
            className="text-3xl md:text-5xl lg:text-6xl font-bold text-gray-300"
          >
            Crafting Tomorrow's Treasures
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 40 }}
            animate={
              activeIndex === 3 ? { opacity: 1, y: 0 } : { opacity: 0.7, y: 20 }
            }
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg md:text-xl lg:text-2xl text-gray-400 leading-relaxed max-w-4xl mx-auto"
          >
            Every diamond we create tells a story of innovation, sustainability,
            and uncompromising quality. Join us in shaping the future of luxury.
          </motion.p>
        </section> */}
      </main>
    </div>
    </>
  );
};

export default ClientAboutPage;
