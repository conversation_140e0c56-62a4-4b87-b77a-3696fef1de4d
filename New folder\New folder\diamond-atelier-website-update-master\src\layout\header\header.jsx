"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Menu, X, Search, User, ShoppingBag } from "lucide-react";

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const menuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const pathname = usePathname();

  // Navigation routes - Luxury brand style
  const navigationRoutes = [
    { id: 1, label: "INVENTORY", path: "https://inventory.diamondatelier.in/", external: true },
    { id: 2, label: "SHAPES", path: "/shapes" },
    { id: 3, label: "BLOGS", path: "/blogs" },
    { id: 4, label: "EDUCATION", path: "/education" },
    { id: 5, label: "ABOUT", path: "/about" },
    { id: 6, label: "CONTACT", path: "/contact" }
  ];

  // Optimized scroll detection
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY;
          setIsScrolled(scrollY > 20);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        hamburgerRef.current &&
        !hamburgerRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);

  const isActive = (route) => {
    if (route.path === "/" && pathname === "/") return true;
    if (route.path !== "/" && pathname.startsWith(route.path)) return true;
    return false;
  };

  return (
    <>
      {/* Main Header */}
      <header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-out ${
          isScrolled
            ? "bg-black/95 backdrop-blur-xl border-b border-white/10 shadow-2xl"
            : "bg-transparent"
        }`}
        style={{
          background: isScrolled
            ? "linear-gradient(180deg, rgba(0,0,0,0.98) 0%, rgba(0,0,0,0.95) 100%)"
            : "transparent",
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link
                href="/"
                className="group flex items-center space-x-3"
                onClick={closeMenu}
              >
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-br from-white to-gray-300 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <span className="text-black font-bold text-lg">DA</span>
                  </div>
                  <div className="absolute inset-0 bg-white rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </div>
                <div className="hidden sm:block">
                  <h1
                    className="text-lg sm:text-xl font-light tracking-[0.25em] uppercase text-white group-hover:text-gray-300 transition-colors duration-300 whitespace-nowrap"
                    style={{
                      fontFamily: 'Times New Roman, serif',
                      letterSpacing: '0.25em'
                    }}
                  >
                    DIAMOND ATELIER
                  </h1>
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navigationRoutes.map((route) => {
                const linkClasses = `group relative text-white hover:text-gray-300 text-sm font-light uppercase tracking-[0.15em] transition-all duration-300 py-2 px-4 rounded-lg hover:bg-white/10 ${
                  isActive(route) ? "text-gray-300 bg-white/15" : ""
                }`;

                return route.external ? (
                  <a
                    key={route.id}
                    href={route.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClasses}
                    style={{ fontFamily: "Times New Roman, serif" }}
                  >
                    {route.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300"></span>
                  </a>
                ) : (
                  <Link
                    key={route.id}
                    href={route.path}
                    className={linkClasses}
                    style={{ fontFamily: "Times New Roman, serif" }}
                    onClick={closeMenu}
                  >
                    {route.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300"></span>
                  </Link>
                );
              })}
            </nav>

            {/* Desktop Actions */}
            <div className="hidden lg:flex items-center space-x-4">
              <button className="p-2 text-white hover:text-gray-300 hover:bg-white/10 rounded-lg transition-all duration-300">
                <Search className="w-5 h-5" />
              </button>
              <button className="p-2 text-white hover:text-gray-300 hover:bg-white/10 rounded-lg transition-all duration-300">
                <User className="w-5 h-5" />
              </button>
              <button className="p-2 text-white hover:text-gray-300 hover:bg-white/10 rounded-lg transition-all duration-300">
                <ShoppingBag className="w-5 h-5" />
              </button>
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <button
                ref={hamburgerRef}
                onClick={toggleMenu}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  isScrolled
                    ? 'text-white hover:bg-white/20 bg-white/10 border border-white/30'
                    : 'text-white hover:bg-white/20 bg-white/10 border border-white/20'
                }`}
                aria-label="Toggle menu"
              >
                <div className="w-6 h-6 relative">
                  {menuOpen ? (
                    <X className="w-6 h-6 transition-transform duration-300 rotate-90" />
                  ) : (
                    <Menu className="w-6 h-6 transition-transform duration-300 hover:scale-110" />
                  )}
                </div>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {menuOpen && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-xl z-[60] lg:hidden transition-opacity duration-300"
          onClick={closeMenu}
        />
      )}

      {/* Mobile Menu */}
      <nav
        ref={menuRef}
        className={`fixed top-0 left-0 h-full w-80 max-w-sm bg-black/95 backdrop-blur-2xl z-[70] transform transition-transform duration-500 ease-out lg:hidden border-r border-white/20 ${
          menuOpen ? "translate-x-0" : "-translate-x-full"
        }`}
        style={{
          background: "linear-gradient(180deg, rgba(0,0,0,0.98) 0%, rgba(0,0,0,0.95) 100%)",
          boxShadow: "10px 0 50px rgba(0,0,0,0.8)"
        }}
      >
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <h1
                className="text-lg sm:text-lg font-light tracking-[0.25em] uppercase text-white whitespace-nowrap"
                style={{
                  fontFamily: 'Times New Roman, serif',
                  letterSpacing: '0.25em'
                }}
              >
                DIAMOND ATELIER
              </h1>
            </div>
            <button
              onClick={closeMenu}
              className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="p-6 space-y-2">
          {navigationRoutes.map((route) => {
            const linkClasses = `block px-4 py-4 text-base font-light uppercase tracking-[0.15em] transition-all duration-300 rounded-lg ${
              isActive(route)
                ? "text-white bg-white/10 border border-white/20"
                : "text-white/80 hover:text-white hover:bg-white/5"
            }`;
            return route.external ? (
              <a
                key={route.id}
                href={route.path}
                target="_blank"
                rel="noopener noreferrer"
                onClick={closeMenu}
                className={linkClasses}
                style={{ fontFamily: "Times New Roman, serif" }}
              >
                {route.label}
              </a>
            ) : (
              <Link
                key={route.id}
                href={route.path}
                onClick={closeMenu}
                className={linkClasses}
                style={{ fontFamily: "Times New Roman, serif" }}
              >
                {route.label}
              </Link>
            );
          })}
        </div>
      </nav>
    </>
  );
};

export default Header;
