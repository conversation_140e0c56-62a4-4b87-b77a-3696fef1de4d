"use client";
import React, { useState, useEffect, useRef } from 'react';

const WorkingCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    // Create unique session key for this counter
    const sessionKey = `working_counter_${targetNumber}_${suffix.replace(/[^a-zA-Z0-9]/g, '_')}`;
    const pageLoadKey = 'page_load_id';

    // Generate unique page load ID
    const currentPageId = Date.now().toString();
    const storedPageId = sessionStorage.getItem(pageLoadKey);

    // Check if this is a fresh page load
    const isNewPageLoad = !storedPageId || storedPageId !== currentPageId;

    if (isNewPageLoad) {
      // Fresh page - clear all counter data and set new page ID
      sessionStorage.clear();
      sessionStorage.setItem(pageLoadKey, currentPageId);
    }

    // Check if this counter has already animated in this session
    if (sessionStorage.getItem(sessionKey)) {
      // Already animated - show final number immediately
      setCount(targetNumber);
      setHasAnimated(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setHasAnimated(true);

          // Mark this counter as animated
          sessionStorage.setItem(sessionKey, 'completed');

          // Simple interval-based counting
          const steps = 60; // 60 steps for smooth animation
          const stepValue = targetNumber / steps;
          const stepDelay = duration / steps;

          let currentStep = 0;

          const timer = setInterval(() => {
            currentStep++;
            const newCount = Math.floor(currentStep * stepValue);

            if (currentStep >= steps) {
              setCount(targetNumber);
              clearInterval(timer);
            } else {
              setCount(newCount);
            }
          }, stepDelay);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [targetNumber, duration, suffix, hasAnimated]);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit'
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

export default WorkingCounter;
