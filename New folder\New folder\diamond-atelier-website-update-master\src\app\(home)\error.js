'use client';

export default function Error({ error, reset }) {
  return (
    <div className="bg-black min-h-screen flex items-center justify-center">
      <div className="text-center text-white px-6">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-red-500 mb-4">Oops!</h1>
          <h2 className="text-2xl font-montserrat mb-4">Something went wrong</h2>
          <p className="text-gray-400 mb-8">
            We encountered an error while loading the Diamond Atelier homepage.
          </p>
        </div>
        
        <div className="space-y-4">
          <button
            onClick={() => reset()}
            className="bg-white text-black px-8 py-3 rounded-full font-bold hover:bg-gray-200 transition-colors mr-4"
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="border border-white text-white px-8 py-3 rounded-full font-bold hover:bg-white hover:text-black transition-colors"
          >
            Go Home
          </button>
        </div>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-8 text-left bg-gray-900 p-4 rounded">
            <summary className="cursor-pointer text-red-400 font-bold">
              Error Details (Development Only)
            </summary>
            <pre className="mt-4 text-sm text-gray-300 overflow-auto">
              {error?.message || 'Unknown error occurred'}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}
