"use client";

import React, { useEffect, useState } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';

function AOSWrapper({ children }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    AOS.init({
      duration: 1000,
      once: false,
      mirror: false,
    });
  }, []);

  // Prevent hydration mismatch by not rendering AOS-dependent content until mounted
  if (!mounted) {
    return <>{children}</>;
  }

  return <>{children}</>;
}

export default AOSWrapper;
