"use client";
import { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

// Google Analytics Configuration
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX';

// Initialize Google Analytics
export const initGA = () => {
  if (typeof window !== 'undefined' && GA_MEASUREMENT_ID) {
    // Load Google Analytics script
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    document.head.appendChild(script1);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;
    
    gtag('js', new Date());
    gtag('config', GA_MEASUREMENT_ID, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }
};

// Track page views
export const trackPageView = (url, title) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_title: title,
      page_location: url,
    });
  }
};

// Track custom events
export const trackEvent = (action, category, label, value) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// Track diamond-specific events
export const trackDiamondEvents = {
  viewDiamond: (diamondId, diamondType) => {
    trackEvent('view_diamond', 'diamonds', `${diamondType}_${diamondId}`);
  },
  
  inquireDiamond: (diamondId, diamondType) => {
    trackEvent('inquire_diamond', 'diamonds', `${diamondType}_${diamondId}`);
  },
  
  downloadCertificate: (diamondId) => {
    trackEvent('download_certificate', 'certificates', diamondId);
  },
  
  contactForm: (formType) => {
    trackEvent('contact_form_submit', 'forms', formType);
  },
  
  educationView: (section) => {
    trackEvent('view_education', 'education', section);
  },
  
  inventorySearch: (searchTerm) => {
    trackEvent('inventory_search', 'search', searchTerm);
  }
};

// Google Analytics Component
const GoogleAnalytics = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Initialize GA on component mount
    initGA();
  }, []);

  useEffect(() => {
    // Track page views on route changes
    if (isClient && pathname && typeof window !== 'undefined') {
      const url = window.location.href;
      const title = document.title;
      trackPageView(url, title);
    }
  }, [pathname, searchParams, isClient]);

  return null;
};

export default GoogleAnalytics;
