"use client";
import { useEffect, useState, useRef } from "react";

export default function AnimatedHeading({ 
  text, 
  className = "", 
  level = "h1",
  showUnderline = true,
  animationDelay = 0.2,
  triggerOnMount = true 
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const headingRef = useRef(null);

  useEffect(() => {
    if (triggerOnMount && !hasAnimated) {
      // Trigger animation immediately when component mounts
      const timer = setTimeout(() => {
        setIsVisible(true);
        setHasAnimated(true);
      }, 100);
      return () => clearTimeout(timer);
    }

    // Intersection Observer for scroll-triggered animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setIsVisible(true);
            setHasAnimated(true);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    const currentRef = headingRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [triggerOnMount, hasAnimated]);

  const words = text.split(' ');
  const HeadingTag = level;

  return (
    <div ref={headingRef} className="relative">
      <HeadingTag className={`${className} overflow-hidden`}>
        {words.map((word, index) => (
          <span
            key={index}
            className={`inline-block transition-all duration-1000 ease-out ${
              isVisible 
                ? 'transform translate-y-0 opacity-100' 
                : 'transform translate-y-full opacity-0'
            }`}
            style={{
              transitionDelay: `${animationDelay + index * 0.1}s`
            }}
          >
            {word}
            {index < words.length - 1 && '\u00A0'}
          </span>
        ))}
      </HeadingTag>
      
      {showUnderline && (
        <div 
          className={`h-0.5 bg-current mt-4 transition-all duration-1000 ease-out ${
            isVisible ? 'w-16 opacity-100' : 'w-0 opacity-0'
          }`}
          style={{
            transitionDelay: `${animationDelay + words.length * 0.1 + 0.2}s`
          }}
        />
      )}
    </div>
  );
}
