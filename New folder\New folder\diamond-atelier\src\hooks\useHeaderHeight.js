"use client";
import { useState, useEffect } from 'react';

/**
 * Dynamic Header Height Management System
 *
 * This hook system solves the problem of inconsistent header spacing across different screen sizes.
 * Instead of using fixed CSS values, it calculates the actual header height in real-time and
 * adjusts spacing accordingly.
 *
 * Features:
 * - Real-time header height calculation
 * - Automatic screen size detection and buffer adjustment
 * - Responsive to font loading and header changes
 * - Smooth transitions when header size changes
 * - Support for education pages with additional navigation
 *
 * Usage Examples:
 *
 * 1. Basic header spacing:
 *    const { headerHeight } = useHeaderHeight();
 *
 * 2. Avoid header overlap:
 *    const spacing = useAvoidHeaderOverlap(20); // 20px extra buffer
 *    <div style={spacing}>Content</div>
 *
 * 3. Education pages:
 *    const spacing = useEducationSpacing(10); // 10px extra buffer
 *    <div style={spacing}>Content</div>
 */

export const useHeaderHeight = () => {
  const [headerHeight, setHeaderHeight] = useState(110); // Default fallback
  const [educationNavHeight, setEducationNavHeight] = useState(60);
  const [isClient, setIsClient] = useState(false);

  // Prevent hydration mismatch by ensuring client-side only execution
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return; // Only run on client side

    let resizeTimeout;
    let observer;

    const calculateHeaderHeight = () => {
      try {
        // Try to get actual header element
        const header = document.querySelector('header');
        if (header) {
          const headerRect = header.getBoundingClientRect();
          const actualHeight = headerRect.height;

          // Add buffer based on screen size
          const screenWidth = window.innerWidth;
          let buffer = 20; // Default buffer

          if (screenWidth >= 3840) buffer = 30; // 5K screens
          else if (screenWidth >= 2560) buffer = 25; // 4K screens
          else if (screenWidth >= 1920) buffer = 20; // 3XL screens
          else if (screenWidth >= 1536) buffer = 20; // 2XL screens
          else if (screenWidth >= 1280) buffer = 15; // XL screens
          else if (screenWidth >= 1024) buffer = 15; // LG screens (your laptop)
          else if (screenWidth >= 768) buffer = 10;  // MD screens
          else if (screenWidth >= 640) buffer = 10;  // SM screens
          else buffer = 10; // Mobile

          const finalHeight = actualHeight + buffer;
          setHeaderHeight(finalHeight);

          // Update CSS custom property for global use
          document.documentElement.style.setProperty('--dynamic-header-height', `${finalHeight}px`);
          document.documentElement.style.setProperty('--education-nav-top', `${actualHeight}px`);
        }
      } catch (error) {
        console.warn('Header height calculation failed:', error);
        // Fallback to default values
        setHeaderHeight(110);
        setEducationNavHeight(60);
      }
    };

    // Throttled resize handler for better performance
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(calculateHeaderHeight, 150); // Increased debounce time
    };

    // Calculate on mount
    calculateHeaderHeight();

    // Add event listeners with passive option for better performance
    window.addEventListener('resize', handleResize, { passive: true });

    // Observer for header changes with throttling
    let observerTimeout;
    const throttledCalculate = () => {
      clearTimeout(observerTimeout);
      observerTimeout = setTimeout(calculateHeaderHeight, 100);
    };

    observer = new MutationObserver(throttledCalculate);
    const header = document.querySelector('header');
    if (header) {
      observer.observe(header, {
        childList: true,
        subtree: true,
        attributes: true
      });
    }

    return () => {
      // Comprehensive cleanup
      window.removeEventListener('resize', handleResize);
      if (observer) {
        observer.disconnect();
      }
      clearTimeout(resizeTimeout);
      clearTimeout(observerTimeout);
    };
  }, [isClient]);

  return {
    headerHeight,
    educationNavHeight,
    // Utility function to get spacing for specific use cases
    getSpacing: (extraBuffer = 0) => headerHeight + extraBuffer,
    // For education pages with additional nav
    getEducationSpacing: (extraBuffer = 0) => headerHeight + educationNavHeight + extraBuffer
  };
};

// Hook for components that need to avoid header overlap
export const useAvoidHeaderOverlap = (extraBuffer = 0) => {
  const { headerHeight } = useHeaderHeight();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Return safe values during SSR
  if (!isClient) {
    return {
      marginTop: 110 + extraBuffer, // Fallback value
      paddingTop: extraBuffer > 0 ? extraBuffer : 0
    };
  }

  return {
    marginTop: headerHeight + extraBuffer,
    paddingTop: extraBuffer > 0 ? extraBuffer : 0
  };
};

// Hook specifically for education pages
export const useEducationSpacing = (extraBuffer = 0) => {
  const { getEducationSpacing } = useHeaderHeight();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Return safe values during SSR
  if (!isClient) {
    return {
      marginTop: 170 + extraBuffer // Fallback value for education pages
    };
  }

  return {
    marginTop: getEducationSpacing(extraBuffer)
  };
};

// Utility function to get current header height (can be used in any component)
export const getCurrentHeaderHeight = () => {
  const headerElement = document.querySelector('header');
  if (headerElement) {
    return headerElement.getBoundingClientRect().height;
  }
  return 110; // fallback
};

// CSS class generator for dynamic spacing
export const generateSpacingClass = (extraBuffer = 0) => {
  const headerHeight = getCurrentHeaderHeight();
  return {
    marginTop: `${headerHeight + extraBuffer}px`
  };
};
