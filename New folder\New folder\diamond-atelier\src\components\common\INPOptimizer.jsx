"use client";
import { useEffect } from 'react';

/**
 * INP (Interaction to Next Paint) Optimizer Component
 * 
 * Optimizes interactions for better INP scores:
 * - Debounces rapid interactions
 * - Optimizes event listeners
 * - Reduces main thread blocking
 */
const INPOptimizer = () => {
  useEffect(() => {
    // Optimize pointer events for better INP
    const optimizePointerEvents = () => {
      // Add passive event listeners where possible
      const elements = document.querySelectorAll('[data-inp-optimize]');
      
      elements.forEach(element => {
        // Remove existing listeners that might block main thread
        const newElement = element.cloneNode(true);
        element.parentNode?.replaceChild(newElement, element);
        
        // Add optimized listeners
        newElement.addEventListener('pointerdown', (e) => {
          // Use requestIdleCallback for non-critical work
          if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
              // Non-critical pointer handling
            });
          }
        }, { passive: true });
      });
    };

    // Debounce rapid interactions
    let interactionTimeout;
    const debounceInteractions = (callback, delay = 16) => {
      return (...args) => {
        clearTimeout(interactionTimeout);
        interactionTimeout = setTimeout(() => callback.apply(this, args), delay);
      };
    };

    // Optimize scroll performance for INP
    let scrollTicking = false;
    const optimizedScrollHandler = debounceInteractions(() => {
      if (!scrollTicking) {
        requestAnimationFrame(() => {
          // Minimal scroll handling
          scrollTicking = false;
        });
        scrollTicking = true;
      }
    }, 16);

    // Add optimized scroll listener
    window.addEventListener('scroll', optimizedScrollHandler, { 
      passive: true,
      capture: false 
    });

    // Optimize touch events for mobile INP
    const optimizeTouchEvents = () => {
      document.addEventListener('touchstart', (e) => {
        // Prevent unnecessary touch handling
      }, { passive: true });

      document.addEventListener('touchmove', (e) => {
        // Minimal touch move handling
      }, { passive: true });
    };

    // Initialize optimizations
    optimizePointerEvents();
    optimizeTouchEvents();

    // Cleanup
    return () => {
      window.removeEventListener('scroll', optimizedScrollHandler);
      clearTimeout(interactionTimeout);
    };
  }, []);

  // Monitor INP in development
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    // Monitor interactions
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'event') {
          const duration = entry.processingEnd - entry.startTime;
          
          if (duration > 200) {
            console.warn('🐌 Slow interaction detected:', {
              type: entry.name,
              duration: `${duration}ms`,
              target: entry.target,
              startTime: entry.startTime
            });
          } else if (duration > 100) {
            console.log('⚠️ Moderate interaction:', {
              type: entry.name,
              duration: `${duration}ms`
            });
          }
        }
        
        if (entry.entryType === 'first-input') {
          console.log('👆 First Input Delay:', `${entry.processingStart - entry.startTime}ms`);
        }
      });
    });

    // Observe interaction events
    try {
      observer.observe({ 
        entryTypes: ['event', 'first-input'],
        buffered: true 
      });
    } catch (e) {
      // Fallback for browsers that don't support event timing
      console.log('Event timing not supported');
    }

    return () => observer.disconnect();
  }, []);

  return null; // This component doesn't render anything
};

/**
 * Hook for optimizing specific interactions
 */
export const useINPOptimization = (elementRef) => {
  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    
    // Add INP optimization attributes
    element.setAttribute('data-inp-optimize', 'true');
    
    // Optimize specific element
    element.style.contain = 'layout style paint';
    element.style.willChange = 'auto';
    element.style.touchAction = 'manipulation';
    
    return () => {
      element.removeAttribute('data-inp-optimize');
    };
  }, [elementRef]);
};

/**
 * Utility to measure interaction performance
 */
export const measureInteraction = (name, callback) => {
  const start = performance.now();
  
  const result = callback();
  
  const end = performance.now();
  const duration = end - start;
  
  if (duration > 100) {
    console.warn(`🐌 Slow interaction "${name}": ${duration}ms`);
  }
  
  return result;
};

/**
 * Debounce utility for interactions
 */
export const debounceInteraction = (func, wait = 16) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export default INPOptimizer;
