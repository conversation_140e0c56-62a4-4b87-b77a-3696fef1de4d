// "use client";

// import React from "react";
// import Image from "next/image";
// import { motion } from "framer-motion";
// import mythImage from "../../../public/images/about/myth.png";

// const containerVariants = {
//   hidden: {},
//   visible: {
//     transition: { staggerChildren: 0.25 },
//   },
// };

// const itemVariants = {
//   hidden: { opacity: 0, y: 50 },
//   visible: {
//     opacity: 1,
//     y: 0,
//     transition: { duration: 0.6, ease: "easeOut" },
//   },
// };

// const mythsAndFacts = [
//   {
//     myth: "Lab-grown diamonds are synthetic in nature!",
//     fact:
//       "Lab-grown diamonds are not fake. Unlike glass or other imitations, they’re as durable and sparkly as natural diamonds.",
//   },
//   {
//     myth: "Lab Grown diamond fade over a period of time!",
//     fact:
//       "A lab diamond never gets cloudy or changes color. It retains brilliance forever, even in harsh chemicals.",
//   },
//   {
//     myth: "Lab-grown diamonds are less precious!",
//     fact:
//       "They're not cheaper due to quality but a shorter supply chain. They're scientifically and ethically valuable.",
//   },
//   {
//     myth: "Lab Grown diamond does not have any resale value!",
//     fact:
//       "Lab diamonds are increasingly resold. Their ethical appeal adds to their value among younger generations.",
//   },
//   {
//     myth: "Lab-grown diamonds aren’t eco-friendly!",
//     fact:
//       "HPHT requires electricity, but far less than mining. They're ethical choices for both people and the planet.",
//   },
// ];

// export default function Myths() {
//   return (
//     <div className="bg-black text-white">
//       {/* Hero Section */}
//       <section className="relative w-full h-[70vh] sm:h-screen">
//         <Image
//           src={mythImage}
//           alt="Myths about lab grown diamonds"
//           fill
//           priority
//           sizes="100vw"
//           className="object-cover"
//         />
//         <motion.h1
//           initial={{ opacity: 0, y: -30 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.8, ease: "easeOut" }}
//           viewport={{ once: true, amount: 0.4 }}
//           className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center uppercase font-bold font-montserrat px-4 text-xl sm:text-2xl md:text-3xl xl:text-5xl leading-snug tracking-wide"
//         >
//           Myths & Facts about
//           <br />
//           Lab Grown Diamonds
//         </motion.h1>
//       </section>

//       {/* Myth List Section */}
//       <motion.section
//         className="max-w-6xl mx-auto p-6 md:p-12 flex flex-col gap-10"
//         variants={containerVariants}
//         initial="hidden"
//         whileInView="visible"
//         viewport={{ once: true, amount: 0.3 }}
//       >
//         {mythsAndFacts.map((item, index) => (
//           <motion.div
//             key={index}
//             className="flex flex-col md:flex-row items-center gap-4 md:gap-6"
//             variants={itemVariants}
//             whileHover={{ scale: 1.02 }}
//             transition={{ type: "spring", stiffness: 300 }}
//           >
//             {/* Label Box */}
//             <div className="text-center min-w-[100px]">
//               <div className="bg-[#6b6d6d] p-3 rounded-t-md">
//                 <span className="text-white font-semibold text-sm font-montserrat tracking-wider">
//                   MYTH {index + 1}
//                 </span>
//               </div>
//               <div className="bg-[#d9d9d9] p-3 rounded-b-md">
//                 <span className="text-black font-semibold text-sm font-montserrat tracking-wider">
//                   FACT
//                 </span>
//               </div>
//             </div>

//             {/* Content Box */}
//             <div className="flex-1 border border-[#6b6d6d] rounded-md overflow-hidden">
//               <div className="bg-black p-4 text-center">
//                 <h2 className="text-[#6b6d6d] font-bold font-montserrat uppercase tracking-wide text-sm sm:text-base md:text-lg xl:text-xl">
//                   {item.myth}
//                 </h2>
//               </div>
//               <div className="bg-[#e9e9e9] p-6 text-justify">
//                 <p className="text-[#6b6d6d] font-montserrat font-medium leading-relaxed text-xs sm:text-sm md:text-base">
//                   {item.fact}
//                 </p>
//               </div>
//             </div>
//           </motion.div>
//         ))}

//       </motion.section>
//     </div>
//   );
// }



"use client";

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import mythImage from "../../../public/images/about/myth.png";

const containerVariants = {
  hidden: {},
  visible: {
    transition: { staggerChildren: 0.25 },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

const mythsAndFacts = [
  {
    myth: "Lab-grown diamonds are synthetic in nature!",
    fact:
      "Lab-grown diamonds are not fake. Unlike glass or other imitations, they’re as durable and sparkly as natural diamonds.",
  },
  {
    myth: "Lab Grown diamond fade over a period of time!",
    fact:
      "A lab diamond never gets cloudy or changes color. It retains brilliance forever, even in harsh chemicals.",
  },
  {
    myth: "Lab-grown diamonds are less precious!",
    fact:
      "They're not cheaper due to quality but a shorter supply chain. They're scientifically and ethically valuable.",
  },
  {
    myth: "Lab Grown diamond does not have any resale value!",
    fact:
      "Lab diamonds are increasingly resold. Their ethical appeal adds to their value among younger generations.",
  },
  {
    myth: "Lab-grown diamonds aren’t eco-friendly!",
    fact:
      "HPHT requires electricity, but far less than mining. They're ethical choices for both people and the planet.",
  },
];

export default function Myths() {
  return (
    <div className="bg-black text-white font-serif">
      {/* Hero Section */}
      <section className="pt-20 md:pt-24 pb-16 md:pb-20 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-black"></div>

        {/* Main Image */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-10 max-w-6xl relative z-10">
          <div className="relative w-full max-w-6xl mx-auto">
            <div className="relative aspect-video rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src={mythImage}
                alt="Myths & Facts about Lab Grown Diamonds"
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

              {/* Overlay Content */}
              <div className="absolute bottom-8 left-8 right-8">
                <motion.h1
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  viewport={{ once: true }}
                  className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-light tracking-wide text-amber-100 mb-4"
                >
                  Myths & Facts about Lab Grown Diamonds
                </motion.h1>
                <p className="text-lg md:text-xl text-slate-200 font-light max-w-2xl">
                  Separating truth from fiction in the world of lab-grown diamonds
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-20 md:py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-10 max-w-5xl">
          <div className="text-center mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-light tracking-wider text-amber-100 mb-4">
              SEPARATING FACT FROM FICTION
            </h2>
            <p className="text-lg md:text-xl text-slate-300 font-light max-w-3xl mx-auto">
              Discover the truth behind common misconceptions about lab-grown diamonds
            </p>
            <div className="w-32 h-0.5 bg-gradient-to-r from-emerald-400 to-amber-400 mx-auto mt-6"></div>
          </div>

        <motion.section
        className="max-w-5xl mx-auto p-4 md:p-8 lg:p-10 flex flex-col gap-10"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {mythsAndFacts.map((item, index) => (
          <motion.div
            key={index}
            className="flex flex-col md:flex-row items-center gap-4 md:gap-6"
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {/* Label Box */}
            <div className="text-center min-w-[100px]">
              <div className="bg-[#6b6d6d] p-3 rounded-t-md">
                <span className="text-white font-semibold text-sm font-montserrat tracking-wider">
                  MYTH {index + 1}
                </span>
              </div>
              <div className="bg-[#d9d9d9] p-3 rounded-b-md">
                <span className="text-black font-semibold text-sm font-montserrat tracking-wider">
                  FACT
                </span>
              </div>
            </div>

            {/* Content Box */}
            <div className="flex-1 border border-[#6b6d6d] rounded-md overflow-hidden">
              <div className="bg-black p-4 text-center">
                <h2 className="text-[#6b6d6d] font-bold font-montserrat uppercase tracking-wide text-sm sm:text-base md:text-lg xl:text-xl">
                  {item.myth}
                </h2>
              </div>
              <div className="bg-[#e9e9e9] p-6 text-justify">
                <p className="text-[#6b6d6d] font-montserrat font-medium leading-relaxed text-xs sm:text-sm md:text-base">
                  {item.fact}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.section>
        </div>
      </section>
    </div>
  );
}
