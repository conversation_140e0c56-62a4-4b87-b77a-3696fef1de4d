"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Menu, X } from "lucide-react";

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const menuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const pathname = usePathname();

  // Navigation routes - Original navigation
  const navigationRoutes = [
    { id: 1, label: "INVENTORY", path: "https://inventory.diamondatelier.in/", external: true },
    { id: 2, label: "HOME", path: "/" },
    { id: 3, label: "SHAPES", path: "/shapes" },
    { id: 4, label: "EDUCATION", path: "/education" },

    { id: 5, label: "CONTACT", path: "/contact" },

    { id: 6, label: "CAREERS", path: "/careers" },
    { id: 7, label: "ABOUT", path: "/about" }
  ];

  // Ultra-optimized scroll detection with RAF and throttling
  useEffect(() => {
    let rafId = null;
    let lastScrollY = 0;
    let ticking = false;

    const updateScrollState = () => {
      const scrollY = window.scrollY;

      // Only update if scroll position changed significantly
      if (Math.abs(scrollY - lastScrollY) > 5) {
        setIsScrolled(scrollY > 50);
        lastScrollY = scrollY;
      }

      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        rafId = requestAnimationFrame(updateScrollState);
        ticking = true;
      }
    };

    // Add scroll listener with passive option for better performance
    window.addEventListener("scroll", handleScroll, {
      passive: true,
      capture: false
    });

    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        hamburgerRef.current &&
        !hamburgerRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    if (menuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "unset";
    };
  }, [menuOpen]);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);
  const isActive = (item) => pathname === item.path;

  return (
    <>
      {/* PROMOTIONAL BANNER - Disappears on scroll */}
      {/* <div className="bg-gradient-to-r from-amber-600 via-amber-500 to-amber-600 text-white text-center py-2 px-4 text-sm font-medium relative z-50">
        Free Engagement Ring Setting with Purchase of 30 ct or Larger Diamond.
      </div> */}

      <header
        className={`sticky top-0 w-full z-50 transition-all duration-100 ease-out ${
          isScrolled
            ? "bg-black backdrop-blur-xl shadow-2xl border-b border-white/30"
            : "bg-black/30 backdrop-blur-md"
        }`}
        style={{
          boxShadow: isScrolled
            ? "0 10px 40px rgba(0,0,0,0.5), inset 0 1px 0 rgba(255,255,255,0.1)"
            : "0 5px 20px rgba(0,0,0,0.2)",
          WebkitBackdropFilter: isScrolled ? "blur(20px)" : "blur(10px)",
          backdropFilter: isScrolled ? "blur(20px)" : "blur(10px)",
          willChange: "transform",
          transform: "translateZ(0)", // Force GPU acceleration
          contain: "layout style paint"
        }}
      >

  
        <div className="max-w-7xl mx-auto px-16 sm:px-6 lg:px-8 mobile-header-container">

          <div className={`transition-all duration-100 ${isScrolled ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100'}`} style={{ willChange: 'opacity, height' }}>
    
            <div className="flex justify-center py-3 lg:py-4 px-4">
              <Link href="/" className="group relative block">
                <div className="relative transition-all duration-100" style={{ willChange: 'transform' }}>
                  <h1
                    className="text-white text-xl sm:text-xl md:text-2xl lg:text-3xl font-light tracking-[0.3em] uppercase transition-all duration-200 whitespace-nowrap"
                    style={{
                      fontFamily: 'Times New Roman, serif',
                      letterSpacing: '0.3em'
                    }}
                  >
                    DIAMOND ATELIER
                  </h1>

                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>
                </div>
              </Link>
            </div>


            <nav className="hidden lg:block pb-3">
              <div className="flex items-center justify-center space-x-8">
                {navigationRoutes.map((item) => {
                  const linkClasses = `group relative text-white hover:text-white text-sm font-medium uppercase tracking-[0.15em] transition-all duration-200 py-2 px-4 rounded-lg hover:bg-white/10 ${
                    isActive(item) ? "text-white bg-white/20 shadow-lg" : ""
                  }`;

                  const linkContent = (
                    <>
                      <span className="relative z-10">{item.label}</span>
                    
                      <span className={`absolute bottom-0 left-1/2 w-0 h-px bg-gradient-to-r from-transparent via-white to-transparent group-hover:w-full transition-all duration-500 transform -translate-x-1/2 ${
                        isActive(item) ? 'w-full' : ''
                      }`}></span>
                
                      <span className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg blur-sm"></span>
                    </>
                  );

                  return item.external ? (
                    <a
                      key={item.id}
                      href={item.path}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={linkClasses}
                      style={{ fontFamily: "Times New Roman, serif" }}
                    >
                      {linkContent}
                    </a>
                  ) : (
                    <Link
                      key={item.id}
                      href={item.path}
                      className={linkClasses}
                      style={{ fontFamily: "Times New Roman, serif" }}
                    >
                      {linkContent}
                    </Link>
                  );
                })}
              </div>
            </nav>
          </div>


          <div className={`transition-all duration-100 ${isScrolled ? 'opacity-100' : 'opacity-0 h-0 overflow-hidden'}`} style={{ willChange: 'opacity, height' }}>
            <div className="flex items-center justify-center lg:justify-between py-3 lg:py-2 px-4">
          
              <Link href="/" className="flex-shrink-0 group">
                <div className="relative transition-all duration-100" style={{ willChange: 'transform' }}>
                  {/* Mobile: Full name, Desktop: DA */}
                  <div className="lg:hidden">
                    <h1
                      className="text-lg sm:text-xl font-light tracking-[0.25em] uppercase text-white transition-all duration-200 whitespace-nowrap"
                      style={{
                        fontFamily: 'Times New Roman, serif',
                        letterSpacing: '0.25em'
                      }}
                    >
                      DIAMOND ATELIER
                    </h1>
                  </div>

                  {/* Desktop: DA logo */}
                  <div className="hidden lg:block">
                    <div
                      className="text-2xl sm:text-3xl md:text-4xl font-light transition-all duration-200 flex items-center"
                      style={{
                        fontFamily: 'Times New Roman, serif',
                        letterSpacing: '0.05em'
                      }}
                    >
                      <span
                        className="inline-block"
                        style={{
                          color: '#f1efe9ff',
                          textShadow: '0 0 10px rgba(184, 134, 11, 0.5)'
                        }}
                      >
                        D
                      </span>
                      <span
                        className="inline-block"
                        style={{
                          color: '#E5E5E5',
                          textShadow: '0 0 10px rgba(229, 229, 229, 0.3)'
                        }}
                      >
                        A
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
              <nav className="hidden lg:block flex-1 mx-8">
                <div className="flex items-center justify-center space-x-6">
                  {navigationRoutes.map((item) => {
                    const linkClasses = `group relative text-white hover:text-white text-sm font-medium uppercase tracking-[0.15em] transition-all duration-200 py-2 px-4 rounded-lg hover:bg-white/15 ${
                      isActive(item) ? "text-white bg-white/25 shadow-lg" : ""
                    }`;

                    const scrolledLinkContent = (
                      <>
                        <span className="relative z-10">{item.label}</span>
                        {/* Compact underline animation */}
                        <span className={`absolute bottom-0 left-1/2 w-0 h-px bg-gradient-to-r from-transparent via-gray-100 to-transparent group-hover:w-full transition-all duration-400 transform -translate-x-1/2 ${
                          isActive(item) ? 'w-full' : ''
                        }`}></span>
                      </>
                    );
                    return item.external ? (
                      <a
                        key={item.id}
                        href={item.path}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={linkClasses}
                        style={{ fontFamily: "Times New Roman, serif" }}
                      >
                        {scrolledLinkContent}
                      </a>
                    ) : (
                      <Link
                        key={item.id}
                        href={item.path}
                        className={linkClasses}
                        style={{ fontFamily: "Times New Roman, serif" }}
                      >
                        {scrolledLinkContent}
                      </Link>
                    );
                  })}
                </div>
              </nav>
            </div>
          </div>
          <div className="lg:hidden absolute top-1/2 left-4 -translate-y-1/2 z-[60]">
            <button
              ref={hamburgerRef}
              onClick={toggleMenu}
              className={`   ${
                isScrolled
                  ? 'text-white hover:bg-white/30 bg-white/20 border-white/50'
                  : 'text-white hover:bg-white/30 bg-white/15 border-white/40'
              }`}
              aria-label="Toggle menu"
            >
              <div className="w-6 h-6 relative">
                {menuOpen ? (
                  <X className="w-6 h-6 transition-transform duration-300 rotate-90" />
                ) : (
                  <Menu className="w-6 h-6 transition-transform duration-300 hover:scale-110" />
                )}
              </div>
            </button>
          </div>
        </div>
    </header>

    {menuOpen && (
      <div
        className="fixed inset-0 bg-black/90 backdrop-blur-xl z-[60] lg:hidden transition-opacity duration-300"
        onClick={closeMenu}
      />
    )} 
    <nav
        ref={menuRef}
        className={`fixed top-0 left-0 h-full w-80 max-w-sm bg-black/95 backdrop-blur-2xl z-[70] transform transition-transform duration-500 ease-out lg:hidden border-r border-white/20 ${
          menuOpen ? "translate-x-0" : "-translate-x-full"
        }`}
        style={{
          background: "linear-gradient(180deg, rgba(0,0,0,0.98) 0%, rgba(0,0,0,0.95) 100%)",
          boxShadow: "10px 0 50px rgba(0,0,0,0.8)"
        }}
      >
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <h1
                className="text-lg sm:text-lg font-light tracking-[0.25em] uppercase text-white whitespace-nowrap"
                style={{
                  fontFamily: 'Times New Roman, serif',
                  letterSpacing: '0.25em'
                }}
              >
                DIAMOND ATELIER
              </h1>
            </div>
            <button
              onClick={closeMenu}
              className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="p-6 space-y-2">
          {navigationRoutes.map((item) => {
            const linkClasses = `block px-4 py-4 text-base font-light uppercase tracking-[0.15em] transition-all duration-300 rounded-lg ${
              isActive(item)
                ? "text-white bg-white/10 border border-white/20"
                : "text-white/80 hover:text-white hover:bg-white/5"
            }`;
            return item.external ? (
              <a
                key={item.id}
                href={item.path}
                target="_blank"
                rel="noopener noreferrer"
                onClick={closeMenu}
                className={linkClasses}
                style={{ fontFamily: "Times New Roman, serif" }}
              >
                {item.label}
              </a>
            ) : (
              <Link
                key={item.id}
                href={item.path}
                onClick={closeMenu}
                className={linkClasses}
                style={{ fontFamily: "Times New Roman, serif" }}
              >
                {item.label}
              </Link>
            );
          })}
        </div>     
      </nav>
    </>
  );
};

export default Header;


// "use client";

// import React, { useState, useEffect, useRef } from "react";
// import { usePathname } from "next/navigation";
// import Link from "next/link";
// import { Menu, X, Search, User, ShoppingBag } from "lucide-react";

// const Header = () => {
//   const [menuOpen, setMenuOpen] = useState(false);
//   const [isScrolled, setIsScrolled] = useState(false);
//   const menuRef = useRef(null);
//   const hamburgerRef = useRef(null);
//   const pathname = usePathname();

//   // Navigation routes - Luxury brand style
//   const navigationRoutes = [
//     { id: 1, label: "COLLECTIONS", path: "/shapes" },
//     { id: 2, label: "INVENTORY", path: "https://inventory.diamondatelier.in/", external: true },
//     { id: 3, label: "EDUCATION", path: "/education" },
//     { id: 4, label: "ABOUT", path: "/about" },
//     { id: 5, label: "CONTACT", path: "/contact" }
//   ];

//   // Optimized scroll detection
//   useEffect(() => {
//     let ticking = false;

//     const handleScroll = () => {
//       if (!ticking) {
//         requestAnimationFrame(() => {
//           const scrollY = window.scrollY;
//           setIsScrolled(scrollY > 20);
//           ticking = false;
//         });
//         ticking = true;
//       }
//     };

//     window.addEventListener("scroll", handleScroll, { passive: true });
//     return () => window.removeEventListener("scroll", handleScroll);
//   }, []);

//   // Close menu when clicking outside
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (
//         menuOpen &&
//         menuRef.current &&
//         !menuRef.current.contains(event.target) &&
//         hamburgerRef.current &&
//         !hamburgerRef.current.contains(event.target)
//       ) {
//         setMenuOpen(false);
//       }
//     };

//     document.addEventListener("mousedown", handleClickOutside);
//     return () => document.removeEventListener("mousedown", handleClickOutside);
//   }, [menuOpen]);

//   const toggleMenu = () => setMenuOpen(!menuOpen);
//   const closeMenu = () => setMenuOpen(false);
//   const isActive = (item) => pathname === item.path;

//   return (
//     <header className={`fixed top-0 w-full z-[100] transition-all duration-500 ${
//       isScrolled
//         ? 'bg-white/98 backdrop-blur-xl shadow-2xl border-b border-gray-200'
//         : 'bg-white/95 backdrop-blur-sm'
//     }`}>

//         {/* Elegant Top Border */}
//         <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>

//         <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-10">

//           {/* Top Row - Logo Only */}
//           <div className="flex items-center justify-center py-4">
//             <Link href="/" className="group relative">
//               <div className="text-center">
//                 <h1
//                   className={`font-light text-black tracking-[0.25em] transition-all duration-500 group-hover:tracking-[0.3em] ${
//                     isScrolled ? 'text-2xl lg:text-2xl' : 'text-2xl lg:text-3xl'
//                   }`}
//                   style={{
//                     fontFamily: 'Playfair Display, Times New Roman, serif',
//                     textShadow: '0 1px 2px rgba(0,0,0,0.1)'
//                   }}
//                 >
//                   DIAMOND ATELIER
//                 </h1>
//                 <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
//               </div>
//               <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/3 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-2xl"></div>
//             </Link>
//           </div>

//           {/* Separator Line */}
//           <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>

//           {/* Bottom Row - All Navigation in One Line */}
//           <div className="flex items-center justify-center py-4">
//             <nav className="hidden lg:flex items-center space-x-12">
//               {navigationRoutes.map((item) => {
//                 const linkClasses = `text-sm font-light text-gray-600 hover:text-black transition-all duration-400 tracking-[0.1em] relative group uppercase ${
//                   isActive(item) ? "text-black" : ""
//                 }`;

//                 const linkContent = (
//                   <>
//                     {item.label}
//                     <span className={`absolute -bottom-3 left-0 w-0 h-0.5 bg-black transition-all duration-500 group-hover:w-full ${
//                       isActive(item) ? 'w-full' : ''
//                     }`}></span>
//                   </>
//                 );

//                 return item.external ? (
//                   <a
//                     key={item.id}
//                     href={item.path}
//                     target="_blank"
//                     rel="noopener noreferrer"
//                     className={linkClasses}
//                   >
//                     {linkContent}
//                   </a>
//                 ) : (
//                   <Link key={item.id} href={item.path} className={linkClasses}>
//                     {linkContent}
//                   </Link>
//                 );
//               })}
//             </nav>

//             {/* Mobile Menu Button */}
//             <button
//               ref={hamburgerRef}
//               onClick={toggleMenu}
//               className="lg:hidden absolute top-4 right-4 p-3 text-gray-600 hover:text-black transition-colors duration-300 hover:bg-gray-50 rounded-lg"
//               aria-label="Toggle menu"
//             >
//               {menuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
//             </button>
//           </div>
//         </div>
//       </header>
//   );
// };

// export default Header;
