// "use client";

// import React, { useEffect, useRef, useState } from "react";
// import Image from "next/image";
// import { motion } from "framer-motion";
// import AOS from "aos";
// import "aos/dist/aos.css";

// import certified from "../../../public/images/about/certified.png";
// import melee from "../../../public/images/about/star-melee.png";
// import money from "../../../public/images/banner/money.png";
// import certificate from "../../../public/images/banner/certificate.png";
// import memo from "../../../public/images/banner/memo.png";

// const fadeRight = {
//   hidden: { opacity: 0, x: -50 },
//   visible: { opacity: 1, x: 0, transition: { duration: 1 } },
// };

// const fadeLeft = {
//   hidden: { opacity: 0, x: 50 },
//   visible: { opacity: 1, x: 0, transition: { duration: 1 } },
// };

// const textContainer = {
//   hidden: {},
//   visible: {
//     transition: {
//       staggerChildren: 0.5,
//     },
//   },
// };

// const fadeUpText = {
//   hidden: { opacity: 0, y: 30 },
//   visible: {
//     opacity: 1,
//     y: 0,
//     transition: { duration: 1, ease: "easeOut" },
//   },
// };

// function Page() {
//   const [currentIndex, setCurrentIndex] = useState(0);
//   const sections = [
//     {
//       title: "10,000+ certified stones",
//       subtitle: "Readily available in stock",
//       description:
//         "We are Grower & Manufacturer of lab grown diamonds. With our wide range having thousands of diamonds, you get one trusted source to fulfill your demand and save your time and energy.",
//       type: "image",
//       media: certified,
//       imagePosition: "left",
//     },
//     {
//       title: "100+ shapes",
//       subtitle: "Diamonds over 100+ shapes",
//       description:
//         "Our artisan workshop offers 100+ diamond shapes, crafted with elegance and precision. We also create custom shapes tailored to your vision within 30 days.",
//       type: "video",
//       media: "/images/about/Shapes video.mp4",
//       imagePosition: "right",
//     },
//     {
//       title: "30+ colors",
//       subtitle: "Diamonds over 100+ shapes",
//       description:
//         "We specialize in unique colored diamonds across 10+ fancy shades, with customized color delivery guaranteed within 20 days.",
//       type: "video",
//       media: "/images/about/Fancy Color video.mp4",
//       imagePosition: "left",
//     },
//     {
//       title: "Matching layout",
//       subtitle: "Diamonds over 100+ shapes",
//       description:
//         "Our comprehensive collection of calibrated matching layouts ranges from 10 to 99 cents, with a 0.10 MM tolerance. Whether you seek uniformity in shapes, colors, clarities, dimensions, or cent values, trust us to deliver precisely what you need.",
//       type: "video",
//       media: "/images/about/Matching Layout video.mp4",
//       imagePosition: "right",
//     },
//     {
//       title: "Star melee",
//       subtitle: "Diamonds over 100+ shapes",
//       description:
//         "We have finest small-sized lab-grown diamonds. -2 (0.006 to 0.008 carats) Star ranging from (0.009 to 0.021), Melee  (0.021 to 0.074 carats)",
//       type: "image",
//       media: melee,
//       imagePosition: "left",
//     },
//     {
//       title: "JEWELRY",
//       subtitle: "Diamonds over 100+ shapes",
//       description:
//         "We are proud to introduce our new timeless range of lab-grown diamond jewelry - featuring rings, bracelets, necklaces, and earrings.",
//       type: "video",
//       media: "/images/about/jewellery.mp4",
//       imagePosition: "right",
//     },
//   ];

//   const policy = [
//     {
//       title: "Competitive Price",
//       description:
//         "Our direct manufacturing access enables us to provide diamonds at affordable prices, ensuring our customers access cost-effective options.",
//       image: money,
//     },
//     {
//       title: "IGI & GIA Certified",
//       description:
//         "We offer an extensive selection of 3000+ IGI & GIA certified stones, providing our clients with an extensive selection of diamonds to choose from.",
//       image: certificate,
//     },
//     {
//       title: "Credit & Memo",
//       description:
//         "We facilitate credit and memo terms lasting 7 to 30 days. Our commitment to long-term partnerships is reflected in our unwavering financial support.",
//       image: memo,
//     },
//   ];

//   const sectionRefs = useRef([]);

//   useEffect(() => {
//     const allVideos = document.querySelectorAll("video");
//     allVideos.forEach((video) => {
//       video.playbackRate = 1.5;
//     });

//     setTimeout(() => {
//       AOS.init({ duration: 400, once: true });
//     }, 100);

//     const handleKeyDown = (e) => {
//       if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
//         e.preventDefault();
        
//         const elements = document.querySelectorAll('.full-section');
        
//         if (e.key === 'ArrowDown') {
//           setCurrentIndex(prev => Math.min(prev + 1, elements.length - 1));
//         } else {
//           setCurrentIndex(prev => Math.max(prev - 1, 0));
//         }
//       }
//     };

//     window.addEventListener('keydown', handleKeyDown);

//     return () => {
//       window.removeEventListener('keydown', handleKeyDown);
//       AOS.refresh();
//     };
//   }, []);

//   // Scroll effect when currentIndex changes
//   useEffect(() => {
//     const elements = document.querySelectorAll('.full-section');
//     elements[currentIndex]?.scrollIntoView({
//       behavior: 'smooth',
//       block: 'center'
//     });
//   }, [currentIndex]);

//   return (
//     <div className="bg-black pt-8 pb-8 cursor-pointer">
//       <div
//         className="full-section relative w-full h-[28vh] md:h-[90vh] xl:h-screen overflow-hidden pt-20 md:pt-0"
//       >
//         <video
//           className="absolute inset-0 w-full h-full object-cover opacity-80"
//           autoPlay
//           muted
//           loop
//           playsInline
//           preload="auto"
//         >
//           <source src="/images/about/HomepageV.mp4" type="video/mp4" />
//         </video>
//       </div>

//       {sections.map((item, index) => (
//         <section
//           key={index}
//           className={`full-section flex flex-col md:flex-row items-center justify-center px-4 h-screen ${
//             item.imagePosition === "left"
//               ? "md:flex-row-reverse"
//               : "md:flex-row"
//           }`}
//         >
//           <motion.div
//             variants={item.imagePosition === "left" ? fadeRight : fadeLeft}
//             initial="hidden"
//             whileInView="visible"
//             viewport={{ once: false, amount: 0.2 }}
//             className="w-full md:w-1/2 flex justify-center items-center"
//           >
//             {item.type === "image" ? (
//               <Image
//                 src={item.media}
//                 alt={item.title}
//                 width={600}
//                 height={400}
//                 loading="eager"
//                 priority={index === 0}
//                 className="max-h-[70vh] w-full max-w-[600px] object-contain rounded"
//                 data-aos="zoom-in-up"
//               />
//             ) : (
//               <video
//                 src={item.media}
//                 muted
//                 autoPlay
//                 playsInline
//                 preload="auto"
//                 loop={item.title !== "30+ colors"} 
//                 onEnded={(e) => {
//                   if (item.title === "30+ colors") {
//                     e.target.pause(); 
//                   }
//                 }}
//                 className="max-h-[70vh] w-full max-w-[600px] object-contain rounded"
//                 data-aos="zoom-in-up"
//               />
//             )}
//           </motion.div>

//           <motion.div
//             variants={textContainer}
//             initial="hidden"
//             whileInView="visible"
//             viewport={{ once: false, amount: 0.3 }}
//             className="w-full md:w-1/2 text-center md:text-left p-4"
//           >
//             <motion.h2
//               variants={fadeUpText}
//               className="text-white font-bold xl:text-4xl md:text-2xl text-xl px-4 uppercase font-oldstandardtt"
//             >
//               {item.title}
//             </motion.h2>
//             {item.subtitle && (
//               <motion.h4
//                 variants={fadeUpText}
//                 className="text-white font-medium xl:text-lg md:text-md text-xs px-4 mt-4 uppercase font-montserrat"
//               >
//                 {item.subtitle}
//               </motion.h4>
//             )}
//             <motion.p
//               variants={fadeUpText}
//               className="text-[#d9d9d9] font-normal xl:text-xl md:text-md text-sm px-4 mt-4 leading-extended font-montserrat"
//             >
//               {item.description}
//             </motion.p>
//           </motion.div>
//         </section>
//       ))}

//       <div 
//         className="full-section min-h-screen flex items-center justify-center py-20"
//       >
//         <div className="grid xl:grid-cols-3 md:grid-cols-3 grid-cols-1 gap-8 md:gap-12 w-full max-w-7xl mx-auto px-4">
//           {policy.map((item, index) => (
//             <section
//               key={index}
//               className="flex flex-col items-center justify-center bg-black/30 rounded-lg p-8 h-full"
//             >
//               <h2
//                 className="text-white italic xl:text-2xl md:text-xl text-lg font-migra mb-6"
//                 data-aos="fade-down"
//               >
//                 {item.title}
//               </h2>
//               <div className="relative w-40 h-40 mb-6">
//                 <Image
//                   src={item.image}
//                   alt={item.title}
//                   className="object-contain"
//                   fill
//                   sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//                   data-aos="zoom-in-up"
//                 />
//               </div>
//               <p
//                 className="text-[#d9d9d9] font-normal leading-relaxed text-center xl:text-base md:text-sm text-sm font-montserrat"
//                 data-aos="fade-up"
//               >
//                 {item.description}
//               </p>
//             </section>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// }

// export default Page;





