"use client";
import { useEffect, useState, useRef } from "react";

export default function AnimatedHeading({ 
  text, 
  className = "", 
  level = "h1",
  showUnderline = true,
  animationDelay = 0.2,
  triggerOnMount = true 
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const headingRef = useRef(null);

  useEffect(() => {
    if (triggerOnMount && !hasAnimated) {
      // Trigger animation immediately when component mounts
      const timer = setTimeout(() => {
        setIsVisible(true);
        setHasAnimated(true);
      }, 100);
      return () => clearTimeout(timer);
    }

    // Intersection Observer for scroll-triggered animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setIsVisible(true);
            setHasAnimated(true);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (headingRef.current) {
      observer.observe(headingRef.current);
    }

    return () => {
      if (headingRef.current) {
        observer.unobserve(headingRef.current);
      }
    };
  }, [triggerOnMount, hasAnimated]);

  // Reset animation when text changes
  useEffect(() => {
    setHasAnimated(false);
    setIsVisible(false);
  }, [text]);

  const animations = [
    'animate-slideInLeft',
    'animate-slideInRight', 
    'animate-slideInDown',
    'animate-slideInUp',
    'animate-slideInDiagonalTopLeft',
    'animate-slideInDiagonalBottomRight'
  ];

  const words = text.split(' ');
  
  const HeadingTag = level;

  return (
    <HeadingTag 
      ref={headingRef}
      className={`heading-elegant animate-textGlow tracking-wider font-extralight relative ${className}`}
    >
      {words.map((word, wordIndex) => {
        const animationClass = animations[wordIndex % animations.length];
        return (
          <span 
            key={`${text}-${wordIndex}`}
            className={`inline-block ${isVisible ? animationClass : ''} ${isVisible ? '' : 'opacity-0'}`}
            style={{ 
              animationDelay: isVisible ? `${wordIndex * animationDelay}s` : '0s',
              animationFillMode: 'forwards'
            }}
          >
            {word}
            {wordIndex < words.length - 1 && '\u00A0'}
          </span>
        );
      })}
      {showUnderline && (
        <div 
          className={`absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-20 h-0.5 bg-white opacity-60 ${isVisible ? 'animate-pulse' : 'opacity-0'}`} 
          style={{ animationDelay: isVisible ? `${words.length * animationDelay}s` : '0s' }}
        />
      )}
    </HeadingTag>
  );
}
