"use client";
import { useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import analytics components to avoid SSR issues
const GoogleAnalytics = dynamic(() => import('./GoogleAnalytics'), {
  ssr: false,
});

const GoogleTagManager = dynamic(() => import('./GoogleTagManager'), {
  ssr: false,
});




const ClientOnlyAnalytics = () => {
  useEffect(() => {
    // Delay analytics loading for better performance
    const timer = setTimeout(() => {
      // Analytics will be loaded after initial page load
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <GoogleTagManager />
      <GoogleAnalytics />
    </>
  );
};

export default ClientOnlyAnalytics;
