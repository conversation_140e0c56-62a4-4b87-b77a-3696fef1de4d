"use client";
import { useEffect, useState } from 'react';

/**
 * NoSSR (No Server-Side Rendering) Component
 * 
 * This component prevents server-side rendering of its children,
 * which helps avoid hydration mismatches caused by:
 * - Browser extensions (like Grammarly)
 * - Client-only APIs
 * - Dynamic content that differs between server and client
 * 
 * Usage:
 * <NoSSR>
 *   <ComponentThatShouldOnlyRenderOnClient />
 * </NoSSR>
 */
export default function NoSSR({ children }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything on the server
  if (!mounted) {
    return null;
  }

  return children;
}
