export default function Loading() {
  return (
    <div className="relative h-screen bg-gray-900">
      {/* Background placeholder */}
      <div className="absolute inset-0 bg-gray-800 animate-pulse"></div>
      
      <div className="relative pt-48 px-6 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-8 place-items-center">
        {/* Contact info placeholder */}
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-700 rounded w-3/4"></div>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-700 rounded w-48"></div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Form placeholder */}
        <div className="w-full max-w-md">
          <div className="bg-gray-300 rounded-2xl p-6 animate-pulse">
            <div className="space-y-4">
              <div>
                <div className="h-4 bg-gray-400 rounded w-24 mb-2"></div>
                <div className="h-10 bg-white rounded-full"></div>
              </div>
              <div>
                <div className="h-4 bg-gray-400 rounded w-32 mb-2"></div>
                <div className="h-10 bg-white rounded-full"></div>
              </div>
              <div>
                <div className="h-4 bg-gray-400 rounded w-20 mb-2"></div>
                <div className="h-24 bg-white rounded-md"></div>
              </div>
              <div className="text-center">
                <div className="h-10 bg-white rounded-full w-24 mx-auto"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
