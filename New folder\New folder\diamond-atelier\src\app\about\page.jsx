
import React from "react";

export const metadata = {
  title: "About Diamond Atelier - Leading Lab Grown Diamond Manufacturer",
  description: "Learn about Diamond Atelier, India's premier lab-grown diamond manufacturer. Our story, expertise, and commitment to ethical diamond production since inception.",
  keywords: [
    "about diamond atelier",
    "lab grown diamond manufacturer",
    "diamond company India",
    "ethical diamond production",
    "diamond manufacturing process",
    "certified diamond supplier"
  ],
  openGraph: {
    title: "About Diamond Atelier - Leading Lab Grown Diamond Manufacturer",
    description: "Learn about Diamond Atelier, India's premier lab-grown diamond manufacturer. Our story, expertise, and commitment to ethical diamond production.",
    images: [
      {
        url: '/images/about-og.jpg',
        width: 1200,
        height: 630,
        alt: 'About Diamond Atelier - Our Story',
      },
    ],
  },
  // Preload critical fonts for LCP optimization
  other: {
    'preload': [
      { rel: 'preload', href: '/fonts/montserrat.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
    ]
  }
};
import img1 from "../../../public/images/education/img1.png";
import img2 from "../../../public/images/education/img2.png";
import img3 from "../../../public/images/education/img3.png";
import img4 from "../../../public/images/education/img4.png";
import img5 from "../../../public/images/education/AboutLabGrown.png";
import money from "../../../public/images/banner/money.png";
import certificate from "../../../public/images/banner/certificate.png";
import memo from "../../../public/images/banner/memo.png";
import ClientAboutPage from "./ClientAboutPage";

// Server-side data preparation
async function getAboutPageData() {
  const evolution = [
    { id: 1, img: img1, text: "Diamond Seed" },
    { id: 2, img: img2, text: "Rough Stage 1" },
    { id: 3, img: img3, text: "Rough Stage 2" },
    { id: 4, img: img4, text: "Final Stage" },
  ];

  const policy = [
    {
      title: "Competitive Price",
      description:
        "Our direct manufacturing access enables us to provide diamonds at affordable prices, ensuring our customers access cost-effective options.",
      image: money,
    },
    {
      title: "IGI & GIA Certified",
      description:
        "We offer an extensive selection of 3000+ IGI & GIA certified stones, providing our clients with an extensive selection of diamonds to choose from.",
      image: certificate,
    },
    {
      title: "Credit & Memo",
      description:
        "We facilitate credit and memo terms lasting 7 to 30 days. Our commitment to long-term partnerships is reflected in our unwavering financial support.",
      image: memo,
    },
  ];

  return { evolution, policy };
}

// Server-side component
export default async function Page() {
  // Get data on server
  const { evolution, policy } = await getAboutPageData();

  return <ClientAboutPage evolution={evolution} policy={policy} />;
}
