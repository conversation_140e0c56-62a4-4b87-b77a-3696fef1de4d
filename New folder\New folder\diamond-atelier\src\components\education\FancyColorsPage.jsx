"use client";

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";

const FancyColorsPage = () => {
  const fancyColors = [
    {
      name: "Pink",
      description: "Rare and romantic, formed by plastic deformation",
      rarity: "Very Rare",
      image: "/images/Colors/pink.png",
      variations: ["Pink", "Purplish Pink", "Pink Champagne", "Brownish Pink"]
    },
    {
      name: "Blue",
      description: "Caused by boron impurities, extremely valuable",
      rarity: "Extremely Rare",
      image: "/images/Colors/blue.png",
      variations: ["Blue", "Greenish Blue", "Grayish Blue", "Violet Blue"]
    },
    {
      name: "Yellow",
      description: "Most common fancy color, caused by nitrogen",
      rarity: "Rare",
      image: "/images/Colors/yellow.png",
      variations: ["Yellow", "Greenish Yellow", "Brownish Yellow", "Orange Yellow"]
    },
    {
      name: "Green",
      description: "Natural radiation exposure creates this color",
      rarity: "Very Rare",
      image: "/images/Colors/green.png",
      variations: ["Green", "Yellowish Green", "Bluish Green", "Grayish Green"]
    },
    {
      name: "Orange",
      description: "Combination of nitrogen and structural defects",
      rarity: "Very Rare",
      image: "/images/Colors/Orange.png",
      variations: ["Orange", "Brownish Orange", "Yellow Orange", "Pinkish Orange"]
    },
    {
      name: "Red",
      description: "Rarest of all diamond colors",
      rarity: "Extremely Rare",
      image: "/images/Colors/red.png",
      variations: ["Red", "Purplish Red", "Brownish Red", "Orange Red"]
    },
    {
      name: "Purple",
      description: "Caused by hydrogen and boron impurities",
      rarity: "Very Rare",
      image: "/images/Colors/purple.png",
      variations: ["Purple", "Reddish Purple", "Pinkish Purple", "Violet Purple"]
    },
    {
      name: "Brown",
      description: "Also known as champagne or cognac diamonds",
      rarity: "Common",
      image: "/images/Colors/brown.png",
      variations: ["Brown", "Yellowish Brown", "Reddish Brown", "Grayish Brown"]
    },
    {
      name: "Black",
      description: "Opaque diamonds with graphite inclusions",
      rarity: "Uncommon",
      image: "/images/Colors/black.png",
      variations: ["Black", "Grayish Black", "Brownish Black"]
    },
    {
      name: "Gray",
      description: "Subtle and sophisticated neutral tone",
      rarity: "Uncommon",
      image: "/images/Colors/grey.png",
      variations: ["Gray", "Bluish Gray", "Greenish Gray", "Violet Gray"]
    },
    {
      name: "Violet",
      description: "Rare purple-blue combination",
      rarity: "Extremely Rare",
      image: "/images/Colors/violet.png",
      variations: ["Violet", "Bluish Violet", "Reddish Violet", "Grayish Violet"]
    },
    {
      name: "White",
      description: "Colorless diamonds with exceptional purity",
      rarity: "Common",
      image: "/images/Colors/white.png",
      variations: ["White", "Off White", "Milky White"]
    },
    {
      name: "Olive",
      description: "Unique greenish-yellow hue with earthy tones",
      rarity: "Rare",
      image: "/images/Colors/olive.png",
      variations: ["Olive", "Brownish Olive", "Grayish Olive", "Yellowish Olive"]
    }
  ];

  const intensityGrades = [
    { grade: "Faint", description: "Barely perceptible color" },
    { grade: "Very Light", description: "Subtle color presence" },
    { grade: "Light", description: "Noticeable but light color" },
    { grade: "Fancy Light", description: "Clear color visibility" },
    { grade: "Fancy", description: "Good color saturation" },
    { grade: "Fancy Intense", description: "Strong color saturation" },
    { grade: "Fancy Vivid", description: "Maximum color saturation" },
    { grade: "Fancy Deep", description: "Deep, rich color tone" }
  ];

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-black"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-light text-white mb-8 tracking-[0.2em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              FANCY COLORED DIAMONDS
            </h1>
            <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base md:text-lg text-white/90 mb-6 font-light max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Discover the extraordinary world of fancy colored diamonds - nature's rarest and most captivating gemstones
            </p>
          </motion.div>
          <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl mb-6">
              <Image
                src="/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png"
                alt="Collection of fancy colored diamonds showing various colors and cuts"
                fill
                className="object-contain bg-black to-gray-200"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                priority
              />
            </div>
        </div>
        
      </section>
  {/*  */}
      {/* Introduction Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-8"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              THE RARITY OF COLOR
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base md:text-lg text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Less than 0.1% of all diamonds exhibit natural fancy colors. These extraordinary gems derive their hues from trace elements, structural anomalies, or radiation exposure during their formation deep within the Earth.
            </p>
            <div className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-4xl mx-auto">
              <p className="text-sm text-white/80 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                Each colored diamond tells a unique geological story, making them among the most sought-after and valuable gemstones in the world.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Diamond Collection */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
          
            <p className="text-sm text-white/70 italic font-light tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Discover the extraordinary world of fancy colored diamonds - nature's rarest and most captivating gemstones
            </p>
          </motion.div>
        </div>
      </section>

      {/* Color Grid Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              SPECTRUM OF COLORS
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 transform group-hover:scale-105 transition-all duration-500">
            {fancyColors.map((color, index) => (
              <motion.div
                key={color.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-full h-40 rounded-lg mb-4 flex items-center justify-center relative">
                  <Image
                    src={color.image}
                    alt={`${color.name} Diamond`}
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />
                </div>
                                {/* <h3 className="text-lg font-light text-white mb-2 tracking-wide uppercase" */}
                    <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-4"></div>
                <h3 className="text-white text-sm font-extralight tracking-[0.2em] uppercase group-hover:text-gray-200 group-hover:tracking-[0.3em] transition-all duration-500 relative" style={{ fontFamily: 'Times New Roman, serif' }}>
                  {color.name}
                </h3>
                <div className="h-px w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-4"></div>
                <p className="text-xs text-white/70 font-light leading-relaxed tracking-wide mb-4" style={{ fontFamily: 'Times New Roman, serif' }}>
                  {color.description}
                </p>

                {/* Color Variations */}
                <div className="mt-4">
                  <h4 className="text-xs font-light text-white/80 mb-2 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                    Color Variations
                  </h4>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {color.variations.map((variation, idx) => (
                      <div
                        key={idx}
                        className="group relative"
                      >
                        <button
                          className={`w-8 h-8 rounded-full border-2 border-white/30 hover:border-white/60 transition-all duration-300 hover:scale-110 shadow-lg hover:shadow-xl bg-gradient-to-br ${getVariationColor(variation)}`}
                          title={variation}
                        />
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                          {variation}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Intensity Grades Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              COLOR INTENSITY GRADES
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              The intensity of color in fancy diamonds is graded on a scale that determines their rarity and value.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {intensityGrades.map((grade, index) => (
              <motion.div
                key={grade.grade}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-black/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-white/40 transition-all duration-500"
              >
                <h3 className="text-base font-light text-white mb-4 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                  {grade.grade}
                </h3>
                <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mb-4"></div>
                <p className="text-xs text-white/80 font-light leading-relaxed tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
                  {grade.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Formation and Rarity Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase text-center" style={{ fontFamily: 'Times New Roman, serif' }}>
              FORMATION
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
          </motion.div>

          <div className="flex justify-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-2xl w-full"
            >
              <h3 className="text-xl font-light text-white mb-6 tracking-wide uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
                How Colors Form
              </h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="text-sm font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>Trace Elements</h4>
                    <p className="text-xs text-white/80 font-light leading-relaxed" style={{ fontFamily: 'Times New Roman, serif' }}>
                      Boron creates blue, nitrogen produces yellow, and hydrogen can cause purple hues.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="text-sm font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>Structural Defects</h4>
                    <p className="text-xs text-white/80 font-light leading-relaxed" style={{ fontFamily: 'Times New Roman, serif' }}>
                      Plastic deformation during formation creates pink and red diamonds.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="text-sm font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>Natural Radiation</h4>
                    <p className="text-xs text-white/80 font-light leading-relaxed" style={{ fontFamily: 'Times New Roman, serif' }}>
                      Exposure to radioactive elements over millions of years creates green diamonds.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>


          </div>
        </div>
      </section>

      {/* Investment Value Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-6 tracking-[0.15em] uppercase" style={{ fontFamily: 'Times New Roman, serif' }}>
              INVESTMENT POTENTIAL
            </h2>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>
            <p className="text-base text-white/90 font-light leading-relaxed max-w-4xl mx-auto tracking-wide" style={{ fontFamily: 'Times New Roman, serif' }}>
              Fancy colored diamonds have shown remarkable appreciation in value, often outperforming traditional investments.
            </p>
          </motion.div>

          <div className="bg-black/20 backdrop-blur-sm rounded-lg p-8 border border-white/20 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-2xl font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>300%+</div>
                <p className="text-xs text-white/80 font-light" style={{ fontFamily: 'Times New Roman, serif' }}>
                  Average appreciation over 10 years for top-grade pink diamonds
                </p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="text-2xl font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>Limited</div>
                <p className="text-xs text-white/80 font-light" style={{ fontFamily: 'Times New Roman, serif' }}>
                  Supply with major mines like Argyle permanently closed
                </p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <div className="text-2xl font-light text-white mb-2" style={{ fontFamily: 'Times New Roman, serif' }}>Growing</div>
                <p className="text-xs text-white/80 font-light" style={{ fontFamily: 'Times New Roman, serif' }}>
                  Global demand from collectors and investors
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};


const getVariationColor = (variation) => {
  const colorMap = {
    // Pink variations
    "Pink": "from-pink-300 to-pink-500",
    "Purplish Pink": "from-pink-400 to-purple-400",
    "Pink Champagne": "from-pink-200 to-amber-200",
    "Brownish Pink": "from-pink-400 to-amber-600",

    // Blue variations
    "Blue": "from-blue-400 to-blue-600",
    "Greenish Blue": "from-blue-400 to-teal-400",
    "Grayish Blue": "from-blue-400 to-gray-500",
    "Violet Blue": "from-blue-400 to-violet-500",

    // Yellow variations
    "Yellow": "from-yellow-300 to-yellow-500",
    "Greenish Yellow": "from-yellow-400 to-green-400",
    "Brownish Yellow": "from-yellow-400 to-amber-600",
    "Orange Yellow": "from-yellow-400 to-orange-400",

    // Green variations
    "Green": "from-green-400 to-green-600",
    "Yellowish Green": "from-green-400 to-yellow-400",
    "Bluish Green": "from-green-400 to-teal-500",
    "Grayish Green": "from-green-400 to-gray-500",

    // Orange variations
    "Orange": "from-orange-400 to-orange-600",
    "Brownish Orange": "from-orange-400 to-amber-700",
    "Yellow Orange": "from-orange-300 to-yellow-400",
    "Pinkish Orange": "from-orange-400 to-pink-400",

    // Red variations
    "Red": "from-red-500 to-red-700",
    "Purplish Red": "from-red-500 to-purple-500",
    "Brownish Red": "from-red-500 to-amber-700",
    "Orange Red": "from-red-500 to-orange-500",

    // Purple variations
    "Purple": "from-purple-400 to-purple-600",
    "Reddish Purple": "from-purple-500 to-red-500",
    "Pinkish Purple": "from-purple-400 to-pink-400",
    "Violet Purple": "from-purple-400 to-violet-500",

    // Brown variations
    "Brown": "from-amber-600 to-amber-800",
    "Yellowish Brown": "from-amber-500 to-yellow-600",
    "Reddish Brown": "from-amber-700 to-red-800",
    "Grayish Brown": "from-amber-600 to-gray-600",

    // Black variations
    "Black": "from-gray-800 to-black",
    "Grayish Black": "from-gray-700 to-black",
    "Brownish Black": "from-gray-800 to-amber-900",

    // Gray variations
    "Gray": "from-gray-400 to-gray-600",
    "Bluish Gray": "from-gray-400 to-blue-500",
    "Greenish Gray": "from-gray-400 to-green-500",
    "Violet Gray": "from-gray-400 to-violet-500",

    // Violet variations
    "Violet": "from-violet-400 to-violet-600",
    "Bluish Violet": "from-violet-400 to-blue-500",
    "Reddish Violet": "from-violet-500 to-red-500",
    "Grayish Violet": "from-violet-400 to-gray-500",

    // White variations
    "White": "from-white to-gray-100",
    "Off White": "from-gray-100 to-gray-200",
    "Milky White": "from-white to-blue-50",

    // Olive variations
    "Olive": "from-green-600 to-yellow-600",
    "Brownish Olive": "from-green-600 to-amber-700",
    "Grayish Olive": "from-green-600 to-gray-600",
    "Yellowish Olive": "from-green-500 to-yellow-500"
  };

  return colorMap[variation] || "from-gray-300 to-gray-500";
};

const getColorGradient = (colorName) => {
  const gradients = {
    Pink: "from-pink-300 to-pink-600",
    Blue: "from-blue-300 to-blue-700",
    Yellow: "from-yellow-300 to-yellow-600",
    Green: "from-green-300 to-green-600",
    Orange: "from-orange-300 to-orange-600",
    Red: "from-red-400 to-red-700",
    Purple: "from-purple-300 to-purple-600",
    Brown: "from-amber-600 to-amber-800",
    Black: "from-gray-800 to-black",
    Gray: "from-gray-400 to-gray-600",
    Violet: "from-violet-300 to-violet-600",
    White: "from-white to-gray-100",
    Olive: "from-green-600 to-yellow-600"
  };
  return gradients[colorName] || "from-white to-gray-300";
};

export default FancyColorsPage;
