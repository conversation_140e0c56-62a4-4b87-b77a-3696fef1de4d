"use client";
import { FaInstagram, FaFacebook, FaLinkedinIn, FaPhone, FaEnvelope, FaMapMarkerAlt, FaGem } from "react-icons/fa";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import ClientOnly from "../../components/ClientOnly";

function Footer() {
  
  const IconWrapper = ({ children }) => (
  <span style={{ transform: "scaleX(1)" }}>{children}</span>
);

  const quickLinks = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Contact", href: "/contact" },
    { name: "Careers", href: "/careers" },
    { name: "Calibration", href: "/matching-other-layout" },

    { name: "webmail", href: "https://sg2plzcpnl506701.prod.sin2.secureserver.net:2096/webmaillogout.cgi" },
  ];

  const educationLinks = [
    
    { name: "Why Lab-Grown", href: "/education/whyLab" },
    { name: "<PERSON> Myths", href: "/education/myth" },
    { name:"Credit-Memo Terms", href: "/credit-term" },
  ];

  // const contactInfo = [
  //   { icon: <FaPhone />, text: "+91 99099 60024", href: "tel:+919909960024" },
  //   // { icon: <FaEnvelope />, text: "<EMAIL>", href: "mailto:<EMAIL>" },
  //   { icon: <FaMapMarkerAlt />, text: "38 West 48th street, 5th floor, New York, NY 10036", href: "https://maps.app.goo.gl/o9iC98Z74vE9Uc5d6" },
  // ];

const contactInfo = [
  {
    icon: (
      <IconWrapper>
        <FaPhone />
      </IconWrapper>
    ),
    text: "+91 9909960024",
    href: "tel:+919909960024",
  },
  {
    icon: <FaMapMarkerAlt />,
    text: "38 West 48th street, 5th floor, New York, NY 10036",
    href: "https://maps.app.goo.gl/o9iC98Z74vE9Uc5d6",
  },
];
  const socialLinks = [
    { icon: <FaFacebook />, href: "https://www.facebook.com/diamondatelier.inc", name: "Facebook" },
    { icon: <FaInstagram />, href: "https://www.instagram.com/diamondatelier.inc/", name: "Instagram" },
    { icon: <FaLinkedinIn />, href: "https://www.linkedin.com/company/diamondatelierinc/", name: "LinkedIn" },
  ];

  return (
    <ClientOnly fallback={
      <footer
        className="bg-black text-white relative overflow-hidden"
        style={{
          backgroundColor: '#000000',
          background: '#000000', // Ensure no gradient
          color: '#ffffff',
          minHeight: '400px',
          padding: '4rem 1rem'
        }}
      >
        <div className="max-w-7xl mx-auto text-center">
          <p>Loading footer...</p>
        </div>
      </footer>
    }>
      <footer
        className="bg-black text-white relative overflow-hidden footer-dark-bg"
        style={{
          backgroundColor: '#000000', // Pure black background
          background: '#000000', // Ensure no gradient
          color: '#ffffff', // Explicit white text
          minHeight: '400px' // Ensure footer has minimum height
        }}
      >
      {/* Background Pattern - Removed for solid black */}

      {/* Top Border - Solid white line instead of gradient */}
      <div className="h-px bg-white"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          
          {/* Brand Section */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-1"
          >
            <div className="flex items-center mb-6">
              <Image
                src="/images/logo/Diamondlogo.png"
                alt="Diamond Atelier Logo"
                width={200}
                height={80}
                className="object-contain"
                style={{ filter: 'brightness(0) invert(1)' }} // Makes the logo white
              />
            </div>
            <p className="text-gray-400 leading-relaxed mb-6 font-montserrat">
              Crafting exceptional lab-grown diamonds with cutting-edge technology and timeless beauty.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-10 h-10 bg-gradient-to-br from-gray-700 to-gray-800 rounded-full flex items-center justify-center hover:from-gray-600 hover:to-gray-700 transition-all duration-300"
                  aria-label={social.name}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="lg:col-span-1"
          >
            <h4 className="text-lg font-semibold mb-6 relative font-montserrat">
              Quick Links
              <div className="absolute -bottom-2 left-0 w-12 h-0.5 bg-gradient-to-r from-white to-gray-400"></div>
            </h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group font-montserrat"
                  >
                    <span className="w-0 group-hover:w-2 h-0.5 bg-white transition-all duration-300 mr-0 group-hover:mr-3"></span>
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Education */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-1"
          >
            <h4 className="text-lg font-semibold mb-6 relative font-montserrat">
              Education
              <div className="absolute -bottom-2 left-0 w-12 h-0.5 bg-gradient-to-r from-white to-gray-400"></div>
            </h4>
            <ul className="space-y-3">
              {educationLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group font-montserrat"
                  >
                    <span className="w-0 group-hover:w-2 h-0.5 bg-white transition-all duration-300 mr-0 group-hover:mr-3"></span>
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-1"
          >
            <h4 className="text-lg font-semibold mb-6 relative font-montserrat">
              Contact Info
              <div className="absolute -bottom-2 left-0 w-12 h-0.5 bg-gradient-to-r from-white to-gray-400"></div>
            </h4>
            <ul className="space-y-4">
              {contactInfo.map((contact, index) => (
                <li key={index}>
                  <a
                    href={contact.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-start space-x-3 text-gray-400 hover:text-white transition-colors duration-300 group font-montserrat"
                  >
                    <div className="w-5 h-5 mt-0.5 text-white group-hover:scale-110 transition-transform duration-300">
                      {contact.icon}
                    </div>
                    <span className="text-sm leading-relaxed">{contact.text}</span>
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 pt-8 border-t border-gray-800"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-500 text-sm font-montserrat">
              © 2024 Diamond Atelier. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link href="#" className="text-gray-500 hover:text-white text-sm transition-colors duration-300 font-montserrat">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-500 hover:text-white text-sm transition-colors duration-300 font-montserrat">
                Terms of Service
              </Link>

            </div>
          </div>
        </motion.div>
      </div>
    </footer>
    </ClientOnly>
  );
}

export default Footer;
