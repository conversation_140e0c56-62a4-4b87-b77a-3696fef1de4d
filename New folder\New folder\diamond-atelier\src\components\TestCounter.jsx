"use client";
import React, { useState, useEffect, useRef } from 'react';

const TestCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    // Create unique key for this counter
    const counterKey = `counter_${targetNumber}_${suffix.replace(/[^a-zA-Z0-9]/g, '_')}`;
    const pageLoadKey = 'page_load_timestamp';

    // Get current page load time
    const currentPageLoad = Date.now();
    const lastPageLoad = localStorage.getItem(pageLoadKey);

    // Check if this is a fresh page load (different timestamp)
    const isPageRefresh = !lastPageLoad || (currentPageLoad - parseInt(lastPageLoad) > 1000);

    if (isPageRefresh) {
      // Clear counter data and set new page load time
      localStorage.removeItem(counterKey);
      localStorage.setItem(pageLoadKey, currentPageLoad.toString());
    }

    // Check if already animated in this page session
    if (localStorage.getItem(counterKey) && !isPageRefresh) {
      // Show final number immediately - already animated
      setCount(targetNumber);
      setHasAnimated(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setHasAnimated(true);

          // Mark as animated in localStorage
          localStorage.setItem(counterKey, 'completed');

          // Start counting animation
          let startTime = Date.now();
          const endTime = startTime + duration;

          const updateCount = () => {
            const now = Date.now();
            const progress = Math.min((now - startTime) / duration, 1);
            const currentCount = Math.floor(progress * targetNumber);

            setCount(currentCount);

            if (now < endTime) {
              requestAnimationFrame(updateCount);
            } else {
              setCount(targetNumber); // Final exact value
            }
          };

          requestAnimationFrame(updateCount);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [targetNumber, duration, suffix, hasAnimated]);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit'
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

export default TestCounter;
