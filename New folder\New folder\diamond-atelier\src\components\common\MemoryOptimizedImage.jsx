"use client";
import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

/**
 * Memory-Optimized Image Component
 * 
 * Designed specifically to reduce memory usage:
 * - Minimal state management
 * - Efficient intersection observer
 * - Automatic cleanup
 * - No complex animations
 */
const MemoryOptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 60, // Lower default quality for memory savings
  ...props
}) => {
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    if (priority || isInView) return;

    // Create observer only when needed
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          // Immediately disconnect to free memory
          observerRef.current?.disconnect();
          observerRef.current = null;
        }
      },
      {
        rootMargin: '100px', // Larger margin for smoother loading
        threshold: 0.01 // Very low threshold
      }
    );

    if (imgRef.current && observerRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, [priority, isInView]);

  // Simple placeholder - no animations to save memory
  if (!isInView && !priority) {
    return (
      <div 
        ref={imgRef}
        className={`bg-gray-800 ${className}`}
        style={{ width, height }}
      />
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      quality={quality}
      priority={priority}
      className={className}
      loading={priority ? "eager" : "lazy"}
      {...props}
    />
  );
};

export default MemoryOptimizedImage;
