#!/usr/bin/env node

/**
 * Add Lazy Loading Script
 * 
 * This script automatically adds lazy loading to all images in the project
 * Run with: node scripts/add-lazy-loading.js
 */

const fs = require('fs');
const path = require('path');

// Files to update with lazy loading
const filesToUpdate = [
  'src/components/education/ClientEducationPage.jsx',
  'src/components/education/Evolution.jsx',
  'src/layout/app/header.jsx',
  'src/app/about/ClientAboutPage.jsx',
  'src/app/careers/ClientCareersPage.jsx',
  'src/app/contact/ClientContactPage.jsx'
];

// Patterns to find and replace
const patterns = [
  {
    // Regular img tags without loading attribute
    find: /<img([^>]*?)src="([^"]*)"([^>]*?)(?!.*loading=)([^>]*?)>/g,
    replace: '<img$1src="$2"$3 loading="lazy"$4>'
  },
  {
    // Next.js Image components without loading attribute
    find: /<Image([^>]*?)src={([^}]*)}([^>]*?)(?!.*loading=)([^>]*?)>/g,
    replace: '<Image$1src={$2}$3 loading="lazy"$4>'
  },
  {
    // Images with loading="eager" that should be lazy (except first few)
    find: /loading="eager"/g,
    replace: 'loading="lazy"'
  }
];

function updateLazyLoading() {
  console.log('🖼️  Adding lazy loading to images...\n');
  
  let totalUpdates = 0;
  
  filesToUpdate.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let fileUpdates = 0;
    const originalContent = content;
    
    // Apply all patterns
    patterns.forEach((pattern, index) => {
      const matches = content.match(pattern.find);
      if (matches) {
        content = content.replace(pattern.find, pattern.replace);
        fileUpdates += matches.length;
        console.log(`✅ Pattern ${index + 1}: Updated ${matches.length} images in ${filePath}`);
      }
    });
    
    // Special handling for priority images (first 2 images should load eagerly)
    if (filePath.includes('ClientHomePage.jsx')) {
      // Keep first 2 images as priority
      content = content.replace(
        /(<Image[^>]*?loading="lazy"[^>]*?>)/g,
        (match, p1, offset) => {
          const beforeMatch = content.substring(0, offset);
          const imageCount = (beforeMatch.match(/<Image/g) || []).length;
          
          if (imageCount < 2) {
            return match.replace('loading="lazy"', 'loading="eager" priority={true}');
          }
          return match;
        }
      );
    }
    
    if (content !== originalContent) {
      // Create backup
      fs.writeFileSync(`${filePath}.backup`, originalContent);
      
      // Write updated content
      fs.writeFileSync(filePath, content);
      totalUpdates += fileUpdates;
      console.log(`📝 Updated ${filePath} (${fileUpdates} changes)\n`);
    } else {
      console.log(`ℹ️  No updates needed for ${filePath}\n`);
    }
  });
  
  console.log(`🎉 Total updates: ${totalUpdates}`);
  console.log('💾 Backup files created with .backup extension');
}

function generateLazyLoadingReport() {
  console.log('\n📊 Lazy Loading Implementation Report:\n');
  
  const components = [
    { name: 'OptimizedImage', status: '✅ Created', description: 'Advanced lazy loading with WebP/AVIF support' },
    { name: 'LazyImage', status: '✅ Created', description: 'Simple lazy loading for regular img tags' },
    { name: 'useLazyLoading', status: '✅ Created', description: 'Custom hook for lazy loading' },
    { name: 'Anatomy Component', status: '✅ Updated', description: 'All images now use OptimizedImage' },
    { name: 'Shapes Component', status: '✅ Updated', description: 'All images now use OptimizedImage' },
    { name: 'Method Component', status: '✅ Updated', description: 'All images now use OptimizedImage' },
    { name: 'Homepage Images', status: '✅ Updated', description: 'Priority loading for above-fold images' }
  ];
  
  components.forEach(comp => {
    console.log(`${comp.status} ${comp.name}: ${comp.description}`);
  });
  
  console.log('\n🚀 Performance Benefits:');
  console.log('• Images load only when needed (viewport intersection)');
  console.log('• Reduced initial page load time');
  console.log('• Better mobile performance');
  console.log('• Smooth loading animations');
  console.log('• Automatic error handling');
  
  console.log('\n📱 Mobile Optimizations:');
  console.log('• 50px margin before loading (preload for smooth scrolling)');
  console.log('• Loading placeholders prevent layout shift');
  console.log('• Priority loading for above-fold content');
  console.log('• Intersection Observer API for efficient detection');
}

function showUsageExamples() {
  console.log('\n💡 Usage Examples:\n');
  
  console.log('1. OptimizedImage (Recommended):');
  console.log(`
import OptimizedImage from '@/components/common/OptimizedImage';

<OptimizedImage
  src="/images/diamond.jpg"
  alt="Beautiful diamond"
  width={400}
  height={300}
  priority={false} // Set to true for above-fold images
  className="rounded-lg"
/>
`);
  
  console.log('2. LazyImage (Simple):');
  console.log(`
import { LazyImage } from '@/hooks/useLazyLoading';

<LazyImage
  src="/images/diamond.jpg"
  alt="Beautiful diamond"
  className="w-full h-auto"
  width={400}
  height={300}
/>
`);
  
  console.log('3. Custom Hook:');
  console.log(`
import { useLazyLoading } from '@/hooks/useLazyLoading';

const { ref, isInView, isLoaded } = useLazyLoading();

<div ref={ref}>
  {isInView && <img src="/images/diamond.jpg" alt="Diamond" />}
</div>
`);
}

// Main execution
function main() {
  console.clear();
  console.log('🖼️  Diamond Atelier - Lazy Loading Implementation\n');
  
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  readline.question('Add lazy loading to all images? (y/N): ', (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      updateLazyLoading();
      generateLazyLoadingReport();
      showUsageExamples();
      
      console.log('\n🚀 Next Steps:');
      console.log('1. Test your website: npm run dev');
      console.log('2. Check Network tab in DevTools');
      console.log('3. Scroll slowly to see lazy loading in action');
      console.log('4. Test on mobile devices');
      console.log('5. Run Lighthouse audit for performance improvements');
    } else {
      console.log('Operation cancelled.');
    }
    
    readline.close();
  });
}

if (require.main === module) {
  main();
}

module.exports = { updateLazyLoading, patterns };
