@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=EB+Garamond:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800&family=Arimo:wght@400;500;600;700&family=Migra:wght@400;500;600;700&family=Old+Standard+TT:wght@400;500;600&family=Open+Sans:wght@400;500;600;700&display=swap");

.font-ebgaramond {
  font-family: "EB Garamond", serif;
}

.font-migra {
  font-family: "Migra", serif;
}

.font-oldstandardtt {
  font-family: "Old Standard TT", serif;
}

.font-opensans {
  font-family: "Open Sans", sans-serif;
}

.font-montserrat {
  font-family: "Montserrat", sans-serif;
}

.font-arimo {
  font-family: "Arimo", sans-serif;
}

@font-face {
  font-family: "Montserrat Classic";
  src: url("/fonts/MontserratClassic.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for enhanced navbar */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Enhanced gradient backgrounds */
.bg-gradient-quality {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-solutions {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-innovation {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-responsibility {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
