"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/page",{

/***/ "(app-pages-browser)/./src/components/common/SimpleHome.jsx":
/*!**********************************************!*\
  !*** ./src/components/common/SimpleHome.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AnimatedHeading */ \"(app-pages-browser)/./src/components/common/AnimatedHeading.jsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sections = [\n    // {\n    //   id: \"hero\",\n    //   title: \"Exquisite Lab Grown Diamonds\",\n    //   subtitle: \"Every Diamond Tells a Story\",\n    //   description:\n    //     \"Ethically sourced, certified diamonds with unparalleled quality and brilliance.\",\n    //   image: \"/image/tache_diamond_rough_cut.png\",\n    //   cta: \"Discover Our Collection\",\n    // },\n    {\n        id: \"shapes\",\n        title: \"100+ shapes\",\n        subtitle: \"Diamonds over 100+ shapes\",\n        description: \"Our artisan workshop offers 100+ diamond shapes, crafted with elegance and precision. We also create custom shapes tailored to your vision within 30 days.\",\n        image: \"/image/diamond-cuts.jpg\",\n        // video: \"/images/about/Shapes video.mp4\",\n        cta: \"Discover Shapes\"\n    },\n    {\n        id: \"matching-layout\",\n        title: \"Matching layout\",\n        subtitle: \"Calibrated consistency from 10–99 cents\",\n        description: \"Our comprehensive collection of calibrated matching layouts ranges from 10 to 99 cents, with a 0.10 MM tolerance. Whether you seek uniformity in shapes, colors, clarities, dimensions, or cent values, trust us to deliver precisely what you need.\",\n        image: \"/image/shaping_phase_tache-768x768.png\",\n        video: \"/images/about/Matching Layout video.mp4\",\n        cta: \"View Our Process\"\n    },\n    {\n        id: \"colors\",\n        title: \"30+ colors\",\n        subtitle: \"Nature's Spectrum, Perfected Diamonds colors over 30+ colors\",\n        description: \"We specialize in unique colored diamonds across 10+ fancy shades, with customized color delivery guaranteed within 20 days.\",\n        image: \"/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png\",\n        video: null,\n        cta: \"Explore Colors\"\n    }\n];\nfunction SimpleHome() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHome.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SimpleHome.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"full-section relative w-full h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png\",\n                        alt: \"Diamond Atelier Homepage Banner\",\n                        fill: true,\n                        className: \"absolute inset-0 w-full h-full object-cover\",\n                        priority: true,\n                        quality: 90\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full opacity-60\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight,\n                                    scale: 0\n                                },\n                                animate: {\n                                    y: [\n                                        null,\n                                        -100\n                                    ],\n                                    opacity: [\n                                        0.6,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 3,\n                                    ease: \"easeOut\"\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col items-center justify-center z-20 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat\",\n                                            style: {\n                                                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',\n                                                letterSpacing: \"0.1em\"\n                                            },\n                                            children: \"DIAMONDS THAT DESERVE YOU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 0.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center max-w-4xl mx-auto mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.3em] leading-relaxed font-montserrat\",\n                                        children: [\n                                            \" \",\n                                            \"10,000+ Certified Stones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg lg:text-xl text-black/70 font-light tracking-wide leading-relaxed font-montserrat\",\n                                        children: \"Where precision meets perfection in every facet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 1,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/shapes\",\n                                        className: \"group relative px-8 py-3 bg-Black border border-white/30 text-Black hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"EXPlORE SHAPES\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-Black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"https://inventory.diamondatelier.in/\",\n                                        className: \"group relative px-8 py-3 bg-black/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: \"VIEW INVENTORY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            delay: 1.5\n                        },\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                        onClick: ()=>setCurrentIndex(1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs tracking-widest mb-2 font-light\",\n                                    children: \"SCROLL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 bg-gradient-to-br from-gray-50 to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center justify-between gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-black\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                children: sections[0].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: sections[0].title,\n                                            level: \"h1\",\n                                            className: \"text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat\",\n                                            showUnderline: false,\n                                            animationDelay: 0.2,\n                                            triggerOnMount: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat\",\n                                            children: sections[0].description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/shapes\",\n                                            className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                            children: sections[0].cta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-64 mb-6 overflow-hidden rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: sections[0].image,\n                                                        alt: sections[0].title,\n                                                        fill: true,\n                                                        className: \"object-cover\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl te font-light mb-4 font-montserrat\",\n                                                            children: \"Premium Diamond Cuts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 font-light font-montserrat\",\n                                                            children: \"Masterfully crafted for maximum brilliance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                        children: sections[0].subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    text: sections[0].title,\n                                    level: \"h1\",\n                                    className: \"text-4xl sm:text-5xl mb-8 font-light leading-tight font-montserrat\",\n                                    showUnderline: false,\n                                    animationDelay: 0.2,\n                                    triggerOnMount: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-xl p-6 max-w-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 mb-4 overflow-hidden rounded-xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: sections[0].image,\n                                                    alt: sections[0].title,\n                                                    fill: true,\n                                                    className: \"object-cover\",\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-light mb-2 font-montserrat\",\n                                                children: \"Premium Diamond Cuts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm font-light font-montserrat\",\n                                                children: \"Masterfully crafted for maximum brilliance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                    children: sections[0].description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/shapes\",\n                                    className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                    children: sections[0].cta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            sections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 \".concat(index % 2 === 0 ? 'bg-white' : 'bg-gray-50'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:grid lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-black \".concat(index % 2 === 0 ? 'order-2' : 'order-1'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                    children: section.subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl xl:text-5xl mb-6 font-light leading-tight font-montserrat\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 font-montserrat\",\n                                                children: section.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                        children: section.cta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                        children: \"Learn More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(index % 2 === 0 ? 'order-1' : 'order-2', \" flex justify-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative max-w-md w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden rounded-2xl shadow-2xl mx-auto \".concat(section.id === 'colors' ? 'h-60 w-64' : 'h-72 w-80'),\n                                                    children: [\n                                                        section.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            autoPlay: true,\n                                                            loop: true,\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            className: \"w-full h-full object-cover\",\n                                                            ref: (video)=>{\n                                                                if (video) {\n                                                                    video.playbackRate = 2.5;\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                    src: section.video,\n                                                                    type: \"video/mp4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: section.image,\n                                                                    alt: section.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-xl p-4 max-w-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-base font-medium mb-2 font-montserrat\",\n                                                            children: [\n                                                                section.id === 'shapes' && '100+ Diamond Shapes',\n                                                                section.id === 'matching-layout' && 'Calibrated Precision',\n                                                                section.id === 'colors' && '30+ Fancy Colors'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 font-light font-montserrat\",\n                                                            children: [\n                                                                section.id === 'shapes' && 'Custom shapes in 30 days',\n                                                                section.id === 'matching-layout' && '0.10 MM tolerance guarantee',\n                                                                section.id === 'colors' && 'Custom colors in 20 days'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden text-center text-black\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                            children: section.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl mb-6 font-light leading-tight font-montserrat\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden rounded-2xl shadow-xl mx-auto \".concat(section.id === 'colors' ? 'h-40 w-52' : 'h-48 w-64'),\n                                            children: [\n                                                section.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                    autoPlay: true,\n                                                    loop: true,\n                                                    muted: true,\n                                                    playsInline: true,\n                                                    className: \"w-full h-full object-cover\",\n                                                    ref: (video)=>{\n                                                        if (video) {\n                                                            video.playbackRate = 4.0;\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                            src: section.video,\n                                                            type: \"video/mp4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: section.image,\n                                                    alt: section.title,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-4 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                children: section.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                children: \"Learn More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                }, section.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative w-full h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"url('/image/Expert.jpg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/40 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 drop-shadow-lg font-montserrat\",\n                                        children: \"Expert Eyes on Every Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-black mb-8 leading-relaxed drop-shadow-md font-montserrat\",\n                                        children: \"Our in-house gemologists personally inspect and verify every lab-grown diamond. You'll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHome, \"2oCXE2WiAW05zG4oFnHELxlAGiA=\");\n_c = SimpleHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/SimpleHome.jsx\n"));

/***/ })

});