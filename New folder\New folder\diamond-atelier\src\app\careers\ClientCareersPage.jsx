"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { MapPin, GraduationCap, Clock, DollarSign, Users, Award, Send, CheckCircle } from "lucide-react";


const staticJobs = [
  {
    id: 1,
    title: "Sales Expert",
    location: "Surat (Piplod)",
    graduation: "B.com",
    experience: "1-3 Yrs",
    salary: "50k"
  },
  {
    id: 2,
    title: "Accountant",
    location: "Surat (Varachha)",
    graduation: "B.com",
    experience: "0-2 Yrs",
    salary: "50k"
  }
];

const CareersPage = () => {
  const [selectedJob, setSelectedJob] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    mobileNumber: "",
    jobRole: "",
    referralName: "",
    resume: null
  });

  // Static data - no API calls for now

  const handleShowJob = (job) => {
    setSelectedJob(job);
    setFormData(prev => ({ ...prev, jobRole: job.title }));
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedJob(null);
    setShowSuccessPopup(false);
    setErrors({});
    // Reset form data
    setFormData({
      fullName: "",
      email: "",
      mobileNumber: "",
      jobRole: "",
      referralName: "",
      resume: null
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const handleNameChange = (e) => {
    const value = e.target.value;
    if (/^[a-zA-Z\s]*$/.test(value)) {
      setFormData(prev => ({ ...prev, fullName: value }));
 
      if (errors.fullName) {
        setErrors(prev => ({ ...prev, fullName: "" }));
      }
    }
  };

  const handleMobileChange = (e) => {
    const value = e.target.value;
    if (/^\d*$/.test(value)) {
      setFormData(prev => ({ ...prev, mobileNumber: value }));
      // Clear error when user starts typing
      if (errors.mobileNumber) {
        setErrors(prev => ({ ...prev, mobileNumber: "" }));
      }
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, resume: "File size should be less than 5MB" }));
        return;
      }
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, resume: "Please upload only PDF, DOC, or DOCX files" }));
        return;
      }
 
      if (errors.resume) {
        setErrors(prev => ({ ...prev, resume: "" }));
      }
    }
    setFormData(prev => ({ ...prev, resume: file }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required";
    } else if (!/^[a-zA-Z\s]+$/.test(formData.fullName.trim())) {
      newErrors.fullName = "Name can only contain letters and spaces";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.mobileNumber.trim()) {
      newErrors.mobileNumber = "Mobile number is required";
    } else if (!/^\d{10,15}$/.test(formData.mobileNumber.trim())) {
      newErrors.mobileNumber = "Please enter a valid mobile number (10-15 digits)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    setTimeout(() => {
      setIsSubmitting(false);
      setShowSuccessPopup(true);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-black text-white" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Hero Section with Gradient */}
      <section className="relative pt-20 sm:pt-24 md:pt-32 pb-12 sm:pb-16 md:pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
        </div>

        <div className="relative max-w-7xl mx-auto text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-3 mt-0">
              <Users className="w-5 h-5 text-white" />
              <span className="text-white font-medium tracking-wider uppercase text-sm">
                Join Our Team
              </span>
            </div>

            <h1 className="text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light text-white mb-6 sm:mb-8 tracking-[0.1em] sm:tracking-[0.2em] uppercase leading-tight">
              CAREERS
            </h1>
            <div className="h-px w-24 sm:w-32 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6 sm:mb-8"></div>

            <p className="text-base sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6 font-light max-w-4xl mx-auto tracking-wide px-2">
              Build Your Future with Diamond Excellence
            </p>

            <p className="text-sm sm:text-base text-white/70 max-w-3xl mx-auto leading-relaxed mb-8 px-2">
              Join a team of passionate professionals dedicated to excellence in the diamond industry.
              We offer growth opportunities, competitive benefits, and a collaborative work environment.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center gap-2 text-white/80">
                <MapPin className="w-5 h-5 text-white" />
                <span className="text-sm">Surat, Gujarat</span>
              </div>
              <div className="hidden sm:block w-px h-6 bg-white/30" />
              <div className="flex items-center gap-2 text-white/80">
                <Award className="w-5 h-5 text-white" />
                <span className="text-sm">Equal Opportunity Employer</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Job Listings Section */}
      <section className="relative py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.05)_0%,transparent_70%)]"></div>
        </div>

        <div className="relative max-w-7xl mx-auto z-10">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-10"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
              Current <span className="italic font-light">Opportunities</span>
            </h2>
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6"></div>
            <p className="text-base text-white/80 max-w-2xl mx-auto tracking-wide">
              Explore exciting career opportunities and become part of our growing team
            </p>
          </motion.div>

          {/* Job Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
            {staticJobs.map((job, index) => (
              <motion.div
                key={job.id || index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="group relative bg-black/20 backdrop-blur-sm rounded-lg overflow-hidden border border-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105"
              >
                <div className="p-6">
                  {/* Job Title */}
                  <div className="flex items-start justify-between mb-6">
                    <h3 className="text-xl font-light text-white tracking-wide uppercase">
                      {job.title}
                    </h3>
                    <div className="bg-gradient-to-r from-white/10 to-white/5 text-white px-3 py-1 rounded-full text-xs font-medium border border-white/20">
                      Open
                    </div>
                  </div>

                {/* Job Details */}
                <div className="space-y-4 mb-8">
                  <div className="flex items-center gap-3 text-gray-400">
                    <MapPin className="w-5 h-5 text-white" />
                    <span className="font-medium">{job.location}</span>
                  </div>

                  <div className="flex items-center gap-3 text-gray-400">
                    <GraduationCap className="w-5 h-5 text-white" />
                    <span>{job.graduation} Required</span>
                  </div>

                  <div className="flex items-center gap-3 text-gray-400">
                    <Clock className="w-5 h-5 text-white" />
                    <span>{job.experience} Experience</span>
                  </div>

                  <div className="flex items-center gap-3 text-gray-400">
                    <DollarSign className="w-5 h-5 text-white" />
                    <span>₹{job.salary} per month</span>
                  </div>
                </div>

                  {/* Apply Button */}
                  <button
                    onClick={() => handleShowJob(job)}
                    className="w-full bg-gradient-to-r from-white to-gray-100 text-black py-3 px-6 rounded-lg font-medium hover:from-gray-100 hover:to-white transition-all duration-300 flex items-center justify-center gap-2 tracking-wide uppercase text-sm"
                  >
                    <Send className="w-4 h-4" />
                    Apply for Position
                  </button>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="mt-12 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-8 text-center"
          >
            <h3 className="text-lg font-light text-white mb-4 tracking-wide uppercase">
              Have Questions About These Positions?
            </h3>
            <p className="text-white/70 mb-6 text-sm">
              Our HR team is here to help you with any questions about career opportunities
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="tel:+919909960024"
                className="flex items-center gap-2 text-white hover:text-gray-300 font-medium transition-colors"
              >
                <span>📞</span>
                +91 99099 60024
              </a>
              <div className="hidden sm:block w-px h-6 bg-gray-600" />
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-white hover:text-gray-300 font-medium transition-colors"
              >
                <span>✉️</span>
                <EMAIL>
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Professional Application Modal - Fully Responsive */}
      {isModalOpen && selectedJob && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 md:p-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl max-h-[95vh] sm:max-h-[90vh] bg-white rounded-lg sm:rounded-xl md:rounded-2xl shadow-2xl overflow-hidden border border-gray-200 sm:border-2 sm:border-white"
          >
            {/* Modal Header */}
            <div className="bg-black text-white p-3 sm:p-4 md:p-6 relative border-b border-gray-800">
              <button
                onClick={closeModal}
                className="absolute top-2 right-2 sm:top-3 sm:right-3 md:top-4 md:right-4 text-white/80 hover:text-white transition-colors text-xl sm:text-2xl w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full hover:bg-white/10"
              >
                ×
              </button>

              <h2 className="text-lg sm:text-xl md:text-2xl font-semibold mb-1 sm:mb-2 pr-8 sm:pr-10">Apply for {selectedJob.title}</h2>
              <p className="text-gray-400 text-sm sm:text-base">Join our team and grow your career with Diamond Atelier</p>
            </div>

            <div className="flex flex-col md:flex-row max-h-[calc(95vh-80px)] sm:max-h-[calc(90vh-120px)] overflow-hidden">
              {/* Left Side - Job Details */}
              <div className="w-full md:w-2/5 bg-black p-3 sm:p-4 md:p-6 overflow-y-auto border-r-0 md:border-r border-gray-800 border-b md:border-b-0">
                <div className="space-y-4 sm:space-y-6">
                  {/* Position Overview */}
                  <div>
                    <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-4">Position Overview</h3>
                    <div className="bg-white/5 border border-white/10 rounded-lg p-3 sm:p-4 space-y-2 sm:space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400 font-medium">Position</span>
                        <span className="text-white font-semibold">{selectedJob.title}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400 font-medium">Location</span>
                        <span className="text-white">{selectedJob.location}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400 font-medium">Education</span>
                        <span className="text-white">{selectedJob.graduation}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400 font-medium">Experience</span>
                        <span className="text-white">{selectedJob.experience}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400 font-medium">Salary</span>
                        <span className="text-white font-semibold">₹{selectedJob.salary}/month</span>
                      </div>
                    </div>
                  </div>

                  {/* Key Responsibilities */}
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-4">Key Responsibilities</h4>
                    <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                      <ul className="space-y-2 text-gray-300">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-white mt-0.5 flex-shrink-0" />
                          <span className="text-sm">Customer service and inquiry management</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-white mt-0.5 flex-shrink-0" />
                          <span className="text-sm">Cash handling and transaction processing</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-white mt-0.5 flex-shrink-0" />
                          <span className="text-sm">Payment processing and billing support</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-white mt-0.5 flex-shrink-0" />
                          <span className="text-sm">Daily sales reporting and register management</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-white mt-0.5 flex-shrink-0" />
                          <span className="text-sm">Product information and promotional support</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* Benefits */}
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-4">What We Offer</h4>
                    <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                      <ul className="space-y-2 text-gray-300">
                        <li className="flex items-center gap-2">
                          <Award className="w-4 h-4 text-white" />
                          <span className="text-sm">Competitive salary package</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-white" />
                          <span className="text-sm">Professional growth opportunities</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-white" />
                          <span className="text-sm">Supportive work environment</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Professional Application Form */}
              <div className="w-full md:w-3/5 bg-white p-3 sm:p-4 md:p-6 overflow-y-auto">
                <h3 className="text-lg sm:text-xl font-semibold text-black mb-4 sm:mb-6">Submit Your Application</h3>

                <form className="space-y-4 sm:space-y-5">
                  {/* Personal Information */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-black mb-1 sm:mb-2">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleNameChange}
                        className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-md sm:rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-colors text-sm sm:text-base ${
                          errors.fullName ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="Enter your full name"
                        required
                      />
                      {errors.fullName && (
                        <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-black mb-1 sm:mb-2">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-md sm:rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-colors text-sm sm:text-base ${
                          errors.email ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="<EMAIL>"
                        required
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-black mb-1 sm:mb-2">
                        Mobile Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        name="mobileNumber"
                        value={formData.mobileNumber}
                        onChange={handleMobileChange}
                        className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-md sm:rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-colors text-sm sm:text-base ${
                          errors.mobileNumber ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="10-digit mobile number"
                        required
                      />
                      {errors.mobileNumber && (
                        <p className="text-red-500 text-sm mt-1">{errors.mobileNumber}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-black mb-2">
                        Applying For
                      </label>
                      <input
                        type="text"
                        name="jobRole"
                        value={formData.jobRole}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
                        readOnly
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Referral Name (Optional)
                    </label>
                    <input
                      type="text"
                      name="referralName"
                      value={formData.referralName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-colors"
                      placeholder="Name of person who referred you"
                    />
                  </div>

                  {/* Resume Upload */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Resume/CV <span className="text-red-500">*</span>
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                      errors.resume ? "border-red-300 bg-red-50" : "border-gray-300 hover:border-black"
                    }`}>
                      <input
                        type="file"
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx"
                        className="hidden"
                        id="resume-upload"
                      />
                      <label htmlFor="resume-upload" className="cursor-pointer">
                        <div className="flex flex-col items-center">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-3">
                            <Send className="w-6 h-6 text-black" />
                          </div>
                          <div className="text-black font-medium mb-1">
                            {formData.resume ? formData.resume.name : "Upload your resume"}
                          </div>
                          <div className="text-sm text-gray-500">
                            PDF, DOC, DOCX (Max 5MB)
                          </div>
                        </div>
                      </label>
                    </div>
                    {errors.resume && (
                      <p className="text-red-500 text-sm mt-1">{errors.resume}</p>
                    )}
                  </div>

                  {/* Terms Agreement */}
                  <div className="flex items-start gap-3 p-4 bg-gray-100 rounded-lg">
                    <input
                      type="checkbox"
                      className="mt-1 w-4 h-4 text-black border-gray-300 rounded focus:ring-black"
                      required
                    />
                    <label className="text-sm text-gray-700 leading-relaxed">
                      I agree to the processing of my personal data for recruitment purposes and
                      understand that my information will be handled according to the company's privacy policy.
                    </label>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-3 sm:pt-4">
                    <button
                      type="button"
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      className="w-full bg-black text-white py-2 sm:py-3 px-4 sm:px-6 rounded-md sm:rounded-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 text-sm sm:text-base"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span className="text-xs sm:text-sm">Submitting Application...</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span className="text-xs sm:text-sm">Submit Application</span>
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Black & White Success Popup - Responsive */}
      {showSuccessPopup && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3 sm:p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-lg sm:rounded-xl md:rounded-2xl max-w-xs sm:max-w-sm md:max-w-md w-full p-4 sm:p-6 md:p-8 text-center shadow-2xl border border-gray-200"
          >
            {/* Success Icon */}
            <div className="mb-4 sm:mb-6">
              <div className="mx-auto w-12 h-12 sm:w-16 sm:h-16 bg-black rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
            </div>

            {/* Success Message */}
            <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-black mb-2 sm:mb-3">
              Application Submitted!
            </h3>
            <p className="text-gray-600 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
              Thank you for your interest in joining Diamond Atelier.
              Our HR team will review your application and contact you within 2-3 business days.
            </p>

            {/* Next Steps */}
            <div className="bg-gray-100 rounded-md sm:rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 text-left">
              <h4 className="font-medium text-black mb-2 text-sm sm:text-base">What happens next?</h4>
              <ul className="text-xs sm:text-sm text-gray-700 space-y-1">
                <li>• Application review (1-2 days)</li>
                <li>• Initial screening call</li>
                <li>• Interview scheduling</li>
              </ul>
            </div>

            {/* Close Button */}
            <button
              onClick={closeModal}
              className="w-full bg-black text-white py-2 sm:py-3 px-4 sm:px-6 rounded-md sm:rounded-lg font-medium hover:bg-gray-800 transition-colors text-sm sm:text-base"
            >
              Continue
            </button>
          </motion.div>
        </div>
      )}

    </div>
  );
};

export default CareersPage;