import "./globals.css";
import "../styles/mac-navbar-fixes.css";
import { <PERSON>, Montser<PERSON>, <PERSON><PERSON>_G<PERSON>mond, Ari<PERSON>, Pop<PERSON>s, Playfair_Display, DM_Sans, Space_Grotesk } from "next/font/google";

import AppLayout from "@/layout/app/layout";
import ClientOnlyAnalytics from "@/components/Analytics/ClientOnlyAnalytics";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const montserrat = Montserrat({
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
});

const arimo = Arimo({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap', 
  variable: "--font-arimo"
});

const ebGaramond = <PERSON><PERSON>_Garamond({
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['latin'],
  display: 'swap',
  variable: "--font-eb<PERSON>mond"
});



const poppins = Poppins({
  weight: ['300', '400', '500', '600', '700', '800'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const playfairDisplay = Playfair_Display({
  weight: ['400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair',
});

const dmSans = DM_Sans({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-dm-sans',
});

const spaceGrotesk = Space_Grotesk({
  weight: ['300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-space-grotesk',
});

export const metadata = {
  title: {
    default: "Diamond Atelier - Premium Lab Grown Diamonds | Certified & Ethical",
    template: "%s | Diamond Atelier"
  },
  description: "Discover premium lab-grown diamonds at Diamond Atelier. 10,000+ certified stones, ethical sourcing, exceptional quality. Expert diamond manufacturer & supplier in India.",
  keywords: [
    "lab grown diamonds",
    "synthetic diamonds",
    "diamond manufacturer",
    "certified diamonds",
    "ethical diamonds",
    "diamond supplier India",
    "CVD diamonds",
    "HPHT diamonds",
    "diamond jewelry",
    "engagement rings",
    "diamond education",
    "conflict-free diamonds"
  ],
  authors: [{ name: "Diamond Atelier" }],
  creator: "Diamond Atelier",
  publisher: "Diamond Atelier",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://diamondatelier.in'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Diamond Atelier - Premium Lab Grown Diamonds",
    description: "Discover premium lab-grown diamonds at Diamond Atelier. 10,000+ certified stones, ethical sourcing, exceptional quality.",
    url: 'https://diamondatelier.in',
    siteName: 'Diamond Atelier',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Diamond Atelier - Premium Lab Grown Diamonds',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Diamond Atelier - Premium Lab Grown Diamonds",
    description: "Discover premium lab-grown diamonds at Diamond Atelier. 10,000+ certified stones, ethical sourcing, exceptional quality.",
    images: ['/images/twitter-image.jpg'],
    creator: '@diamondatelier',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({ children }) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Diamond Atelier",
    "description": "Premium lab-grown diamond manufacturer and supplier",
    "url": "https://diamondatelier.in",
    "logo": "https://diamondatelier.in/images/logo/Diamond Atelier logo (white).png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+91-XXXXXXXXXX",
      "contactType": "customer service",
      "availableLanguage": ["English", "Hindi"]
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "IN",
      "addressRegion": "Gujarat"
    },
    "sameAs": [
      "https://www.linkedin.com/company/diamond-atelier",
      "https://www.instagram.com/diamond_atelier",
      "https://www.facebook.com/diamondatelier"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Lab Grown Diamonds",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Product",
            "name": "Lab Grown Diamonds",
            "description": "Premium certified lab-grown diamonds"
          }
        }
      ]
    }
  };

  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <link rel="canonical" href="https://diamondatelier.in" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#000000" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        {/* Resource hints for performance optimization */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://www.google-analytics.com" />
      </head>
      <body
        className={`${inter.variable} ${montserrat.variable} ${ebGaramond.variable} ${arimo.variable} ${poppins.variable} ${playfairDisplay.variable} ${dmSans.variable} ${spaceGrotesk.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <AppLayout>
          <ClientOnlyAnalytics />
          {children}
        </AppLayout>
      </body>
    </html>
  );
}
