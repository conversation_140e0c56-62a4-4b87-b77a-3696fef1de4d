"use client";
import React, { useEffect, useState  } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import Link from "next/link";
import { 
  Star, 
  Diamond, 
  Award, 
  Settings, 
  Sparkles, 
  Eye, 
  Crown,
  Gem,
  ArrowRight,
  CheckCircle
} from "lucide-react";
import LoadingScreen from "../../utils/LoadingScreen";

const iconMap = {
  star: Star,
  diamond: Diamond,
  certificate: Award,
  settings: Settings
};

export default function ClientStarMellePage({ heroContent, features, specifications, collections }) {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1300);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <LoadingScreen />;

  return (
    <div className="bg-black text-white min-h-screen" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Luxury Hero Section */}
      <section className="relative pb-12 bg-black text-white overflow-hidden" style={{ marginTop: '0px' }}>
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.08)_0%,transparent_60%)]"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-12 text-center">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* Luxury Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-6 mt-6">
              <Crown className="w-5 h-5 text-white" />
              <span className="text-white font-light tracking-[0.2em] uppercase text-sm">
                Premium Collection
              </span>
            </div>

            {/* Main Title - Compact Style */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-white mb-6 leading-tight tracking-[0.1em] uppercase">
              <span className="block">STAR Melee</span>
              {/* <span className="block italic font-light text-gray-200">Melle</span> */}
              <span className="block text-2xl md:text-3xl lg:text-4xl mt-2 font-light tracking-[0.1em]">DIAMONDS</span>
            </h1>

            {/* Elegant Divider */}
            <div className="flex items-center justify-center mb-6">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-white to-transparent"></div>
              <Gem className="w-3 h-3 text-white mx-3" />
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-white to-transparent"></div>
            </div>

            {/* Subtitle */}
            <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4 font-light tracking-wide">
              {heroContent.subtitle}
            </p>

            {/* Description */}
            <p className="text-base text-gray-400 max-w-2xl mx-auto leading-relaxed mb-8 font-light">
              {heroContent.description}
            </p>

            {/* CTA Buttons - Compact Style */}
            {/* <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="group bg-white text-black px-8 py-3 rounded-none font-light hover:bg-gray-100 transition-all duration-500 tracking-[0.15em] uppercase text-sm border-2 border-white">
                <span className="flex items-center gap-2">
                  Explore Collection
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
              </button>
              <button className="group border-2 border-white/40 text-white px-8 py-3 rounded-none font-light hover:bg-white hover:text-black transition-all duration-500 tracking-[0.15em] uppercase text-sm">
                <span className="flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  View Specifications
                </span>
              </button>
            </div> */}
          </motion.div>
        </div>
      </section>

      {/* Features Section - Cartier Inspired */}
      <section className="py-12 bg-black border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-10"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
              Exceptional <span className="italic font-light">Craftsmanship</span>
            </h2>
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"></div>
          </motion.div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => {
              const IconComponent = iconMap[feature.icon];
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group text-center p-6 border border-gray-800 hover:border-white/30 transition-all duration-500 bg-gradient-to-b from-gray-900/20 to-black"
                >
                  <div className="mb-4">
                    <IconComponent className="w-8 h-8 text-white mx-auto group-hover:scale-110 transition-transform duration-500" />
                  </div>
                  <h3 className="text-lg font-light text-white mb-3 tracking-[0.1em] uppercase">
                    {feature.title}
                  </h3>
                  <p className="text-gray-400 leading-relaxed font-light text-sm">
                    {feature.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Specifications Section - Van Cleef Style */}
      <section className="py-12 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-10"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
              Technical <span className="italic font-light">Excellence</span>
            </h2>
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-4"></div>
            <p className="text-base text-gray-400 max-w-2xl mx-auto font-light">
              Precision-crafted specifications that define our commitment to perfection
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-black/50 backdrop-blur-sm border border-gray-700 p-6 hover:border-white/30 transition-all duration-500"
              >
                <h3 className="text-lg font-light text-white mb-4 tracking-[0.1em] uppercase border-b border-gray-700 pb-3">
                  {spec.category}
                </h3>
                <div className="space-y-2">
                  {spec.details.map((detail, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <CheckCircle className="w-3 h-3 text-white flex-shrink-0" />
                      <span className="text-gray-300 font-light text-sm">{detail}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Collections Section - Graff Inspired */}
      <section className="py-12 bg-black">
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-10"
          >
            <h2 className="text-2xl md:text-3xl font-light text-white mb-4 tracking-[0.1em] uppercase">
              Signature <span className="italic font-light">Collections</span>
            </h2>
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent mx-auto"></div>
          </motion.div>

          <div className="space-y-10">
            {collections.map((collection, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-8 items-center ${
                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                }`}
              >
                {/* Content */}
                <div className={`space-y-4 ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="inline-flex items-center gap-2 bg-white/5 border border-white/20 rounded-full px-4 py-2">
                    <Sparkles className="w-3 h-3 text-white" />
                    <span className="text-white font-light text-xs tracking-[0.15em] uppercase">
                      Premium Collection
                    </span>
                  </div>

                  <h3 className="text-xl md:text-2xl font-light text-white leading-tight tracking-[0.05em]">
                    {collection.name}
                  </h3>

                  <p className="text-base text-gray-300 leading-relaxed font-light">
                    {collection.description}
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="text-xs text-gray-500 uppercase tracking-[0.15em] w-16">Size Range:</span>
                      <span className="text-white font-light text-sm">{collection.sizes}</span>
                    </div>
                    {/* <div className="flex items-start gap-4">
                      <span className="text-sm text-gray-500 uppercase tracking-[0.2em] w-20 mt-1">Applications:</span>
                      <div className="flex flex-wrap gap-2">
                        {collection.applications.map((app, idx) => (
                          <span key={idx} className="bg-white/10 text-white px-3 py-1 text-xs font-light tracking-wide border border-white/20">
                            {app}
                          </span>
                        ))}
                      </div>
                    </div> */}
                  </div>

                  {/* <button className="group border border-white/40 text-white px-8 py-3 font-light hover:bg-white hover:text-black transition-all duration-500 tracking-[0.2em] uppercase text-sm">
                    <span className="flex items-center gap-3">
                      View Details
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </span>
                  </button> */}
                </div>

                {/* Image Placeholder */}
                <div className={`relative ${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                  <div className="relative aspect-[4/3] bg-gradient-to-br from-gray-800 to-gray-900 rounded-none overflow-hidden group border border-gray-700">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Diamond className="w-16 h-16 text-white/30 group-hover:text-white/50 transition-colors duration-500" />
                    </div>
                    <div className="absolute bottom-4 left-4 right-4">
                      <p className="text-white/80 text-sm font-light tracking-wide">
                        {collection.name}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Luxury CTA Section */}
      <section className="py-12 bg-gradient-to-b from-gray-900 to-black border-t border-gray-800">
        <div className="max-w-4xl mx-auto px-6 lg:px-12 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-flex items-center gap-2 bg-white/5 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3">
              <Crown className="w-5 h-5 text-white" />
              <span className="text-white font-light tracking-[0.2em] uppercase text-sm">
                Bespoke Service
              </span>
            </div>

            <h2 className="text-2xl md:text-3xl font-light text-white leading-tight tracking-[0.05em]">
              Create Your <span className="italic font-light">Masterpiece</span>
            </h2>

            <p className="text-base text-gray-300 leading-relaxed font-light max-w-2xl mx-auto">
              Work with our master craftsmen to create bespoke jewelry featuring our finest star melle diamonds.
              Each piece is a testament to uncompromising quality and timeless elegance.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6">
            <Link href="/contact">
              <button className="bg-white text-black px-8 py-3 font-light hover:bg-gray-100 transition-all duration-500 tracking-[0.15em] uppercase text-sm">
                Schedule Consultation
              </button>
              </Link>
              
              {/* <button className="border-2 border-white/40 text-white px-8 py-3 font-light hover:bg-white hover:text-black transition-all duration-500 tracking-[0.15em] uppercase text-sm">
                View Portfolio
              </button> */}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Size Chart Table Section */}
      <section className="py-16 bg-black text-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-light text-white leading-tight tracking-[0.05em] mb-4">
              -2/STAR/MELEE/ELEVEN <span className="italic font-light">SIZE CHART</span>
            </h2>
            <p className="text-base text-gray-300 leading-relaxed font-light max-w-3xl mx-auto">
              Comprehensive size chart for diamond categories from -2 to Caraties, showing precise measurements and point values.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="overflow-x-auto"
          >
            <div className="min-w-full bg-gradient-to-br from-gray-900/50 to-black/50 backdrop-blur-sm border border-white/10 rounded-lg">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="px-4 py-4 text-left font-light text-white tracking-[0.1em] uppercase">Pointer</th>
                    <th className="px-4 py-4 text-left font-light text-white tracking-[0.1em] uppercase">Sieve</th>
                    <th className="px-4 py-4 text-left font-light text-white tracking-[0.1em] uppercase">Size (MM)</th>
                    <th className="px-4 py-4 text-left font-light text-white tracking-[0.1em] uppercase">Points</th>
                  </tr>
                </thead>
                <tbody className="text-gray-300">
                  {/* -2 Category */}
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300">
                    <td className="px-4 py-3 font-light">-2</td>
                    <td className="px-4 py-3 font-light">000-00</td>
                    <td className="px-4 py-3 font-light">0.90-0.99</td>
                    <td className="px-4 py-3 font-light">0.45</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300">
                    <td className="px-4 py-3 font-light">-2</td>
                    <td className="px-4 py-3 font-light">00-0</td>
                    <td className="px-4 py-3 font-light">1.00-1.09</td>
                    <td className="px-4 py-3 font-light">0.5</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300">
                    <td className="px-4 py-3 font-light">-2</td>
                    <td className="px-4 py-3 font-light">0-10</td>
                    <td className="px-4 py-3 font-light">1.10-1.14</td>
                    <td className="px-4 py-3 font-light">0.65</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300">
                    <td className="px-4 py-3 font-light">-2</td>
                    <td className="px-4 py-3 font-light">+1-1.1.5</td>
                    <td className="px-4 py-3 font-light">1.15-1.19</td>
                    <td className="px-4 py-3 font-light">0.70</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300">
                    <td className="px-4 py-3 font-light">-2</td>
                    <td className="px-4 py-3 font-light">+1.5-2.0</td>
                    <td className="px-4 py-3 font-light">1.20-1.24</td>
                    <td className="px-4 py-3 font-light">0.80</td>
                  </tr>

                  {/* Star Category */}
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+2.0-2.5</td>
                    <td className="px-4 py-3 font-light">1.25-1.29</td>
                    <td className="px-4 py-3 font-light">0.90</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+2.5-3.0</td>
                    <td className="px-4 py-3 font-light">1.30-1.34</td>
                    <td className="px-4 py-3 font-light">1.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+3.0-3.0</td>
                    <td className="px-4 py-3 font-light">1.35-1.39</td>
                    <td className="px-4 py-3 font-light">1.10</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+3.5-4.0</td>
                    <td className="px-4 py-3 font-light">1.40-1.44</td>
                    <td className="px-4 py-3 font-light">1.25</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+4.0-4.5</td>
                    <td className="px-4 py-3 font-light">1.45-1.49</td>
                    <td className="px-4 py-3 font-light">1.30</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+4.5-5.0</td>
                    <td className="px-4 py-3 font-light">1.50-1.54</td>
                    <td className="px-4 py-3 font-light">1.50</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+5.0-5.5</td>
                    <td className="px-4 py-3 font-light">1.55-1.59</td>
                    <td className="px-4 py-3 font-light">1.60</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+5.5-6.0</td>
                    <td className="px-4 py-3 font-light">1.60-1.69</td>
                    <td className="px-4 py-3 font-light">1.75</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-white/5">
                    <td className="px-4 py-3 font-medium text-white">Star</td>
                    <td className="px-4 py-3 font-light">+6.0-6.5</td>
                    <td className="px-4 py-3 font-light">1.70-1.79</td>
                    <td className="px-4 py-3 font-light">2.00</td>
                  </tr>

                  {/* Melee Category */}
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+6.5-7.0</td>
                    <td className="px-4 py-3 font-light">1.80-1.89</td>
                    <td className="px-4 py-3 font-light">2.50</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+7.0-7.5</td>
                    <td className="px-4 py-3 font-light">1.90-1.99</td>
                    <td className="px-4 py-3 font-light">3.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+7.5-8.0</td>
                    <td className="px-4 py-3 font-light">2.00-2.09</td>
                    <td className="px-4 py-3 font-light">3.50</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+8.0-8.5</td>
                    <td className="px-4 py-3 font-light">2.10-2.19</td>
                    <td className="px-4 py-3 font-light">4.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+8.5-9.0</td>
                    <td className="px-4 py-3 font-light">2.20-2.29</td>
                    <td className="px-4 py-3 font-light">4.50</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+9.0-9.5</td>
                    <td className="px-4 py-3 font-light">2.30-2.39</td>
                    <td className="px-4 py-3 font-light">5.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+9.5-10.0</td>
                    <td className="px-4 py-3 font-light">2.40-2.49</td>
                    <td className="px-4 py-3 font-light">5.50</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+10.0-10.5</td>
                    <td className="px-4 py-3 font-light">2.50-2.59</td>
                    <td className="px-4 py-3 font-light">6.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-blue-500/10">
                    <td className="px-4 py-3 font-medium text-blue-300">Melee</td>
                    <td className="px-4 py-3 font-light">+10.5-11.0</td>
                    <td className="px-4 py-3 font-light">2.60-2.69</td>
                    <td className="px-4 py-3 font-light">7.00</td>
                  </tr>

                  {/* Eleven Category */}
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-green-500/10">
                    <td className="px-4 py-3 font-medium text-green-300">Eleven</td>
                    <td className="px-4 py-3 font-light">+1.10-1.15</td>
                    <td className="px-4 py-3 font-light">2.70-2.79</td>
                    <td className="px-4 py-3 font-light">8.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-green-500/10">
                    <td className="px-4 py-3 font-medium text-green-300">Eleven</td>
                    <td className="px-4 py-3 font-light">+11.5-12.0</td>
                    <td className="px-4 py-3 font-light">2.80-2.89</td>
                    <td className="px-4 py-3 font-light">9.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-green-500/10">
                    <td className="px-4 py-3 font-medium text-green-300">Eleven</td>
                    <td className="px-4 py-3 font-light">+12.0-12.5</td>
                    <td className="px-4 py-3 font-light">2.90-2.99</td>
                    <td className="px-4 py-3 font-light">10.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-green-500/10">
                    <td className="px-4 py-3 font-medium text-green-300">Eleven</td>
                    <td className="px-4 py-3 font-light">+12.5-13.0</td>
                    <td className="px-4 py-3 font-light">3.00-3.09</td>
                    <td className="px-4 py-3 font-light">11.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-green-500/10">
                    <td className="px-4 py-3 font-medium text-green-300">Eleven</td>
                    <td className="px-4 py-3 font-light">+13-13.5</td>
                    <td className="px-4 py-3 font-light">3.10-3.19</td>
                    <td className="px-4 py-3 font-light">12.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-green-500/10">
                    <td className="px-4 py-3 font-medium text-green-300">Eleven</td>
                    <td className="px-4 py-3 font-light">+13.5-14.0</td>
                    <td className="px-4 py-3 font-light">3.20-3.29</td>
                    <td className="px-4 py-3 font-light">13.00</td>
                  </tr>

                  {/* Fourteen Category */}
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-yellow-500/10">
                    <td className="px-4 py-3 font-medium text-yellow-300">Fourteen</td>
                    <td className="px-4 py-3 font-light">+14.0-14.5</td>
                    <td className="px-4 py-3 font-light">3.30-3.39</td>
                    <td className="px-4 py-3 font-light">14</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-yellow-500/10">
                    <td className="px-4 py-3 font-medium text-yellow-300">Fourteen</td>
                    <td className="px-4 py-3 font-light">+14.5-15.0</td>
                    <td className="px-4 py-3 font-light">3.40-3.49</td>
                    <td className="px-4 py-3 font-light">15.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-yellow-500/10">
                    <td className="px-4 py-3 font-medium text-yellow-300">Fourteen</td>
                    <td className="px-4 py-3 font-light">+15.0-15.5</td>
                    <td className="px-4 py-3 font-light">3.50-3.59</td>
                    <td className="px-4 py-3 font-light">16.00</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-yellow-500/10">
                    <td className="px-4 py-3 font-medium text-yellow-300">Fourteen</td>
                    <td className="px-4 py-3 font-light">+15.5-16.0</td>
                    <td className="px-4 py-3 font-light">3.60-3.69</td>
                    <td className="px-4 py-3 font-light">17.00</td>
                  </tr>

                  {/* Larger Categories */}
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-purple-500/10">
                    <td className="px-4 py-3 font-medium text-purple-300">Twenties</td>
                    <td className="px-4 py-3 font-light">1/5</td>
                    <td className="px-4 py-3 font-light">3.70-3.90</td>
                    <td className="px-4 py-3 font-light">18-22</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-purple-500/10">
                    <td className="px-4 py-3 font-medium text-purple-300">Quarter</td>
                    <td className="px-4 py-3 font-light">1/4</td>
                    <td className="px-4 py-3 font-light">4.00-4.10</td>
                    <td className="px-4 py-3 font-light">23-29</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-purple-500/10">
                    <td className="px-4 py-3 font-medium text-purple-300">Thirties</td>
                    <td className="px-4 py-3 font-light">1/3</td>
                    <td className="px-4 py-3 font-light">4.20-4.70</td>
                    <td className="px-4 py-3 font-light">30-39</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-purple-500/10">
                    <td className="px-4 py-3 font-medium text-purple-300">Forties</td>
                    <td className="px-4 py-3 font-light">3/8</td>
                    <td className="px-4 py-3 font-light">4.80-5.10</td>
                    <td className="px-4 py-3 font-light">40-49</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-purple-500/10">
                    <td className="px-4 py-3 font-medium text-purple-300">Half</td>
                    <td className="px-4 py-3 font-light">1/2</td>
                    <td className="px-4 py-3 font-light">5.10-5.60</td>
                    <td className="px-4 py-3 font-light">50-69</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-purple-500/10">
                    <td className="px-4 py-3 font-medium text-purple-300">Seventies</td>
                    <td className="px-4 py-3 font-light">3/4</td>
                    <td className="px-4 py-3 font-light">5.70-6.20</td>
                    <td className="px-4 py-3 font-light">70-89</td>
                  </tr>
                  <tr className="border-b border-white/10 hover:bg-white/5 transition-colors duration-300 bg-gold/20">
                    <td className="px-4 py-3 font-medium text-yellow-200">Caraties</td>
                    <td className="px-4 py-3 font-light">1/1</td>
                    <td className="px-4 py-3 font-light">6.40</td>
                    <td className="px-4 py-3 font-light">90.00</td>
                  </tr>
                  <tr className="hover:bg-white/5 transition-colors duration-300 bg-gold/20">
                    <td className="px-4 py-3 font-medium text-yellow-200">1.00 Ct</td>
                    <td className="px-4 py-3 font-light">-</td>
                    <td className="px-4 py-3 font-light">6.50</td>
                    <td className="px-4 py-3 font-light">1 Carat</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
