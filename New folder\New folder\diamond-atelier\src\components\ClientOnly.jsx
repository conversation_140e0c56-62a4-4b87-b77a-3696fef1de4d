"use client";
import { useState, useEffect } from 'react';

/**
 * ClientOnly Component
 * 
 * This component prevents hydration mismatches by only rendering children
 * on the client side after the component has mounted.
 * 
 * Useful for components that:
 * - Use browser-specific APIs
 * - Have dynamic content that differs between server and client
 * - Are affected by browser extensions
 * 
 * Usage:
 * <ClientOnly fallback={<div>Loading...</div>}>
 *   <ComponentThatCausesHydrationIssues />
 * </ClientOnly>
 */
export default function ClientOnly({ children, fallback = null }) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return fallback;
  }

  return children;
}
