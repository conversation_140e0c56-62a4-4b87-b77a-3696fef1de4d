export default function Loading() {
  return (
    <section className="relative pt-24 bg-black min-h-screen grid content-center">
      <div className="animate-pulse text-center space-y-8">
        {/* Title placeholders */}
        <div className="h-8 bg-gray-700 rounded w-64 mx-auto"></div>
        <div className="h-16 bg-gray-700 rounded w-96 mx-auto mb-10"></div>
        
        {/* Grid placeholders */}
        <div className="grid xl:grid-cols-4 md:grid-cols-4 grid-cols-2 place-items-center gap-6 px-4">
          {[1, 2, 3, 4].map((item) => (
            <div key={item} className="text-center animate-pulse">
              <div className="w-32 h-32 bg-gray-700 rounded-lg mx-auto"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
