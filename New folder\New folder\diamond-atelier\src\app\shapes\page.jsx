

import React from "react";
import {shapes, sideStone, exoticShapes} from "../../utils/shapeData";
import ClientShapesPage from "./ClientShapesPage";


async function getShapesPageData() {
  return {
    shapes,
    sideStone,
    exoticShapes
  };
}

// Server-side component
export default async function Page() {
  // Get data on server
  const { shapes, sideStone, exoticShapes } = await getShapesPageData();

  return (
    <ClientShapesPage
      shapes={shapes}
      sideStone={sideStone}
      exoticShapes={exoticShapes}
    />
  );
}
