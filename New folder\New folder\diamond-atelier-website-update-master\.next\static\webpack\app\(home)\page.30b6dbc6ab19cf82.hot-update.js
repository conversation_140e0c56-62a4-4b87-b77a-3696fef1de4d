"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/page",{

/***/ "(app-pages-browser)/./src/components/common/SimpleHome.jsx":
/*!**********************************************!*\
  !*** ./src/components/common/SimpleHome.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AnimatedHeading */ \"(app-pages-browser)/./src/components/common/AnimatedHeading.jsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sections = [\n    // {\n    //   id: \"hero\",\n    //   title: \"Exquisite Lab Grown Diamonds\",\n    //   subtitle: \"Every Diamond Tells a Story\",\n    //   description:\n    //     \"Ethically sourced, certified diamonds with unparalleled quality and brilliance.\",\n    //   image: \"/image/tache_diamond_rough_cut.png\",\n    //   cta: \"Discover Our Collection\",\n    // },\n    {\n        id: \"shapes\",\n        title: \"100+ shapes\",\n        subtitle: \"Diamonds over 100+ shapes\",\n        description: \"Our artisan workshop offers 100+ diamond shapes, crafted with elegance and precision. We also create custom shapes tailored to your vision within 30 days.\",\n        image: \"/image/diamond-cuts.jpg\",\n        video: \"/images/about/Shapes video.mp4\",\n        cta: \"Discover Shapes\"\n    },\n    {\n        id: \"matching-layout\",\n        title: \"Matching layout\",\n        subtitle: \"Calibrated consistency from 10–99 cents\",\n        description: \"Our comprehensive collection of calibrated matching layouts ranges from 10 to 99 cents, with a 0.10 MM tolerance. Whether you seek uniformity in shapes, colors, clarities, dimensions, or cent values, trust us to deliver precisely what you need.\",\n        image: \"/image/shaping_phase_tache-768x768.png\",\n        video: \"/images/about/Matching Layout video.mp4\",\n        cta: \"View Our Process\"\n    },\n    {\n        id: \"colors\",\n        title: \"30+ colors\",\n        subtitle: \"Nature's Spectrum, Perfected Diamonds colors over 30+ colors\",\n        description: \"We specialize in unique colored diamonds across 10+ fancy shades, with customized color delivery guaranteed within 20 days.\",\n        image: \"/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png\",\n        video: \"/images/about/Fancy Color video.mp4\",\n        cta: \"Explore Colors\"\n    }\n];\nfunction SimpleHome() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHome.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SimpleHome.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"full-section relative w-full h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png\",\n                        alt: \"Diamond Atelier Homepage Banner\",\n                        fill: true,\n                        className: \"absolute inset-0 w-full h-full object-cover\",\n                        priority: true,\n                        quality: 90\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full opacity-60\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight,\n                                    scale: 0\n                                },\n                                animate: {\n                                    y: [\n                                        null,\n                                        -100\n                                    ],\n                                    opacity: [\n                                        0.6,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 3,\n                                    ease: \"easeOut\"\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col items-center justify-center z-20 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat\",\n                                            style: {\n                                                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',\n                                                letterSpacing: \"0.1em\"\n                                            },\n                                            children: \"DIAMONDS THAT DESERVE YOU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 0.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center max-w-4xl mx-auto mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.3em] leading-relaxed font-montserrat\",\n                                        children: [\n                                            \" \",\n                                            \"10,000+ Certified Stones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg lg:text-xl text-black/70 font-light tracking-wide leading-relaxed font-montserrat\",\n                                        children: \"Where precision meets perfection in every facet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 1,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/shapes\",\n                                        className: \"group relative px-8 py-3 bg-Black border border-white/30 text-Black hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"EXPlORE SHAPES\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-Black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"https://inventory.diamondatelier.in/\",\n                                        className: \"group relative px-8 py-3 bg-black/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: \"VIEW INVENTORY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            delay: 1.5\n                        },\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                        onClick: ()=>setCurrentIndex(1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs tracking-widest mb-2 font-light\",\n                                    children: \"SCROLL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 bg-gradient-to-br from-gray-50 to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center justify-between gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-black\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                children: sections[0].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: sections[0].title,\n                                            level: \"h1\",\n                                            className: \"text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat\",\n                                            showUnderline: false,\n                                            animationDelay: 0.2,\n                                            triggerOnMount: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat\",\n                                            children: sections[0].description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/shapes\",\n                                            className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                            children: sections[0].cta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-64 mb-6 overflow-hidden rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: sections[0].image,\n                                                        alt: sections[0].title,\n                                                        fill: true,\n                                                        className: \"object-cover\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-light mb-4 font-montserrat\",\n                                                            children: \"Premium Diamond Cuts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 font-light font-montserrat\",\n                                                            children: \"Masterfully crafted for maximum brilliance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                        children: sections[0].subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    text: sections[0].title,\n                                    level: \"h1\",\n                                    className: \"text-4xl sm:text-5xl mb-8 font-light leading-tight font-montserrat\",\n                                    showUnderline: false,\n                                    animationDelay: 0.2,\n                                    triggerOnMount: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-xl p-6 max-w-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 mb-4 overflow-hidden rounded-xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: sections[0].image,\n                                                    alt: sections[0].title,\n                                                    fill: true,\n                                                    className: \"object-cover\",\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-light mb-2 font-montserrat\",\n                                                children: \"Premium Diamond Cuts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm font-light font-montserrat\",\n                                                children: \"Masterfully crafted for maximum brilliance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                    children: sections[0].description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/shapes\",\n                                    className: \"inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                    children: sections[0].cta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            sections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 \".concat(index % 2 === 0 ? 'bg-white' : 'bg-gray-50'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:grid lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-black \".concat(index % 2 === 0 ? 'order-2' : 'order-1'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                    children: section.subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl xl:text-5xl mb-6 font-light leading-tight font-montserrat\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 font-montserrat\",\n                                                children: section.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                        children: section.cta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                        children: \"Learn More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(index % 2 === 0 ? 'order-1' : 'order-2', \" flex justify-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative max-w-md w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-72 w-80 overflow-hidden rounded-2xl shadow-2xl mx-auto\",\n                                                    children: [\n                                                        section.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            autoPlay: true,\n                                                            loop: true,\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            className: \"w-full h-full object-cover\",\n                                                            ref: (video)=>{\n                                                                if (video) {\n                                                                    video.playbackRate = 4.0;\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                    src: section.video,\n                                                                    type: \"video/mp4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: section.image,\n                                                                    alt: section.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-xl p-4 max-w-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-base font-medium mb-2 font-montserrat\",\n                                                            children: [\n                                                                section.id === 'shapes' && '100+ Diamond Shapes',\n                                                                section.id === 'matching-layout' && 'Calibrated Precision',\n                                                                section.id === 'colors' && '30+ Fancy Colors'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 font-light font-montserrat\",\n                                                            children: [\n                                                                section.id === 'shapes' && 'Custom shapes in 30 days',\n                                                                section.id === 'matching-layout' && '0.10 MM tolerance guarantee',\n                                                                section.id === 'colors' && 'Custom colors in 20 days'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden text-center text-black\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                            children: section.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl mb-6 font-light leading-tight font-montserrat\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-48 w-64 overflow-hidden rounded-2xl shadow-xl mx-auto\",\n                                            children: [\n                                                section.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                    autoPlay: true,\n                                                    loop: true,\n                                                    muted: true,\n                                                    playsInline: true,\n                                                    className: \"w-full h-full object-cover\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                            src: section.video,\n                                                            type: \"video/mp4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: section.image,\n                                                            alt: section.title,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: section.image,\n                                                    alt: section.title,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-4 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                children: section.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-600 px-8 py-3 text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat\",\n                                                children: \"Learn More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                }, section.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative w-full h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"url('/image/Expert.jpg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/40 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 drop-shadow-lg font-montserrat\",\n                                        children: \"Expert Eyes on Every Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-black mb-8 leading-relaxed drop-shadow-md font-montserrat\",\n                                        children: \"Our in-house gemologists personally inspect and verify every lab-grown diamond. You'll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHome, \"2oCXE2WiAW05zG4oFnHELxlAGiA=\");\n_c = SimpleHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/SimpleHome.jsx\n"));

/***/ })

});