"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/page",{

/***/ "(app-pages-browser)/./src/components/common/SimpleHome.jsx":
/*!**********************************************!*\
  !*** ./src/components/common/SimpleHome.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AnimatedHeading */ \"(app-pages-browser)/./src/components/common/AnimatedHeading.jsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sections = [\n    // {\n    //   id: \"hero\",\n    //   title: \"Exquisite Lab Grown Diamonds\",\n    //   subtitle: \"Every Diamond Tells a Story\",\n    //   description:\n    //     \"Ethically sourced, certified diamonds with unparalleled quality and brilliance.\",\n    //   image: \"/image/tache_diamond_rough_cut.png\",\n    //   cta: \"Discover Our Collection\",\n    // },\n    {\n        id: \"shapes\",\n        title: \"Iconic Diamond Cuts\",\n        subtitle: \"Timeless Elegance, Modern Precision\",\n        description: \"From the classic brilliance of round cuts to the contemporary allure of emerald and cushion shapes. Each cut is masterfully executed to maximize fire, brilliance, and scintillation.\",\n        image: \"/image/tache_lesotho_icon-768x752.jpg\",\n        cta: \"Discover Shapes\"\n    },\n    {\n        id: \"craftsmanship\",\n        title: \"Artisanal Excellence\",\n        subtitle: \"Heritage Craftsmanship, Revolutionary Technology\",\n        description: \"Our master craftsmen blend centuries-old techniques with cutting-edge technology, creating diamonds that rival nature's finest. Every facet is precisely cut to achieve maximum optical performance and breathtaking beauty.\",\n        image: \"/image/shaping_phase_tache-768x768.png\",\n        cta: \"View Our Process\"\n    },\n    {\n        id: \"colors\",\n        title: \"Rare Fancy Colors\",\n        subtitle: \"Nature's Spectrum, Perfected\",\n        description: \"Experience the extraordinary with our collection of fancy colored diamonds. From vivid yellows to rare pinks and blues, each colored diamond represents the pinnacle of gemological achievement.\",\n        image: \"/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png\",\n        cta: \"Explore Colors\"\n    }\n];\nfunction SimpleHome() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHome.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SimpleHome.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"full-section relative w-full h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png\",\n                        alt: \"Diamond Atelier Homepage Banner\",\n                        fill: true,\n                        className: \"absolute inset-0 w-full h-full object-cover\",\n                        priority: true,\n                        quality: 90\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full opacity-60\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight,\n                                    scale: 0\n                                },\n                                animate: {\n                                    y: [\n                                        null,\n                                        -100\n                                    ],\n                                    opacity: [\n                                        0.6,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 3,\n                                    ease: \"easeOut\"\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col items-center justify-center z-20 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat\",\n                                            style: {\n                                                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',\n                                                letterSpacing: \"0.1em\"\n                                            },\n                                            children: \"DIAMONDS THAT DESERVE YOU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 0.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center max-w-4xl mx-auto mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.3em] leading-relaxed font-montserrat\",\n                                        children: [\n                                            \" \",\n                                            \"10,000+ Certified Stones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg lg:text-xl text-white/70 font-light tracking-wide leading-relaxed font-montserrat\",\n                                        children: \"Where precision meets perfection in every facet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 1,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/shapes\",\n                                        className: \"group relative px-8 py-3 bg-transparent border border-white/30 text-white hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"EXPlORE SHAPES\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"https://inventory.diamondatelier.in/\",\n                                        className: \"group relative px-8 py-3 bg-black/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: \"VIEW INVENTORY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            delay: 1.5\n                        },\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                        onClick: ()=>setCurrentIndex(1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs tracking-widest mb-2 font-light\",\n                                    children: \"SCROLL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center justify-between gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-black\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                children: sections[0].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: sections[0].title,\n                                            level: \"h1\",\n                                            className: \"text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat\",\n                                            showUnderline: false,\n                                            animationDelay: 0.2,\n                                            triggerOnMount: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat\",\n                                            children: sections[0].description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                            children: sections[0].cta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-96 h-96 xl:w-[500px] xl:h-[500px] 2xl:w-[600px] 2xl:h-[600px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: sections[0].image,\n                                            alt: sections[0].title,\n                                            fill: true,\n                                            className: \"object-cover rounded-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                        children: sections[0].subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    text: sections[0].title,\n                                    level: \"h1\",\n                                    className: \"text-4xl sm:text-5xl mb-8 font-light leading-tight font-montserrat\",\n                                    showUnderline: false,\n                                    animationDelay: 0.2,\n                                    triggerOnMount: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-80 h-80 sm:w-96 sm:h-96\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: sections[0].image,\n                                            alt: sections[0].title,\n                                            fill: true,\n                                            className: \"object-cover rounded-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                    children: sections[0].description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                    children: sections[0].cta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            sections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center justify-between gap-16 \".concat(index % 2 === 0 ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                    children: section.subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl xl:text-5xl mb-6 font-light leading-tight font-montserrat\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 max-w-2xl font-montserrat\",\n                                                children: section.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                children: section.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-80 h-80 xl:w-96 xl:h-96\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: section.image,\n                                                alt: section.title,\n                                                fill: true,\n                                                className: \"object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden text-center text-black\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                            children: section.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl mb-6 font-light leading-tight font-montserrat\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-72 h-72 sm:w-80 sm:h-80\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: section.image,\n                                                alt: section.title,\n                                                fill: true,\n                                                className: \"object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base leading-relaxed font-light text-gray-700 mb-6 max-w-2xl mx-auto font-montserrat\",\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                        children: section.cta\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                }, section.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative w-full h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"url('/image/Expert.jpg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/40 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 drop-shadow-lg font-montserrat\",\n                                        children: \"Expert Eyes on Every Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-black mb-8 leading-relaxed drop-shadow-md font-montserrat\",\n                                        children: \"Our in-house gemologists personally inspect and verify every lab-grown diamond. You'll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\components\\\\common\\\\SimpleHome.jsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHome, \"2oCXE2WiAW05zG4oFnHELxlAGiA=\");\n_c = SimpleHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbW1vbi9TaW1wbGVIb21lLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDMkM7QUFDWjtBQUNpQjtBQUNMO0FBQ2Y7QUFDc0I7QUFFbEQsTUFBTVEsV0FBVztJQUNmLElBQUk7SUFDSixnQkFBZ0I7SUFDaEIsMkNBQTJDO0lBQzNDLDZDQUE2QztJQUM3QyxpQkFBaUI7SUFDakIseUZBQXlGO0lBQ3pGLGlEQUFpRDtJQUNqRCxvQ0FBb0M7SUFDcEMsS0FBSztJQUNMO1FBQ0VDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLGFBQ0U7UUFDRkMsT0FBTztRQUNQQyxLQUFLO0lBQ1A7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxhQUNFO1FBQ0ZDLE9BQU87UUFDUEMsS0FBSztJQUNQO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsYUFDRTtRQUNGQyxPQUNFO1FBQ0ZDLEtBQUs7SUFDUDtDQUNEO0FBRWMsU0FBU0M7O0lBQ3RCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUV6Q0MsZ0RBQVNBO2dDQUFDO1lBQ1JrQixZQUFZO1FBQ2Q7K0JBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDbkIsa0RBQUtBO3dCQUNKb0IsS0FBSTt3QkFDSkMsS0FBSTt3QkFDSkMsSUFBSTt3QkFDSkgsV0FBVTt3QkFDVkksUUFBUTt3QkFDUkMsU0FBUzs7Ozs7O2tDQUlYLDhEQUFDTjt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O29CQUdkSCwwQkFDQyw4REFBQ0U7d0JBQUlDLFdBQVU7a0NBQ1o7K0JBQUlNLE1BQU07eUJBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUN0Qiw4REFBQ3hCLGlEQUFNQSxDQUFDYyxHQUFHO2dDQUVUQyxXQUFVO2dDQUNWVSxTQUFTO29DQUNQQyxHQUFHQyxLQUFLQyxNQUFNLEtBQUtDLE9BQU9DLFVBQVU7b0NBQ3BDQyxHQUFHSixLQUFLQyxNQUFNLEtBQUtDLE9BQU9HLFdBQVc7b0NBQ3JDQyxPQUFPO2dDQUNUO2dDQUNBQyxTQUFTO29DQUNQSCxHQUFHO3dDQUFDO3dDQUFNLENBQUM7cUNBQUk7b0NBQ2ZJLFNBQVM7d0NBQUM7d0NBQUs7cUNBQUU7b0NBQ2pCRixPQUFPO3dDQUFDO3dDQUFHO3dDQUFHO3FDQUFFO2dDQUNsQjtnQ0FDQUcsWUFBWTtvQ0FDVkMsVUFBVSxJQUFJVixLQUFLQyxNQUFNLEtBQUs7b0NBQzlCVSxRQUFRQztvQ0FDUkMsT0FBT2IsS0FBS0MsTUFBTSxLQUFLO29DQUN2QmEsTUFBTTtnQ0FDUjsrQkFqQktqQjs7Ozs7Ozs7OztrQ0F3QmIsOERBQUNWO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ2YsaURBQU1BLENBQUNjLEdBQUc7Z0NBQ1RXLFNBQVM7b0NBQUVVLFNBQVM7b0NBQUdKLEdBQUc7Z0NBQUc7Z0NBQzdCRyxTQUFTO29DQUFFQyxTQUFTO29DQUFHSixHQUFHO2dDQUFFO2dDQUM1QkssWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBS0ksTUFBTTtnQ0FBVTtnQ0FDN0MxQixXQUFVOzBDQUVWLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMyQjs0Q0FDQzNCLFdBQVU7NENBQ1Y0QixPQUFPO2dEQUNMLGdGQUFnRjtnREFDaEZDLGVBQWU7NENBQ2pCO3NEQUNEOzs7Ozs7c0RBS0QsOERBQUM5Qjs0Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS25CLDhEQUFDZixpREFBTUEsQ0FBQ2MsR0FBRztnQ0FDVFcsU0FBUztvQ0FBRVUsU0FBUztvQ0FBR0osR0FBRztnQ0FBRztnQ0FDN0JHLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdKLEdBQUc7Z0NBQUU7Z0NBQzVCSyxZQUFZO29DQUFFQyxVQUFVO29DQUFLRyxPQUFPO29DQUFLQyxNQUFNO2dDQUFVO2dDQUN6RDFCLFdBQVU7O2tEQUVWLDhEQUFDMkI7d0NBQUczQixXQUFVOzs0Q0FLTDs0Q0FBSTs7Ozs7OztrREFHYiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQzhCO3dDQUFFOUIsV0FBVTtrREFBb0g7Ozs7Ozs7Ozs7OzswQ0FNbkksOERBQUNmLGlEQUFNQSxDQUFDYyxHQUFHO2dDQUNUVyxTQUFTO29DQUFFVSxTQUFTO29DQUFHSixHQUFHO2dDQUFHO2dDQUM3QkcsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0osR0FBRztnQ0FBRTtnQ0FDNUJLLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUtHLE9BQU87b0NBQUdDLE1BQU07Z0NBQVU7Z0NBQ3ZEMUIsV0FBVTs7a0RBRVYsOERBQUNoQixrREFBSUE7d0NBQ0grQyxNQUFLO3dDQUNML0IsV0FBVTs7MERBRVYsOERBQUNnQztnREFBS2hDLFdBQVU7MERBQWdCOzs7Ozs7MERBQ2hDLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUdqQiw4REFBQ2hCLGtEQUFJQTt3Q0FDSCtDLE1BQUs7d0NBQ0wvQixXQUFVO2tEQUVWLDRFQUFDZ0M7NENBQUtoQyxXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXRDLDhEQUFDZixpREFBTUEsQ0FBQ2MsR0FBRzt3QkFDVFcsU0FBUzs0QkFBRVUsU0FBUzt3QkFBRTt3QkFDdEJELFNBQVM7NEJBQUVDLFNBQVM7d0JBQUU7d0JBQ3RCQyxZQUFZOzRCQUFFQyxVQUFVOzRCQUFLRyxPQUFPO3dCQUFJO3dCQUN4Q3pCLFdBQVU7d0JBQ1ZpQyxTQUFTLElBQU1DLGdCQUFnQjtrQ0FFL0IsNEVBQUNqRCxpREFBTUEsQ0FBQ2MsR0FBRzs0QkFDVG9CLFNBQVM7Z0NBQUVILEdBQUc7b0NBQUM7b0NBQUc7b0NBQUc7aUNBQUU7NEJBQUM7NEJBQ3hCSyxZQUFZO2dDQUFFQyxVQUFVO2dDQUFHQyxRQUFRQztnQ0FBVUUsTUFBTTs0QkFBWTs0QkFDL0QxQixXQUFVOzs4Q0FFViw4REFBQ2dDO29DQUFLaEMsV0FBVTs4Q0FBMEM7Ozs7Ozs4Q0FHMUQsOERBQUNqQix1RkFBV0E7b0NBQUNpQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLN0IsOERBQUNtQztnQkFBUW5DLFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2dDO2dEQUFLaEMsV0FBVTswREFDYmIsUUFBUSxDQUFDLEVBQUUsQ0FBQ0csUUFBUTs7Ozs7Ozs7Ozs7c0RBR3pCLDhEQUFDUix3REFBZUE7NENBQ2RzRCxNQUFNakQsUUFBUSxDQUFDLEVBQUUsQ0FBQ0UsS0FBSzs0Q0FDdkJnRCxPQUFNOzRDQUNOckMsV0FBVTs0Q0FDVnNDLGVBQWU7NENBQ2ZDLGdCQUFnQjs0Q0FDaEJDLGdCQUFnQjs7Ozs7O3NEQUVsQiw4REFBQ1Y7NENBQUU5QixXQUFVO3NEQUNWYixRQUFRLENBQUMsRUFBRSxDQUFDSSxXQUFXOzs7Ozs7c0RBRTFCLDhEQUFDa0Q7NENBQU96QyxXQUFVO3NEQUNmYixRQUFRLENBQUMsRUFBRSxDQUFDTSxHQUFHOzs7Ozs7Ozs7Ozs7OENBS3BCLDhEQUFDTTtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNuQixrREFBS0E7NENBQ0pvQixLQUFLZCxRQUFRLENBQUMsRUFBRSxDQUFDSyxLQUFLOzRDQUN0QlUsS0FBS2YsUUFBUSxDQUFDLEVBQUUsQ0FBQ0UsS0FBSzs0Q0FDdEJjLElBQUk7NENBQ0pILFdBQVU7NENBQ1ZJLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2hCLDhEQUFDTDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDZ0M7d0NBQUtoQyxXQUFVO2tEQUNiYixRQUFRLENBQUMsRUFBRSxDQUFDRyxRQUFROzs7Ozs7Ozs7Ozs4Q0FHekIsOERBQUNSLHdEQUFlQTtvQ0FDZHNELE1BQU1qRCxRQUFRLENBQUMsRUFBRSxDQUFDRSxLQUFLO29DQUN2QmdELE9BQU07b0NBQ05yQyxXQUFVO29DQUNWc0MsZUFBZTtvQ0FDZkMsZ0JBQWdCO29DQUNoQkMsZ0JBQWdCOzs7Ozs7OENBRWxCLDhEQUFDekM7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDbkIsa0RBQUtBOzRDQUNKb0IsS0FBS2QsUUFBUSxDQUFDLEVBQUUsQ0FBQ0ssS0FBSzs0Q0FDdEJVLEtBQUtmLFFBQVEsQ0FBQyxFQUFFLENBQUNFLEtBQUs7NENBQ3RCYyxJQUFJOzRDQUNKSCxXQUFVOzRDQUNWSSxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OzhDQUlkLDhEQUFDMEI7b0NBQUU5QixXQUFVOzhDQUNWYixRQUFRLENBQUMsRUFBRSxDQUFDSSxXQUFXOzs7Ozs7OENBRTFCLDhEQUFDa0Q7b0NBQU96QyxXQUFVOzhDQUNmYixRQUFRLENBQUMsRUFBRSxDQUFDTSxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU92Qk4sU0FBU3VELEtBQUssQ0FBQyxHQUFHbkMsR0FBRyxDQUFDLENBQUM0QixTQUFTUSxzQkFDL0IsOERBQUNSO29CQUVDbkMsV0FBVTs4QkFFViw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FDQ0MsV0FBVyxzREFFVixPQURDMkMsUUFBUSxNQUFNLElBQUkscUJBQXFCOztrREFJekMsOERBQUM1Qzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDZ0M7b0RBQUtoQyxXQUFVOzhEQUNibUMsUUFBUTdDLFFBQVE7Ozs7Ozs7Ozs7OzBEQUdyQiw4REFBQ3NEO2dEQUFHNUMsV0FBVTswREFDWG1DLFFBQVE5QyxLQUFLOzs7Ozs7MERBRWhCLDhEQUFDeUM7Z0RBQUU5QixXQUFVOzBEQUNWbUMsUUFBUTVDLFdBQVc7Ozs7OzswREFFdEIsOERBQUNrRDtnREFBT3pDLFdBQVU7MERBQ2ZtQyxRQUFRMUMsR0FBRzs7Ozs7Ozs7Ozs7O2tEQUtoQiw4REFBQ007d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDbkIsa0RBQUtBO2dEQUNKb0IsS0FBS2tDLFFBQVEzQyxLQUFLO2dEQUNsQlUsS0FBS2lDLFFBQVE5QyxLQUFLO2dEQUNsQmMsSUFBSTtnREFDSkgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPbEIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNnQzs0Q0FBS2hDLFdBQVU7c0RBQ2JtQyxRQUFRN0MsUUFBUTs7Ozs7Ozs7Ozs7a0RBR3JCLDhEQUFDc0Q7d0NBQUc1QyxXQUFVO2tEQUNYbUMsUUFBUTlDLEtBQUs7Ozs7OztrREFFaEIsOERBQUNVO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ25CLGtEQUFLQTtnREFDSm9CLEtBQUtrQyxRQUFRM0MsS0FBSztnREFDbEJVLEtBQUtpQyxRQUFROUMsS0FBSztnREFDbEJjLElBQUk7Z0RBQ0pILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSWhCLDhEQUFDOEI7d0NBQUU5QixXQUFVO2tEQUNWbUMsUUFBUTVDLFdBQVc7Ozs7OztrREFFdEIsOERBQUNrRDt3Q0FBT3pDLFdBQVU7a0RBQ2ZtQyxRQUFRMUMsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21CQWpFYjBDLFFBQVEvQyxFQUFFOzs7OzswQkF3RW5CLDhEQUFDK0M7Z0JBQVFuQyxXQUFVOztrQ0FDakIsOERBQUNEO3dCQUNDQyxXQUFVO3dCQUNWNEIsT0FBTzs0QkFDTGlCLGlCQUFpQjt3QkFDbkI7Ozs7OztrQ0FFRiw4REFBQzlDO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDNEM7d0NBQUc1QyxXQUFVO2tEQUE0Rjs7Ozs7O2tEQUcxRyw4REFBQzhCO3dDQUFFOUIsV0FBVTtrREFBeUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZcEc7R0FsVXdCTjtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pblxcRGVza3RvcFxcTmV3IGZvbGRlclxcTmV3IGZvbGRlclxcZGlhbW9uZC1hdGVsaWVyLXdlYnNpdGUtdXBkYXRlLW1hc3Rlclxcc3JjXFxjb21wb25lbnRzXFxjb21tb25cXFNpbXBsZUhvbWUuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgdXNlU3RhdGUsdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCBBbmltYXRlZEhlYWRpbmcgZnJvbSBcIi4vQW5pbWF0ZWRIZWFkaW5nXCI7XG5pbXBvcnQgeyBDaGV2cm9uRG93biB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIlxuaW1wb3J0IHsgbW90aW9uLCB1c2VJblZpZXcgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuXG5jb25zdCBzZWN0aW9ucyA9IFtcbiAgLy8ge1xuICAvLyAgIGlkOiBcImhlcm9cIixcbiAgLy8gICB0aXRsZTogXCJFeHF1aXNpdGUgTGFiIEdyb3duIERpYW1vbmRzXCIsXG4gIC8vICAgc3VidGl0bGU6IFwiRXZlcnkgRGlhbW9uZCBUZWxscyBhIFN0b3J5XCIsXG4gIC8vICAgZGVzY3JpcHRpb246XG4gIC8vICAgICBcIkV0aGljYWxseSBzb3VyY2VkLCBjZXJ0aWZpZWQgZGlhbW9uZHMgd2l0aCB1bnBhcmFsbGVsZWQgcXVhbGl0eSBhbmQgYnJpbGxpYW5jZS5cIixcbiAgLy8gICBpbWFnZTogXCIvaW1hZ2UvdGFjaGVfZGlhbW9uZF9yb3VnaF9jdXQucG5nXCIsXG4gIC8vICAgY3RhOiBcIkRpc2NvdmVyIE91ciBDb2xsZWN0aW9uXCIsXG4gIC8vIH0sXG4gIHtcbiAgICBpZDogXCJzaGFwZXNcIixcbiAgICB0aXRsZTogXCJJY29uaWMgRGlhbW9uZCBDdXRzXCIsXG4gICAgc3VidGl0bGU6IFwiVGltZWxlc3MgRWxlZ2FuY2UsIE1vZGVybiBQcmVjaXNpb25cIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiRnJvbSB0aGUgY2xhc3NpYyBicmlsbGlhbmNlIG9mIHJvdW5kIGN1dHMgdG8gdGhlIGNvbnRlbXBvcmFyeSBhbGx1cmUgb2YgZW1lcmFsZCBhbmQgY3VzaGlvbiBzaGFwZXMuIEVhY2ggY3V0IGlzIG1hc3RlcmZ1bGx5IGV4ZWN1dGVkIHRvIG1heGltaXplIGZpcmUsIGJyaWxsaWFuY2UsIGFuZCBzY2ludGlsbGF0aW9uLlwiLFxuICAgIGltYWdlOiBcIi9pbWFnZS90YWNoZV9sZXNvdGhvX2ljb24tNzY4eDc1Mi5qcGdcIixcbiAgICBjdGE6IFwiRGlzY292ZXIgU2hhcGVzXCIsXG4gIH0sXG4gIHtcbiAgICBpZDogXCJjcmFmdHNtYW5zaGlwXCIsXG4gICAgdGl0bGU6IFwiQXJ0aXNhbmFsIEV4Y2VsbGVuY2VcIixcbiAgICBzdWJ0aXRsZTogXCJIZXJpdGFnZSBDcmFmdHNtYW5zaGlwLCBSZXZvbHV0aW9uYXJ5IFRlY2hub2xvZ3lcIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiT3VyIG1hc3RlciBjcmFmdHNtZW4gYmxlbmQgY2VudHVyaWVzLW9sZCB0ZWNobmlxdWVzIHdpdGggY3V0dGluZy1lZGdlIHRlY2hub2xvZ3ksIGNyZWF0aW5nIGRpYW1vbmRzIHRoYXQgcml2YWwgbmF0dXJlJ3MgZmluZXN0LiBFdmVyeSBmYWNldCBpcyBwcmVjaXNlbHkgY3V0IHRvIGFjaGlldmUgbWF4aW11bSBvcHRpY2FsIHBlcmZvcm1hbmNlIGFuZCBicmVhdGh0YWtpbmcgYmVhdXR5LlwiLFxuICAgIGltYWdlOiBcIi9pbWFnZS9zaGFwaW5nX3BoYXNlX3RhY2hlLTc2OHg3NjgucG5nXCIsXG4gICAgY3RhOiBcIlZpZXcgT3VyIFByb2Nlc3NcIixcbiAgfSxcbiAge1xuICAgIGlkOiBcImNvbG9yc1wiLFxuICAgIHRpdGxlOiBcIlJhcmUgRmFuY3kgQ29sb3JzXCIsXG4gICAgc3VidGl0bGU6IFwiTmF0dXJlJ3MgU3BlY3RydW0sIFBlcmZlY3RlZFwiLFxuICAgIGRlc2NyaXB0aW9uOlxuICAgICAgXCJFeHBlcmllbmNlIHRoZSBleHRyYW9yZGluYXJ5IHdpdGggb3VyIGNvbGxlY3Rpb24gb2YgZmFuY3kgY29sb3JlZCBkaWFtb25kcy4gRnJvbSB2aXZpZCB5ZWxsb3dzIHRvIHJhcmUgcGlua3MgYW5kIGJsdWVzLCBlYWNoIGNvbG9yZWQgZGlhbW9uZCByZXByZXNlbnRzIHRoZSBwaW5uYWNsZSBvZiBnZW1vbG9naWNhbCBhY2hpZXZlbWVudC5cIixcbiAgICBpbWFnZTpcbiAgICAgIFwiL2ltYWdlcy9Db2xvcnMvbmF0dXJhbC1jb2xvci1kbWRzLWNvbG9yLWRpYW1vbmRzLXJvdWdoLWRpYW1vbmRzLXJlbW92ZWJnLXByZXZpZXcucG5nXCIsXG4gICAgY3RhOiBcIkV4cGxvcmUgQ29sb3JzXCIsXG4gIH0sXG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaW1wbGVIb21lKCkge1xuICBjb25zdCBbYWN0aXZlU2VjdGlvbiwgc2V0QWN0aXZlU2VjdGlvbl0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRJc0NsaWVudCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBtaW4taC1zY3JlZW4gcHQtMjBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZnVsbC1zZWN0aW9uIHJlbGF0aXZlIHctZnVsbCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgey8qIEJhY2tncm91bmQgSW1hZ2UgKi99XG4gICAgICAgIDxJbWFnZVxuICAgICAgICAgIHNyYz1cIi9pbWFnZS9Ib21lcGFnZV9CYW5uZXJfMjAyNV9EZXNrdG9wX2MwZGRmYTgxOTQucG5nXCJcbiAgICAgICAgICBhbHQ9XCJEaWFtb25kIEF0ZWxpZXIgSG9tZXBhZ2UgQmFubmVyXCJcbiAgICAgICAgICBmaWxsXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgcHJpb3JpdHlcbiAgICAgICAgICBxdWFsaXR5PXs5MH1cbiAgICAgICAgLz5cblxuICAgICAgICB7LyogU29waGlzdGljYXRlZCBPdmVybGF5IEdyYWRpZW50cyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibGFjay8zMCB2aWEtdHJhbnNwYXJlbnQgdG8tYmxhY2svNjAgcG9pbnRlci1ldmVudHMtbm9uZVwiIC8+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tYmxhY2svMjAgdmlhLXRyYW5zcGFyZW50IHRvLWJsYWNrLzIwIHBvaW50ZXItZXZlbnRzLW5vbmVcIiAvPlxuXG4gICAgICAgIHsvKiBBbmltYXRlZCBQYXJ0aWNsZXMvU3BhcmtsZXMgKi99XG4gICAgICAgIHtpc0NsaWVudCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG92ZXJmbG93LWhpZGRlbiBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICB7Wy4uLkFycmF5KDEyKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAga2V5PXtpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHctMSBoLTEgYmctd2hpdGUgcm91bmRlZC1mdWxsIG9wYWNpdHktNjBcIlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3tcbiAgICAgICAgICAgICAgICAgIHg6IE1hdGgucmFuZG9tKCkgKiB3aW5kb3cuaW5uZXJXaWR0aCxcbiAgICAgICAgICAgICAgICAgIHk6IE1hdGgucmFuZG9tKCkgKiB3aW5kb3cuaW5uZXJIZWlnaHQsXG4gICAgICAgICAgICAgICAgICBzY2FsZTogMCxcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgICAgICAgIHk6IFtudWxsLCAtMTAwXSxcbiAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IFswLjYsIDBdLFxuICAgICAgICAgICAgICAgICAgc2NhbGU6IFswLCAxLCAwXSxcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzICsgTWF0aC5yYW5kb20oKSAqIDIsXG4gICAgICAgICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgICAgICAgZGVsYXk6IE1hdGgucmFuZG9tKCkgKiAzLFxuICAgICAgICAgICAgICAgICAgZWFzZTogXCJlYXNlT3V0XCIsXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgQ29udGFpbmVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei0yMCBweC02XCI+XG4gICAgICAgICAgey8qIExvZ28gU2VjdGlvbiAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiA1MCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAxLjUsIGVhc2U6IFwiZWFzZU91dFwiIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi04XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgIDxoMVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtMnhsIHNtOnRleHQtMnhsIG1kOnRleHQtM3hsIGxnOnRleHQtNHhsIHhsOnRleHQtNXhsIGZvbnQtbGlnaHQgbGVhZGluZy10aWdodCB0cmFja2luZy1bMC4yZW1dIHRleHQtYmxhY2svOTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNzAwIGZvbnQtbW9udHNlcnJhdFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIC8vIHRleHRTaGFkb3c6ICcwIDAgNDBweCByZ2JhKDI1NSwyNTUsMjU1LDAuNiksIDAgMCA4MHB4IHJnYmEoMjU1LDI1NSwyNTUsMC4zKScsXG4gICAgICAgICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiBcIjAuMWVtXCIsXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIERJQU1PTkRTIFRIQVQgREVTRVJWRSBZT1VcbiAgICAgICAgICAgICAgPC9oMT5cblxuICAgICAgICAgICAgICB7LyogUHJlbWl1bSBHbG93IEVmZmVjdCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLXIgZnJvbS10cmFuc3BhcmVudCB2aWEtYmxhY2svMTAgdG8tdHJhbnNwYXJlbnQgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi03MDAgYmx1ci0zeGxcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIHsvKiBFbGVnYW50IFRhZ2xpbmUgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMS41LCBkZWxheTogMC41LCBlYXNlOiBcImVhc2VPdXRcIiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWF4LXctNHhsIG14LWF1dG8gbWItMTJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LWxnIHNtOnRleHQteGwgbWQ6dGV4dC0yeGwgbGc6dGV4dC0yeGwgeGw6dGV4dC0zeGwgZm9udC1saWdodCB0ZXh0LWJsYWNrLzkwIHRyYWNraW5nLVswLjNlbV0gbGVhZGluZy1yZWxheGVkIGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICB7LyogPFBlcm1hbmVudENvdW50ZXJcbiAgICAgICAgICAgICAgICB0YXJnZXROdW1iZXI9ezEwMDAwfVxuICAgICAgICAgICAgICAgIHN1ZmZpeD1cIisgQ0VSVElGSUVEIFNUT05FU1wiXG4gICAgICAgICAgICAgICAgZHVyYXRpb249ezIwMDB9XG4gICAgICAgICAgICAgIC8+ICovfXtcIiBcIn1cbiAgICAgICAgICAgICAgMTAsMDAwKyBDZXJ0aWZpZWQgU3RvbmVzXG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTI0IGgtcHggYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS1ibGFjay82MCB0by10cmFuc3BhcmVudCBteC1hdXRvIG10LTYgbWItNlwiPjwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgbWQ6dGV4dC1sZyBsZzp0ZXh0LXhsIHRleHQtd2hpdGUvNzAgZm9udC1saWdodCB0cmFja2luZy13aWRlIGxlYWRpbmctcmVsYXhlZCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgV2hlcmUgcHJlY2lzaW9uIG1lZXRzIHBlcmZlY3Rpb24gaW4gZXZlcnkgZmFjZXRcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogQ2FsbCB0byBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAxLjUsIGRlbGF5OiAxLCBlYXNlOiBcImVhc2VPdXRcIiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBzbTpnYXAtNlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9zaGFwZXNcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBweC04IHB5LTMgYmctdHJhbnNwYXJlbnQgYm9yZGVyIGJvcmRlci13aGl0ZS8zMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlIGhvdmVyOnRleHQtYmxhY2sgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRleHQtc20gc206dGV4dC1iYXNlIHRyYWNraW5nLXdpZGVyIGZvbnQtbGlnaHQgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMFwiPkVYUGxPUkUgU0hBUEVTPC9zcGFuPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctd2hpdGUgdHJhbnNmb3JtIHNjYWxlLXgtMCBncm91cC1ob3ZlcjpzY2FsZS14LTEwMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi01MDAgb3JpZ2luLWxlZnRcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cImh0dHBzOi8vaW52ZW50b3J5LmRpYW1vbmRhdGVsaWVyLmluL1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIHJlbGF0aXZlIHB4LTggcHktMyBiZy1ibGFjay8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdHJhY2tpbmctd2lkZXIgZm9udC1saWdodFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5WSUVXIElOVkVOVE9SWTwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTY3JvbGwgSW5kaWNhdG9yICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEuNSwgZGVsYXk6IDEuNSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS04IGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIHotMTAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRJbmRleCgxKX1cbiAgICAgICAgPlxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBhbmltYXRlPXt7IHk6IFswLCA4LCAwXSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMiwgcmVwZWF0OiBJbmZpbml0eSwgZWFzZTogXCJlYXNlSW5PdXRcIiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgdGV4dC13aGl0ZS82MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0cmFja2luZy13aWRlc3QgbWItMiBmb250LWxpZ2h0XCI+XG4gICAgICAgICAgICAgIFNDUk9MTFxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTQgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgICAgey8qIERlc2t0b3AgTGF5b3V0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBnYXAtMTZcIj5cbiAgICAgICAgICAgIHsvKiBMZWZ0IC0gVGV4dCBDb250ZW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1ibGFja1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gdXBwZXJjYXNlIHRyYWNraW5nLVswLjJlbV0gZm9udC1saWdodCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWN0aW9uc1swXS5zdWJ0aXRsZX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QW5pbWF0ZWRIZWFkaW5nXG4gICAgICAgICAgICAgICAgdGV4dD17c2VjdGlvbnNbMF0udGl0bGV9XG4gICAgICAgICAgICAgICAgbGV2ZWw9XCJoMVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC01eGwgeGw6dGV4dC02eGwgMnhsOnRleHQtN3hsIG1iLTggZm9udC1saWdodCBsZWFkaW5nLXRpZ2h0IGZvbnQtbW9udHNlcnJhdFwiXG4gICAgICAgICAgICAgICAgc2hvd1VuZGVybGluZT17ZmFsc2V9XG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk9ezAuMn1cbiAgICAgICAgICAgICAgICB0cmlnZ2VyT25Nb3VudD17dHJ1ZX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB4bDp0ZXh0LTJ4bCBsZWFkaW5nLXJlbGF4ZWQgZm9udC1saWdodCB0ZXh0LWdyYXktNzAwIG1iLTEyIG1heC13LTJ4bCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbnNbMF0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJiZy1ibGFjayB0ZXh0LXdoaXRlIHB4LTggcHktNCB0ZXh0LWxnIGZvbnQtbWVkaXVtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4xZW1dIGhvdmVyOmJnLWdyYXktODAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbnNbMF0uY3RhfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUmlnaHQgLSBJbWFnZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTk2IGgtOTYgeGw6dy1bNTAwcHhdIHhsOmgtWzUwMHB4XSAyeGw6dy1bNjAwcHhdIDJ4bDpoLVs2MDBweF1cIj5cbiAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgIHNyYz17c2VjdGlvbnNbMF0uaW1hZ2V9XG4gICAgICAgICAgICAgICAgICBhbHQ9e3NlY3Rpb25zWzBdLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgZmlsbFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyIHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgICBwcmlvcml0eVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTW9iaWxlIExheW91dCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiB0ZXh0LWNlbnRlciB0ZXh0LWJsYWNrXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4yZW1dIGZvbnQtbGlnaHQgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAge3NlY3Rpb25zWzBdLnN1YnRpdGxlfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxBbmltYXRlZEhlYWRpbmdcbiAgICAgICAgICAgICAgdGV4dD17c2VjdGlvbnNbMF0udGl0bGV9XG4gICAgICAgICAgICAgIGxldmVsPVwiaDFcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBzbTp0ZXh0LTV4bCBtYi04IGZvbnQtbGlnaHQgbGVhZGluZy10aWdodCBmb250LW1vbnRzZXJyYXRcIlxuICAgICAgICAgICAgICBzaG93VW5kZXJsaW5lPXtmYWxzZX1cbiAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk9ezAuMn1cbiAgICAgICAgICAgICAgdHJpZ2dlck9uTW91bnQ9e3RydWV9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04IGZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTgwIGgtODAgc206dy05NiBzbTpoLTk2XCI+XG4gICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICBzcmM9e3NlY3Rpb25zWzBdLmltYWdlfVxuICAgICAgICAgICAgICAgICAgYWx0PXtzZWN0aW9uc1swXS50aXRsZX1cbiAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlciByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgcHJpb3JpdHlcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBsZWFkaW5nLXJlbGF4ZWQgZm9udC1saWdodCB0ZXh0LWdyYXktNzAwIG1iLTggbWF4LXctMnhsIG14LWF1dG8gZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgIHtzZWN0aW9uc1swXS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYmctYmxhY2sgdGV4dC13aGl0ZSBweC04IHB5LTQgdGV4dC1sZyBmb250LW1lZGl1bSB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMWVtXSBob3ZlcjpiZy1ncmF5LTgwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgIHtzZWN0aW9uc1swXS5jdGF9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBPdGhlciBTZWN0aW9ucyAqL31cbiAgICAgIHtzZWN0aW9ucy5zbGljZSgxKS5tYXAoKHNlY3Rpb24sIGluZGV4KSA9PiAoXG4gICAgICAgIDxzZWN0aW9uXG4gICAgICAgICAga2V5PXtzZWN0aW9uLmlkfVxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC00IGxnOnB4LTggYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIHsvKiBEZXNrdG9wIExheW91dCAtIEFsdGVybmF0aW5nICovfVxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdhcC0xNiAke1xuICAgICAgICAgICAgICAgIGluZGV4ICUgMiA9PT0gMCA/IFwiZmxleC1yb3ctcmV2ZXJzZVwiIDogXCJcIlxuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIFRleHQgQ29udGVudCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1ibGFja1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4yZW1dIGZvbnQtbGlnaHQgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLnN1YnRpdGxlfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCB4bDp0ZXh0LTV4bCBtYi02IGZvbnQtbGlnaHQgbGVhZGluZy10aWdodCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB4bDp0ZXh0LXhsIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0IHRleHQtZ3JheS03MDAgbWItOCBtYXgtdy0yeGwgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgICB7c2VjdGlvbi5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWJsYWNrIHRleHQtYmxhY2sgcHgtOCBweS0zIHRleHQtYmFzZSBmb250LW1lZGl1bSB1cHBlcmNhc2UgdHJhY2tpbmctWzAuMWVtXSBob3ZlcjpiZy1ibGFjayBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWN0aW9uLmN0YX1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEltYWdlICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTgwIGgtODAgeGw6dy05NiB4bDpoLTk2XCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtzZWN0aW9uLmltYWdlfVxuICAgICAgICAgICAgICAgICAgICBhbHQ9e3NlY3Rpb24udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyIHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTW9iaWxlIExheW91dCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHRleHQtY2VudGVyIHRleHQtYmxhY2tcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4yZW1dIGZvbnQtbGlnaHQgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgICB7c2VjdGlvbi5zdWJ0aXRsZX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgc206dGV4dC00eGwgbWItNiBmb250LWxpZ2h0IGxlYWRpbmctdGlnaHQgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAge3NlY3Rpb24udGl0bGV9XG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBmbGV4IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTcyIGgtNzIgc206dy04MCBzbTpoLTgwXCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtzZWN0aW9uLmltYWdlfVxuICAgICAgICAgICAgICAgICAgICBhbHQ9e3NlY3Rpb24udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyIHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0IHRleHQtZ3JheS03MDAgbWItNiBtYXgtdy0yeGwgbXgtYXV0byBmb250LW1vbnRzZXJyYXRcIj5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbi5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItYmxhY2sgdGV4dC1ibGFjayBweC04IHB5LTMgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIHVwcGVyY2FzZSB0cmFja2luZy1bMC4xZW1dIGhvdmVyOmJnLWJsYWNrIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgIHtzZWN0aW9uLmN0YX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuICAgICAgKSl9XG5cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBoLTk2IG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LWZ1bGwgaC1mdWxsIGJnLWNvdmVyIGJnLWNlbnRlciBiZy1uby1yZXBlYXRcIlxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6IFwidXJsKCcvaW1hZ2UvRXhwZXJ0LmpwZycpXCIsXG4gICAgICAgICAgfX1cbiAgICAgICAgPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsYWNrLzQwIHRvLXRyYW5zcGFyZW50XCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBoLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHctZnVsbFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0yeGxcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIHNtOnRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWJsYWNrIG1iLTYgZHJvcC1zaGFkb3ctbGcgZm9udC1tb250c2VycmF0XCI+XG4gICAgICAgICAgICAgICAgRXhwZXJ0IEV5ZXMgb24gRXZlcnkgU3RvbmVcbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWJsYWNrIG1iLTggbGVhZGluZy1yZWxheGVkIGRyb3Atc2hhZG93LW1kIGZvbnQtbW9udHNlcnJhdFwiPlxuICAgICAgICAgICAgICAgIE91ciBpbi1ob3VzZSBnZW1vbG9naXN0cyBwZXJzb25hbGx5IGluc3BlY3QgYW5kIHZlcmlmeSBldmVyeVxuICAgICAgICAgICAgICAgIGxhYi1ncm93biBkaWFtb25kLiBZb3UmYXBvcztsbCByZWNlaXZlIGEgZGV0YWlsZWQgcmVwb3J0XG4gICAgICAgICAgICAgICAgY292ZXJpbmcgYnJpbGxpYW5jZSwgY3V0LCBhbmQgcXVhbGl0eSDigJQgZmFyIGJleW9uZCB3aGF0IGFcbiAgICAgICAgICAgICAgICBjZXJ0aWZpY2F0ZSBhbG9uZSBjYW4gc2hvdy5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSW1hZ2UiLCJBbmltYXRlZEhlYWRpbmciLCJDaGV2cm9uRG93biIsIkxpbmsiLCJtb3Rpb24iLCJ1c2VJblZpZXciLCJzZWN0aW9ucyIsImlkIiwidGl0bGUiLCJzdWJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaW1hZ2UiLCJjdGEiLCJTaW1wbGVIb21lIiwiYWN0aXZlU2VjdGlvbiIsInNldEFjdGl2ZVNlY3Rpb24iLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwic3JjIiwiYWx0IiwiZmlsbCIsInByaW9yaXR5IiwicXVhbGl0eSIsIkFycmF5IiwibWFwIiwiXyIsImkiLCJpbml0aWFsIiwieCIsIk1hdGgiLCJyYW5kb20iLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwieSIsImlubmVySGVpZ2h0Iiwic2NhbGUiLCJhbmltYXRlIiwib3BhY2l0eSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZGVsYXkiLCJlYXNlIiwiaDEiLCJzdHlsZSIsImxldHRlclNwYWNpbmciLCJwIiwiaHJlZiIsInNwYW4iLCJvbkNsaWNrIiwic2V0Q3VycmVudEluZGV4Iiwic2VjdGlvbiIsInRleHQiLCJsZXZlbCIsInNob3dVbmRlcmxpbmUiLCJhbmltYXRpb25EZWxheSIsInRyaWdnZXJPbk1vdW50IiwiYnV0dG9uIiwic2xpY2UiLCJpbmRleCIsImgyIiwiYmFja2dyb3VuZEltYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/SimpleHome.jsx\n"));

/***/ })

});