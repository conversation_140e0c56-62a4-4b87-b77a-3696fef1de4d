/**
 * Memory Cleanup Utilities
 * 
 * Utilities to help reduce memory usage and force garbage collection
 */

/**
 * Force garbage collection (Chrome DevTools only)
 */
export const forceGarbageCollection = () => {
  if (typeof window !== 'undefined' && window.gc) {
    try {
      window.gc();
      console.log('🗑️ Forced garbage collection');
    } catch (e) {
      console.warn('Garbage collection not available');
    }
  }
};

/**
 * Clear all intervals and timeouts
 */
export const clearAllTimers = () => {
  // Clear all timeouts
  let timeoutId = setTimeout(() => {}, 0);
  for (let i = 0; i <= timeoutId; i++) {
    clearTimeout(i);
  }
  
  // Clear all intervals
  let intervalId = setInterval(() => {}, 0);
  for (let i = 0; i <= intervalId; i++) {
    clearInterval(i);
  }
  
  console.log('🧹 Cleared all timers');
};

/**
 * Remove all event listeners from window
 */
export const cleanupEventListeners = () => {
  if (typeof window !== 'undefined') {
    // Clone window to remove all listeners
    const newWindow = window.cloneNode ? window.cloneNode(true) : window;
    
    // Common events to clean up
    const events = ['scroll', 'resize', 'mousemove', 'click', 'touchstart', 'touchmove'];
    
    events.forEach(event => {
      try {
        window.removeEventListener(event, () => {});
      } catch (e) {
        // Ignore errors
      }
    });
    
    console.log('🧹 Cleaned up event listeners');
  }
};

/**
 * Memory usage reporter
 */
export const reportMemoryUsage = (label = 'Memory Usage') => {
  if (typeof window !== 'undefined' && 'memory' in performance) {
    const memory = performance.memory;
    const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
    const total = Math.round(memory.totalJSHeapSize / 1024 / 1024);
    const limit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
    
    console.log(`💾 ${label}:`, {
      'Used': `${used}MB`,
      'Total': `${total}MB`,
      'Limit': `${limit}MB`,
      'Usage': `${Math.round((used / limit) * 100)}%`
    });
    
    return { used, total, limit };
  }
  return null;
};

/**
 * Complete memory cleanup
 */
export const performMemoryCleanup = () => {
  console.log('🧹 Starting memory cleanup...');
  
  // Clear timers
  clearAllTimers();
  
  // Force garbage collection
  forceGarbageCollection();
  
  // Report memory usage
  setTimeout(() => {
    reportMemoryUsage('After Cleanup');
  }, 1000);
};

/**
 * Memory monitoring hook
 */
export const useMemoryMonitor = (interval = 30000) => {
  if (typeof window === 'undefined') return;
  
  const monitor = setInterval(() => {
    const memory = reportMemoryUsage('Memory Monitor');
    
    // Alert if memory usage is too high
    if (memory && memory.used > 150) {
      console.warn('⚠️ High memory usage detected:', memory.used + 'MB');
      console.log('💡 Consider running performMemoryCleanup()');
    }
  }, interval);
  
  return () => clearInterval(monitor);
};

export default {
  forceGarbageCollection,
  clearAllTimers,
  cleanupEventListeners,
  reportMemoryUsage,
  performMemoryCleanup,
  useMemoryMonitor
};
