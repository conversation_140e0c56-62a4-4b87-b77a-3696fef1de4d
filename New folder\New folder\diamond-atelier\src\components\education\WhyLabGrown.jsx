// -
// "use client";
// import React from "react";
// import Image from "next/image";
// import { motion } from "framer-motion";
// import volcano from "../../../public/images/about/volcano.png";
// import img1 from "../../../public/images/about/img1.png";
// import img2 from "../../../public/images/about/img2.png";
// import img3 from "../../../public/images/about/img3.png";
// import img4 from "../../../public/images/about/img4.png";
// import img5 from "../../../public/images/about/img5.png";

// const fadeLeft = {
//   hidden: { opacity: 0, x: -50 },
//   visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: "easeOut" } },
// };

// const fadeRight = {
//   hidden: { opacity: 0, x: 50 },
//   visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: "easeOut" } },
// };

// const fadeUpText = {
//   hidden: { opacity: 0, y: 40 },
//   visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
// };

// const textContainer = {
//   visible: { transition: { staggerChildren: 0.15 } },
// };

// const list = [
//   {
//     id: 1,
//     media: img1,
//     title: "LESSER PRICE",
//     description: "Mined Diamonds can trade 15 hands and travel across 3 continents before reaching the consumer...",
//     imagePosition: "right",
//   },
//   {
//     id: 2,
//     media: img2,
//     title: "NO CHILD LABOUR",
//     description: "Children are considered cheap labour and often employed in mining...",
//     imagePosition: "left",
//   },
//   {
//     id: 3,
//     media: img3,
//     title: "RISE IN EMPLOYMENT",
//     description: "Lab-grown diamond companies ensure secure jobs in both cutting and polishing...",
//     imagePosition: "right",
//   },
//   {
//     id: 4,
//     media: img4,
//     title: "ECO FRIENDLY",
//     description: "Lab grown diamonds don’t require digging or excavation...",
//     imagePosition: "left",
//   },
//   {
//     id: 5,
//     media: img5,
//     title: "CONFLICT FREE",
//     description: "Mined diamonds are often tainted by violence, poverty, and environmental harm...",
//     imagePosition: "right",
//   },
// ];

// const WhyLabGrown = () => {
//   return (
//     <div className="bg-white text-black">
//       {/* Hero Section */}
//       <section className="bg-black text-white flex flex-col items-center justify-center px-4 pt-24 pb-12 sm:pt-28 xl:pt-36">
//         <motion.h1
//           initial={{ opacity: 0, y: 50, scale: 0.95 }}
//           whileInView={{ opacity: 1, y: 0, scale: 1 }}
//           transition={{ duration: 0.8, ease: "easeOut", type: "spring", stiffness: 60 }}
//           viewport={{ once: true }}
//           className="uppercase font-bold text-center text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-5xl 2xl:text-6xl 3xl:text-7xl 4k:text-8xl max-w-5xl lg:max-w-6xl xl:max-w-7xl font-montserrat px-4"
//         >
//           WHY CHOOSE LAB GROWN DIAMONDS?
//         </motion.h1>

//         <div className="mt-6 sm:mt-8">
//           <Image
//             src={volcano}
//             alt="Volcano"
//             className="object-contain max-h-[300px] sm:max-h-[400px] xl:max-h-[500px] w-full max-w-[90vw] mx-auto"
//           />
//         </div>
//       </section>

//       {/* Feature Sections */}
//       <div className="space-y-14 sm:space-y-16 xl:space-y-20 px-4 sm:px-6 xl:px-20 2xl:px-32 max-w-[1920px] mx-auto bg-black py-10 sm:py-14 xl:py-20">
//         {list.map((item, index) => (
//           <section
//             key={item.id}
//             className={`flex flex-col md:flex-row items-center justify-between gap-8 lg:gap-12 xl:gap-16 ${
//               item.imagePosition === "left" ? "md:flex-row-reverse" : ""
//             }`}
//           >
//             <motion.div
//               variants={item.imagePosition === "left" ? fadeRight : fadeLeft}
//               initial="hidden"
//               whileInView="visible"
//               viewport={{ once: false, amount: 0.3 }}
//               className="w-full md:w-1/2 flex justify-center"
//             >
//               <div className="relative w-full max-w-[480px] h-[240px] sm:h-[260px] md:h-[280px] xl:h-[300px] 2xl:h-[320px] rounded-lg overflow-hidden shadow-lg">
//                 <Image
//                   src={item.media}
//                   alt={item.title}
//                   fill
//                   style={{ objectFit: "contain" }}
//                   priority={index === 0}
//                   className="rounded-lg"
//                 />
//               </div>
//             </motion.div>

//             {/* Text */}
           
//             <motion.div
//               variants={textContainer}
//               initial="hidden"
//               whileInView="visible"
//               viewport={{ once: false, amount: 0.4 }}
//               className="w-full md:w-1/2 max-w-xl text-center md:text-left"
//             >
//               <motion.h2
//                 variants={fadeUpText}
//                 className="font-bold uppercase font-montserrat text-xl sm:text-2xl md:text-3xl xl:text-4xl text-white"
//               >
//                 {item.title}
//               </motion.h2>
//               <motion.p
//                 variants={fadeUpText}
//                 className="mt-3 sm:mt-4 text-sm sm:text-base md:text-lg text-gray-300 leading-relaxed font-montserrat"
//               >
//                 {item.description}
//               </motion.p>
//             </motion.div>
//           </section>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default WhyLabGrown;



"use client";
import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import volcano from "../../../public/images/about/volcano.png";
import img1 from "../../../public/images/about/img1.png";
import img2 from "../../../public/images/about/img2.png";
import img3 from "../../../public/images/about/img3.png";
import img4 from "../../../public/images/about/img4.png";
import img5 from "../../../public/images/about/img5.png";

const fadeLeft = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: "easeOut" } },
};

const fadeRight = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: "easeOut" } },
};

const fadeUpText = {
  hidden: { opacity: 0, y: 40 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
};

const textContainer = {
  visible: { transition: { staggerChildren: 0.15 } },
};

const list = [
  {
    id: 1,
    media: img1,
    title: "LESSER PRICE",
    description: "Mined Diamonds can trade 15 hands and travel across 3 continents before reaching the consumer...",
    imagePosition: "right",
  },
  {
    id: 2,
    media: img2,
    title: "NO CHILD LABOUR",
    description: "Children are considered cheap labour and often employed in mining...",
    imagePosition: "left",
  },
  {
    id: 3,
    media: img3,
    title: "RISE IN EMPLOYMENT",
    description: "Lab-grown diamond companies ensure secure jobs in both cutting and polishing...",
    imagePosition: "right",
  },
  {
    id: 4,
    media: img4,
    title: "ECO FRIENDLY",
    description: "Lab grown diamonds don’t require digging or excavation...",
    imagePosition: "left",
  },
  {
    id: 5,
    media: img5,
    title: "CONFLICT FREE",
    description: "Mined diamonds are often tainted by violence, poverty, and environmental harm...",
    imagePosition: "right",
  },
];

const WhyLabGrown = () => {
  return (
    <div className="bg-black text-white font-serif">
      {/* Hero Section */}
      <section className="bg-black text-white flex flex-col items-center justify-center px-4 pt-16 pb-8 sm:pt-20">
        <motion.h1
          initial={{ opacity: 0, y: 50, scale: 0.95 }}
          whileInView={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut", type: "spring", stiffness: 60 }}
          viewport={{ once: true }}
          className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-light tracking-wide text-amber-100 text-center max-w-4xl lg:max-w-5xl xl:max-w-6xl px-4"
        >
          WHY CHOOSE LAB GROWN DIAMONDS?
        </motion.h1>

        <div className="mt-4 sm:mt-6">
          <Image
            src={volcano}
            alt="Volcano"
            className="object-contain max-h-[250px] sm:max-h-[300px] md:max-h-[350px] lg:max-h-[400px] xl:max-h-[450px] w-full mx-auto"
          />
        </div>
      </section>

      {/* Feature Sections */}
      <div className="space-y-8 sm:space-y-10 xl:space-y-12 px-4 sm:px-6 xl:px-8 max-w-[1400px] mx-auto bg-black py-6 sm:py-8 xl:py-10">
        {list.map((item, index) => (
          <section
            key={item.id}
            className={`flex flex-col md:flex-row items-center justify-between gap-6 lg:gap-8 xl:gap-10 ${
              item.imagePosition === "left" ? "md:flex-row-reverse" : ""
            }`}
          >
            <motion.div
              variants={item.imagePosition === "left" ? fadeRight : fadeLeft}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.3 }}
              className="w-full md:w-1/2 flex justify-center"
            >
              <div className="relative w-full max-w-[480px] h-[200px] sm:h-[220px] md:h-[240px] lg:h-[260px] xl:h-[280px] rounded-lg overflow-hidden shadow-lg">
                <Image
                  src={item.media}
                  alt={item.title}
                  fill
                  style={{ objectFit: "contain" }}
                  priority={index === 0}
                  className="rounded-lg"
                />
              </div>
            </motion.div>

            {/* Text */}
           
            <motion.div
              variants={textContainer}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.4 }}
              className="w-full md:w-1/2 max-w-xl text-center md:text-left"
            >
              <motion.h2
                variants={fadeUpText}
                className="font-bold uppercase font-montserrat text-lg sm:text-xl md:text-2xl xl:text-3xl text-white"
              >
                {item.title}
              </motion.h2>
              <motion.p
                variants={fadeUpText}
                className="mt-2 sm:mt-3 text-sm sm:text-base md:text-base text-gray-300 leading-relaxed font-montserrat"
              >
                {item.description}
              </motion.p>
            </motion.div>
          </section>
        ))}
      </div>
    </div>
  );
};

export default WhyLabGrown;
