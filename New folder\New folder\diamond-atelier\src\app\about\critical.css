/* Critical CSS for About Page LCP Optimization */

/* Preload critical text styles to prevent CLS and improve LCP */
.lcp-text {
  font-family: var(--font-family, system-ui, -apple-system, sans-serif);
  font-display: swap;
  color: rgb(156 163 175); /* text-gray-400 */
  line-height: 1.625; /* leading-relaxed */
  text-align: center;
  min-height: 6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 56rem; /* max-w-4xl */
  margin: 0 auto;
}

/* Responsive text sizes */
@media (min-width: 768px) {
  .lcp-text {
    font-size: 1.25rem; /* text-xl */
  }
}

@media (min-width: 1024px) {
  .lcp-text {
    font-size: 1.5rem; /* text-2xl */
  }
}

/* Default mobile size */
.lcp-text {
  font-size: 1.125rem; /* text-lg */
}

/* Header styles to prevent CLS */
.lcp-header {
  font-family: var(--font-family, system-ui, -apple-system, sans-serif);
  font-display: swap;
  font-weight: 700;
  color: rgb(209 213 219); /* text-gray-300 */
  min-height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

/* Responsive header sizes */
@media (min-width: 768px) {
  .lcp-header {
    font-size: 3rem; /* text-5xl */
  }
}

@media (min-width: 1024px) {
  .lcp-header {
    font-size: 3.75rem; /* text-6xl */
  }
}

/* Default mobile header size */
.lcp-header {
  font-size: 2.25rem; /* text-4xl */
}

/* Container to prevent layout shift - REMOVED for INP optimization */
/* .lcp-container was causing INP issues with pointer events */

/* Evolution section optimization */
.evolution-item {
  min-height: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.evolution-image-container {
  width: 15rem; /* w-60 */
  height: 15rem; /* h-60 */
  margin: 0 auto 1rem auto;
  background-color: rgb(31 41 55); /* bg-gray-800 */
  border-radius: 0.75rem; /* rounded-xl */
  flex-shrink: 0;
}

.evolution-text {
  color: rgb(209 213 219); /* text-gray-300 */
  font-size: 1.125rem; /* text-lg */
  font-weight: 500;
  min-height: 2rem;
  text-align: center;
}

/* Font loading optimization */
@font-face {
  font-family: 'system-ui-fallback';
  src: local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont');
  font-display: swap;
}

/* Prevent FOUC (Flash of Unstyled Content) */
.font-loading {
  font-family: 'system-ui-fallback', system-ui, -apple-system, sans-serif;
  visibility: visible;
}

/* Smooth transitions without causing CLS */
.smooth-transition {
  transition: opacity 0.2s ease-in-out;
}

/* Layout containment for performance and INP optimization */
.contain-layout {
  contain: layout style paint;
}

/* INP Optimization - Prevent unnecessary pointer events */
.no-pointer-events {
  pointer-events: none;
}

/* Optimize for interactions */
.optimized-interaction {
  will-change: auto;
  contain: layout style paint;
  pointer-events: auto;
  touch-action: manipulation;
}

/* Reduce hover effects that can cause INP issues */
.minimal-hover:hover {
  transform: none;
  transition: none;
}

/* Fast transitions for better INP */
.fast-transition {
  transition: opacity 0.1s ease-out;
}
