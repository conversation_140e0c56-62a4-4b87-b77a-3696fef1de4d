"use client";
import React, { useState, useEffect, useRef } from 'react';

const FinalCounter = ({ targetNumber = 100, suffix = "", duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const ref = useRef(null);
  const hasRunRef = useRef(false);

  useEffect(() => {
    // Create unique key for this specific counter
    const counterKey = `final_counter_${targetNumber}_${suffix.replace(/[^a-zA-Z0-9]/g, '_')}`;
    
    // Check if this counter has already run
    const hasAlreadyRun = localStorage.getItem(counterKey);
    
    if (hasAlreadyRun) {
      // Already ran - show final number immediately
      setCount(targetNumber);
      setIsComplete(true);
      hasRunRef.current = true;
      return;
    }

    // Only set up observer if not already run
    if (!hasRunRef.current) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && !hasRunRef.current && !isComplete) {
            hasRunRef.current = true;
            setIsComplete(true);
            
            // Mark this counter as completed in localStorage
            localStorage.setItem(counterKey, 'completed');
            
            // Start animation
            const steps = 60;
            const stepValue = targetNumber / steps;
            const stepDelay = duration / steps;
            
            let currentStep = 0;
            
            const timer = setInterval(() => {
              currentStep++;
              const newCount = Math.floor(currentStep * stepValue);
              
              if (currentStep >= steps) {
                setCount(targetNumber);
                clearInterval(timer);
              } else {
                setCount(newCount);
              }
            }, stepDelay);
            
            // Disconnect observer after first use
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => {
        observer.disconnect();
      };
    }
  }, []); // Empty dependency array - run only once

  // Function to reset all counters (call this on page refresh)
  const resetAllCounters = () => {
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('final_counter_')) {
        localStorage.removeItem(key);
      }
    });
  };

  // Listen for page refresh
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Only clear on actual page refresh, not navigation
      if (performance.navigation?.type === 1) {
        resetAllCounters();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return (
    <span
      ref={ref}
      style={{
        display: 'inline-block',
        fontFamily: 'Times New Roman, serif',
        color: 'inherit',
        fontSize: 'inherit'
      }}
    >
      {count.toLocaleString()}{suffix}
    </span>
  );
};

export default FinalCounter;
